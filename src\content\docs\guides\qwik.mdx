---
title: Qwik with Yumma CSS
banner:
  content: N/A
description: Integrate Yumma CSS into Qwik applications.
slug: docs/guides/qwik
---

## Creating a new project

To create a new Qwik project, you need run the [Qwik CLI](https://qwik.dev/docs/getting-started/#create-an-app-using-the-cli) in your terminal.

<Steps>
    1. **Install Yumma CSS**

        Install `yummacss` using a package manager.

        <PackageManagers pkgManagers={["pnpm", "npm", "yarn"]} pkg="yummacss -D" title="Terminal" />

        <br />

    2. **Initialize configuration**

            Create a configuration file in your project.

            <PackageManagers pkgManagers={["pnpm", "npm", "yarn"]} pkg="yummacss init" title="Terminal" type="dlx" />

    3. **Set up configuration**

        Specify the locations of all your project files in the config file.

        ```js title="yumma.config.js" mark={2-3}
        export default {
            source: ["./src/**/*.{ts,tsx}"],
            output: "./src/global.css",
            buildOptions: {
                reset: true,
                minify: false,
            }
        };
        ```

    4. **Build styles**

        You can now start generating your CSS with the [`build`](/docs/foundations/config/#build) or [`watch`](/docs/foundations/config/#watch) command.

        <PackageManagers pkgManagers={["pnpm", "npm", "yarn"]} pkg="yummacss build" title="Terminal" type="dlx" />

    5. **Done!**

        You're all set to start using Yumma CSS utility classes in your project.

        ```tsx title="routes/index.tsx" mark={5-7}
        import { component$ } from "@builder.io/qwik";

        export default component$(() => {
            return (
                <div class="bg-indigo-12 d-g h-dvh pi-c tc-white">
                    <h1 class="fs-3xl fw-500">Yumma CSS ⚙️ Qwik</h1>
                </div>
            );
        });
        ```

</Steps>

---

## Clone this project

Skip the guide steps entirely with our Qwik starter.

```bash title="Cloning the project..."
git clone https://github.com/yumma-lib/with-qwik.git
```
