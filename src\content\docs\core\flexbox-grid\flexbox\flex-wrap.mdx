---
title: Flex Wrap
banner:
  content: N/A
description: Controls how flex items are wrapped.
slug: docs/flex-wrap
---

<Class category="flexbox" name="flex-wrap" />

### No Wrap

> Initial value

This example sets the flex wrap to **nowrap**. The items will be displayed in a single line, and they will not wrap to the next line.

```html live "fw-nw"
<div class="p-r tc-white">
  <div class="d-f fw-nw g-4 max-w-30" id="area">
    <div class="ai-c bg-indigo d-f jc-c p-4 rad-1">A</div>
    <div class="ai-c bg-indigo d-f jc-c p-4 rad-1">B</div>
    <div class="ai-c bg-indigo d-f jc-c p-4 rad-1">C</div>
  </div>
</div>
```

### Wrap

This example sets the flex wrap to **wrap**. The items will wrap onto multiple lines if there is not enough space in the container.

```html live "fw-w"
<div class="p-r tc-white">
  <div class="d-f fw-w g-4 max-w-30" id="area">
    <div class="ai-c bg-indigo d-f jc-c p-4 rad-1">A</div>
    <div class="ai-c bg-indigo d-f jc-c p-4 rad-1">B</div>
    <div class="ai-c bg-indigo d-f jc-c p-4 rad-1">C</div>
  </div>
</div>
```

### Wrap Reverse

This example sets the flex wrap to **wrap-reverse**. The items will wrap onto multiple lines, but the order of the lines will be reversed.

```html live "fw-wr"
<div class="p-r tc-white">
  <div class="d-f fw-wr g-4 max-w-30" id="area">
    <div class="ai-c bg-indigo d-f jc-c p-4 rad-1">A</div>
    <div class="ai-c bg-indigo d-f jc-c p-4 rad-1">B</div>
    <div class="ai-c bg-indigo d-f jc-c p-4 rad-1">C</div>
  </div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<MediaModifier class="fw-wr" mediaModifier="fw-w" classPrefix="fw">
  ### Media modifier
</MediaModifier>

<HoverModifier class="fw-wr" hoverModifier="fw-w" classPrefix="fw">
  ### Hover modifier
</HoverModifier>
