---
title: Grid Auto Columns
banner:
  content: N/A
description: Controls the size of grid columns created implicitly.
slug: docs/grid-auto-columns
---

<Class category="grid" name="grid-auto-columns" />

## Auto

> Initial value

This example sets the grid auto columns to **auto**. The columns will size themselves based on the content and available space in the grid container.

```html live "gac-auto"
<div class="d-g gac-auto g-4 gtr-auto">
  <div class="ai-c bg-indigo d-f jc-c p-4 rad-1 tc-white">Sphinx of black quartz, judge my vow.</div>
  <div class="ai-c bg-indigo-8 d-f jc-c p-4 rad-1 tc-indigo-5">B</div>
  <div class="ai-c bg-indigo-8 d-f jc-c p-4 rad-1 tc-indigo-5">C</div>
</div>
```

## Min Content

This example sets the grid auto columns to **min-content**. The columns will size themselves to the smallest possible width that does not cause overflow.

```html live "gac-min"
<div class="d-g gac-min g-4 gtr-auto">
  <div class="ai-c bg-indigo d-f jc-c p-4 rad-1 tc-white">Sphinx of black quartz, judge my vow.</div>
  <div class="ai-c bg-indigo-8 d-f jc-c p-4 rad-1 tc-indigo-5">B</div>
  <div class="ai-c bg-indigo-8 d-f jc-c p-4 rad-1 tc-indigo-5">C</div>
</div>
```

## Max Content

This example sets the grid auto columns to **max-content**. The columns will size themselves to the largest possible width based on their content.

```html live "gac-max"
<div class="d-g gac-max g-4 gtr-auto">
  <div class="ai-c bg-indigo d-f jc-c p-4 rad-1 tc-white">Sphinx of black quartz, judge my vow.</div>
  <div class="ai-c bg-indigo-8 d-f jc-c p-4 rad-1 tc-indigo-5">B</div>
  <div class="ai-c bg-indigo-8 d-f jc-c p-4 rad-1 tc-indigo-5">C</div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<MediaModifier class="gac-min" mediaModifier="gac-auto" classPrefix="gac">
  ### Breakpoint modifier
</MediaModifier>

<HoverModifier class="gac-min" hoverModifier="gac-auto" classPrefix="gac">
  ### Hover modifier
</HoverModifier>
