---
title: Flex Basis
banner:
  content: N/A
description: Controls the initial size of flex elements.
slug: docs/flex-basis
---

<Class category="flexbox" name="flex-basis" />

This example showcases various `flex-basis` utilities:

- The **fb-0** flex basis utility sets the flex-basis property to **0rem**, allowing the item to have no initial size.
- The **fb-50** flex basis utility sets the flex-basis property to **12.5rem**, giving the item a base size of **12.5rem**.
- Finally, **fb-full** flex basis utility sets the flex-basis property to **100%**, allowing the item to take up the full width of its container.

```html live "fb-0" "fb-50" "fb-auto" "fb-full" layout="/src/layouts/inline.astro"
<div class="tc-white" id="area">
  <div class="d-f g-4 h-24 w-full">
    <div class="ai-c bg-indigo d-f fb-0 jc-c p-4 rad-1">A</div>
    <div class="ai-c bg-indigo d-f fb-50 jc-c p-4 rad-1">B</div>
    <div class="ai-c bg-indigo d-f fb-full jc-c p-4 rad-1">C</div>
  </div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<BreakpointVariant class="fb-1" variant="fb-2" classPrefix="fb">
  ### Breakpoint variant
</BreakpointVariant>

<HoverVariant class="fb-1" variant="fb-2" classPrefix="fb">
  ### Hover variant
</HoverVariant>
