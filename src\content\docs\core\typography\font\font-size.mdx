---
title: Font Size
banner:
  content: N/A
description: Controls the font size of an element.
slug: docs/font-size
---

<Class category="font" name="font-size" />

## Extra Small

This example sets the font size to **0.75rem**. This size is typically used for smaller text elements.

```html live "fs-xs" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-xs p-4 rad-1 ta-c tc-lead">Aa</p>
</div>
```

## Small

This example sets the font size to **0.875rem**. This size is often used for slightly larger text elements.

```html live "fs-sm" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-sm p-4 rad-1 ta-c tc-lead">Aa</p>
</div>
```

## Medium

> Initial value

This example sets the font size to **1rem**. This is the standard size for body text.

```html live "fs-md" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-md p-4 rad-1 ta-c tc-lead">Aa</p>
</div>
```

## Large

This example sets the font size to **1.125rem**. This size is often used for prominent headings.

```html live "fs-lg" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg p-4 rad-1 ta-c tc-lead">Aa</p>
</div>
```

## Extra Large

This example sets the font size to **1.25rem**. This size is typically used for major headings or titles.

```html live "fs-xl" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-xl p-4 rad-1 ta-c tc-lead">Aa</p>
</div>
```

## XXL

This example sets the font size to **1.5rem**. This size is often used for very prominent headings.

```html live "fs-xxl" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-xxl p-4 rad-1 ta-c tc-lead">Aa</p>
</div>
```

## 3XL

This example sets the font size to **1.875rem**. This size is typically used for the largest headings.

```html live "fs-3xl" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-3xl p-4 rad-1 ta-c tc-lead">Aa</p>
</div>
```

## 4XL

This example sets the font size to **2.25rem**. This size is often used for the most prominent headings.

```html live "fs-4xl" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-4xl p-4 rad-1 ta-c tc-lead">Aa</p>
</div>
```

## 5XL

This example sets the font size to **3rem**. This size is typically used for the most prominent text elements.

```html live "fs-5xl" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-5xl p-4 rad-1 ta-c tc-lead">Aa</p>
</div>
```

## 6XL

This example sets the font size to **3.75rem**. This size is often used for the most prominent text elements.

```html live "fs-6xl" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-6xl p-4 rad-1 ta-c tc-lead">Aa</p>
</div>
```

## 7XL

This example sets the font size to **4.5rem**. This size is typically used for the largest display text.

```html live "fs-7xl" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-7xl p-4 rad-1 ta-c tc-lead">Aa</p>
</div>
```

## 8XL

This example sets the font size to **6rem**. This size is typically used for the largest display text.

```html live "fs-8xl" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-8xl p-4 rad-1 ta-c tc-lead">Aa</p>
</div>
```

## 9XL

This example sets the font size to **8rem**. This size is typically used for the largest display text.

```html live "fs-9xl" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-9xl p-4 rad-1 ta-c tc-lead">Aa</p>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/foundations/responsive-design/) or other factors, such as [hover states](/docs/foundations/variants/).

<BreakpointVariant class="fs-md" variant="fs-lg" classPrefix="fs">
  ### Breakpoint variant
</BreakpointVariant>

<HoverVariant class="fs-md" variant="fs-lg" classPrefix="fs">
  ### Hover variant
</HoverVariant>
