---
title: Align Items
banner:
  content: N/A
description: Controls flex and grid positioning along a container's transverse axis.
slug: docs/align-items
---

<Class category="flexbox" name="align-items" />

## Baseline

This example sets the align items to **baseline**. The items will be aligned along the baseline of the container.

```html live "ai-b" layout="/src/layouts/inline.astro"
<div class="ai-b d-g g-4 gtc-3 h-64 tc-white ta-c w-full" id="area">
  <div class="bg-indigo p-4 rad-1">A</div>
  <div class="bg-indigo p-4 rad-1">B</div>
  <div class="bg-indigo p-4 rad-1">C</div>
  <div class="bg-indigo p-4 rad-1">D</div>
  <div class="bg-indigo p-4 rad-1">E</div>
</div>
```

## Center

This example sets the align items to **center**. The items will be centered within the container.

```html live "ai-c" layout="/src/layouts/inline.astro"
<div class="ai-c d-g g-4 gtc-3 h-64 tc-white ta-c w-full" id="area">
  <div class="bg-indigo p-4 rad-1">A</div>
  <div class="bg-indigo p-4 rad-1">B</div>
  <div class="bg-indigo p-4 rad-1">C</div>
  <div class="bg-indigo p-4 rad-1">D</div>
  <div class="bg-indigo p-4 rad-1">E</div>
</div>
```

## Flex End

This example sets the align items to **flex-end**. The items will be aligned to the end of the container.

```html live "ai-fe" layout="/src/layouts/inline.astro"
<div class="ai-fe d-g g-4 gtc-3 h-64 tc-white ta-c w-full" id="area">
  <div class="bg-indigo p-4 rad-1">A</div>
  <div class="bg-indigo p-4 rad-1">B</div>
  <div class="bg-indigo p-4 rad-1">C</div>
  <div class="bg-indigo p-4 rad-1">D</div>
  <div class="bg-indigo p-4 rad-1">E</div>
</div>
```

## Flex Start

This example sets the align items to **flex-start**. The items will be aligned to the start of the container.

```html live "ai-fs" layout="/src/layouts/inline.astro"
<div class="ai-fs d-g g-4 gtc-3 h-64 tc-white ta-c w-full" id="area">
  <div class="bg-indigo p-4 rad-1">A</div>
  <div class="bg-indigo p-4 rad-1">B</div>
  <div class="bg-indigo p-4 rad-1">C</div>
  <div class="bg-indigo p-4 rad-1">D</div>
  <div class="bg-indigo p-4 rad-1">E</div>
</div>
```

## Stretch

This example sets the align items to **stretch**. The items will stretch to fill the container along the cross axis.

```html live "ai-s" layout="/src/layouts/inline.astro"
<div class="ai-s d-g g-4 gtc-3 h-64 tc-white ta-c w-full" id="area">
  <div class="ai-c bg-indigo d-f jc-c p-4 rad-1">A</div>
  <div class="ai-c bg-indigo d-f jc-c p-4 rad-1">B</div>
  <div class="ai-c bg-indigo d-f jc-c p-4 rad-1">C</div>
  <div class="ai-c bg-indigo d-f jc-c p-4 rad-1">D</div>
  <div class="ai-c bg-indigo d-f jc-c p-4 rad-1">E</div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<MediaModifier class="ai-s" mediaModifier="ai-e" classPrefix="ai">
  ### Breakpoint modifier
</MediaModifier>

<HoverModifier class="ai-s" hoverModifier="ai-e" classPrefix="ai">
  ### Hover modifier
</HoverModifier>
