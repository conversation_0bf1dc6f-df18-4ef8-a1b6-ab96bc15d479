---
title: Text Decoration Style
banner:
  content: N/A
description: Controls the style of text decoration.
slug: docs/text-decoration-style
---

<Class category="text" name="text-decoration-style" />

## Dashed

This example sets the text decoration style to **dashed**. The text will be displayed with a dashed line for its decoration.

```html live "tds-d" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg p-4 rad-1 ta-c tc-lead tdl-u tds-d">Sphinx of black quartz, judge my vow.</p>
</div>
```

## Solid

> Initial value

This example sets the text decoration style to **solid**. The text will be displayed with a solid line for its decoration.

```html live "tds-s" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg p-4 rad-1 ta-c tc-lead tdl-u tds-s">Sphinx of black quartz, judge my vow.</p>
</div>
```

## Wavy

This example sets the text decoration style to **wavy**. The text will be displayed with a wavy line for its decoration.

```html live "tds-w" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg p-4 rad-1 ta-c tc-lead tdl-u tds-w">Sphinx of black quartz, judge my vow.</p>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/foundations/responsive-design/) or other factors, such as [hover states](/docs/foundations/variants/).

<BreakpointVariant class="tds-none" variant="tds-s" classPrefix="tds">
  ### Breakpoint variant
</BreakpointVariant>

<HoverVariant class="tds-none" variant="tds-s" classPrefix="tds">
  ### Hover variant
</HoverVariant>
