---
title: Line Height
banner:
  content: N/A
description: Controls the line height of an element.
slug: docs/line-height
---

<Class category="text" name="line-height" />

## 1

This example sets the line height to **1**. The line height is equal to the font size, resulting in single spacing between lines.

```html live "lh-1" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg lh-1 p-4 rad-1 ta-c tc-lead">The pike eats the owl and then lazes in the sun under the water. Who would eat a big pike?</p>
</div>
```

## 2

This example sets the line height to **1.25**. The line height is 1.25 times the font size, providing a bit of extra space between lines for improved readability.

```html live "lh-2" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg lh-2 p-4 rad-1 ta-c tc-lead">The pike eats the owl and then lazes in the sun under the water. Who would eat a big pike?</p>
</div>
```

## 3

This example sets the line height to **1.375**. The line height is 1.375 times the font size, offering a comfortable amount of spacing between lines.

```html live "lh-3" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg lh-3 p-4 rad-1 ta-c tc-lead">The pike eats the owl and then lazes in the sun under the water. Who would eat a big pike?</p>
</div>
```

## 4

This example sets the line height to **1.5**. The line height is 1.5 times the font size, which is commonly used for body text to enhance readability.

```html live "lh-4" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg lh-4 p-4 rad-1 ta-c tc-lead">The pike eats the owl and then lazes in the sun under the water. Who would eat a big pike?</p>
</div>
```

## 5

This example sets the line height to **1.625**. The line height is 1.625 times the font size, providing a more spacious feel for the text.

```html live "lh-5" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg lh-5 p-4 rad-1 ta-c tc-lead">The pike eats the owl and then lazes in the sun under the water. Who would eat a big pike?</p>
</div>
```

## 6

This example sets the line height to **2**. The line height is double the font size, creating a very open and airy layout for the text.

```html live "lh-6" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg lh-6 p-4 rad-1 ta-c tc-lead">The pike eats the owl and then lazes in the sun under the water. Who would eat a big pike?</p>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/foundations/responsive-design/) or other factors, such as [hover states](/docs/foundations/variants/).

<BreakpointVariant class="lh-1" variant="lh-2" classPrefix="lh">
  ### Breakpoint variant
</BreakpointVariant>

<HoverVariant class="lh-1" variant="lh-2" classPrefix="lh">
  ### Hover variant
</HoverVariant>
