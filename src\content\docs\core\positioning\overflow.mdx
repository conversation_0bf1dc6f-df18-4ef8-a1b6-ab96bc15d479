---
title: Overflow
banner:
  content: N/A
description: Controls how an element behaves when content overflows.
slug: docs/overflow
---

<Class category="positioning" name="overflow" />

## Auto

This example sets the overflow to **auto**. The element will add scrollbars if the content overflows the container.

<Note icon="cursor_arrow">Try scrolling through the container to see how the overflow behaves.</Note>

```html live "o-auto" layout="/src/layouts/inline.astro"
<div class="bg-indigo-2 h-32 o-auto p-4">
  <div class="bg-indigo h-64 p-4 tc-white">This content overflows the container.</div>
</div>
```

## Clip

This example sets the overflow to **clip**. The content that overflows the container will be clipped and not visible.

```html live "o-c" layout="/src/layouts/inline.astro"
<div class="bg-indigo-2 h-32 o-c p-4">
  <div class="bg-indigo h-64 p-4 tc-white">This content is clipped and cannot be scrolled.</div>
</div>
```

## Hidden

This example sets the overflow to **hidden**. The content that overflows the container will be hidden and not visible, without scrollbars.

```html live "o-h" layout="/src/layouts/inline.astro"
<div class="bg-indigo-2 h-32 o-h p-4">
  <div class="bg-indigo h-64 p-4 tc-white">This content is hidden when it overflows.</div>
</div>
```

## Scroll

This example sets the overflow to **scroll**. The element will always show scrollbars, regardless of whether the content overflows.

```html live "o-s" layout="/src/layouts/inline.astro"
<div class="bg-indigo-2 h-32 o-s p-4">
  <div class="bg-indigo h-64 p-4 tc-white">This content overflows the container, and scrollbars are always visible.</div>
</div>
```

## Visible

> Initial value

This example sets the overflow to **visible**. The content will overflow the container and be visible outside of it.

```html live "o-v" layout="/src/layouts/inline.astro"
<div class="bg-indigo-2 h-32 o-v p-4">
  <div class="bg-indigo h-64 p-4 tc-white">This content overflows the container and is fully visible.</div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<BreakpointVariant class="o-h" variant="o-auto" classPrefix="o">
  ### Breakpoint variant
</BreakpointVariant>

<HoverVariant class="o-h" variant="o-auto" classPrefix="o">
  ### Hover variant
</HoverVariant>
