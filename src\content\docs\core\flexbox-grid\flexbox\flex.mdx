---
title: Flex
banner:
  content: N/A
description: Controls how flex elements grow and shrink.
slug: docs/flex
---

<Class category="flexbox" name="flex" />

This example showcases various `flex` utilities:

- The **f-none** flex utility sets the flex property to **none**, preventing the item from growing or shrinking.
- The **f-1** flex utility sets the flex property to **1 1 0%**, allowing the item to grow and shrink equally with a base size of **0**.
- Finally, **f-auto** flex utility sets the flex property to **1 1 auto**, allowing the item to grow and shrink with a base size determined by its content.

```html live "f-none" "f-1" "f-auto" layout="/src/layouts/inline.astro"
<div class="tc-white" id="area">
  <div class="d-f g-4 h-24 w-full">
    <div class="ai-c bg-indigo d-f f-none jc-c p-4 rad-1">A</div>
    <div class="ai-c bg-indigo d-f f-1 jc-c p-4 rad-1">B</div>
    <div class="ai-c bg-indigo d-f f-auto jc-c p-4 rad-1">C</div>
  </div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/foundations/responsive-design/) or other factors, such as [hover states](/docs/foundations/variants/).

<BreakpointVariant class="f-1" variant="f-2" classPrefix="f">
  ### Breakpoint variant
</BreakpointVariant>

<HoverVariant class="f-1" variant="f-2" classPrefix="f">
  ### Hover variant
</HoverVariant>
