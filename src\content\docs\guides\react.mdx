---
title: React with Yumma CSS
banner:
  content: N/A
description: Integrate Yumma CSS into React applications.
slug: docs/guides/react
---

## Creating a new project

To create a new React project, you need run the [React command](https://react.dev/learn/build-a-react-app-from-scratch#vite) in your terminal.

<Steps>

    1. **Install Yumma CSS**

        Install `yummacss` using a package manager.

        <PackageManagers pkgManagers={["pnpm", "npm", "yarn"]} pkg="yummacss -D" title="Terminal" />

        <br />

    2. **Initialize configuration**

            Create a configuration file in your project.

            <PackageManagers pkgManagers={["pnpm", "npm", "yarn"]} pkg="yummacss init" title="Terminal" type="dlx" />

    3. **Set up configuration**

        Specify the locations of all your project files in the config file.

        ```js title="yumma.config.js" mark={2-3}
        export default {
            source: ["./src/**/*.{ts,tsx}"],
            output: "./src/index.css",
            buildOptions: {
                reset: true,
                minify: false,
            }
        };
        ```

    4. **Build styles**

        You can now start generating your CSS with the [`build`](/docs/foundations/config/#build) or [`watch`](/docs/foundations/config/#watch) command.

        <PackageManagers pkgManagers={["pnpm", "npm", "yarn"]} pkg="yummacss build" title="Terminal" type="dlx" />

    5. **Done!**

        You're all set to start using Yumma CSS utility classes in your project.

        ```tsx title="App.tsx" mark={3-5}
        function App() {
            return (
                <div className="bg-indigo-12 d-g h-dvh pi-c tc-white">
                    <h1 className="fs-3xl fw-500">Yumma CSS ⚙️ React</h1>
                </div>
            );
        }

        export default App;
        ```

</Steps>

---

## Clone this project

Skip the guide steps entirely with our React starter.

```bash title="Cloning the project..."
git clone https://github.com/yumma-lib/with-react.git
```
