---
title: Background Clip
banner:
  content: N/A
description: Controls the box around the background of an element.
slug: docs/background-clip
---

<Class category="background" name="background-clip" />

## Border Box

> Initial value

This example sets the background to **border-box**. The background extends to the outer edge of the border (including the border itself).

```html live "bc-bb"
<div class="b-3 b-d bc-bb bc-indigo-8 bg-indigo d-20 p-2 rad-1"></div>
```

## Content Box

This example sets the background to **content-box**. The background is confined to the content area, excluding padding and borders.

```html live "bc-cb"
<div class="b-3 b-d bc-cb bc-indigo-8 bg-indigo d-20 p-2 rad-1"></div>
```

## Padding Box

This example sets the background to **padding-box**. The background extends to the outer edge of the padding, but does not include the border.

```html live "bc-pb"
<div class="b-3 b-d bc-indigo-8 bc-pb bg-indigo d-20 p-2 rad-1"></div>
```

## Text

This example sets the background to **text**. The background is clipped to the text itself, allowing for unique visual effects.

```html live "bc-t"
<div class="bc-t fs-3xl fw-700 o-h px-4 py-2 rad-2 tc-transparent" style="background-image: linear-gradient(132deg, #413cb8 0.00%, #7a77cd 100.00%);">
  Hi, Anne. It's finally springtime
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<MediaModifier class="bc-cb" mediaModifier="bc-bb" classPrefix="bc">
  ### Breakpoint modifier
</MediaModifier>

<HoverModifier class="bc-cb" hoverModifier="bc-bb" classPrefix="bc">
  ### Hover modifier
</HoverModifier>
