---
title: Installation
banner:
  content: N/A
description: Yumma CSS is a CSS framework packed with a set of abbreviated utility classes for building faster and more maintainable UIs.
slug: docs/installation
---

## Installation guide

Yumma CSS comes with the Yumma CLI, a powerful tool that's a breeze to set up and takes no time or effort for production-ready projects.

<Steps>
  1. **Install Yumma CSS**

      Add `yummacss` to your project.

      <PackageManagers pkgManagers={["pnpm", "npm", "yarn"]} pkg="yummacss -D" title="Terminal" />

      <br />

2. **Initialize configuration**

   Create a configuration file in your project.

   <PackageManagers pkgManagers={["pnpm", "npm", "yarn"]} pkg="yummacss init" title="Terminal" type="dlx" />

3. **Set up configuration**

   Specify the locations of all your project files in the config file.

   ```js title="yumma.config.js" mark={2-3}
   export default {
     source: ["./src/**/*.{ts,tsx}"],
     output: "./src/styles.css",
     buildOptions: {
       reset: true,
       minify: false,
     },
   };
   ```

4. **Build styles**

   You can now start generating your CSS with the [`build`](/docs/first-steps#build-command) or [`watch`](/docs/first-steps#watch-command) command.

   <PackageManagers pkgManagers={["pnpm", "npm", "yarn"]} pkg="yummacss build" title="Terminal" type="dlx" />

</Steps>

## Framework guides

Yumma CSS works with any framework, seriously! Try it out with your favorite one.

<div class="d-g gtc-4 not-content g-4">
  <a href="/docs/guides/angular" class="d-f tc-accent">
    <Note icon="angular" />
    Angular
  </a>
  <a href="/docs/guides/astro" class="d-f tc-accent">
    <Note icon="astro" />
    Astro
  </a>
  <a href="/docs/guides/nextjs" class="d-f tc-accent">
    <Note icon="nextjs" />
    Next.js
  </a>
  <a href="/docs/guides/nuxtjs" class="d-f tc-accent">
    <Note icon="nuxtjs" />
    Nuxt.js
  </a>
  <a href="/docs/guides/qwik" class="d-f tc-accent">
    <Note icon="qwik" />
    Qwik
  </a>
  <a href="/docs/guides/preact" class="d-f tc-accent">
    <Note icon="preact" />
    Preact
  </a>
  <a href="/docs/guides/react" class="d-f tc-accent">
    <Note icon="react" />
    React
  </a>
  <a href="/docs/guides/solidjs" class="d-f tc-accent">
    <Note icon="solidjs" />
    Solid.js
  </a>
  <a href="/docs/guides/svelte" class="d-f tc-accent">
    <Note icon="svelte" />
    Svelte
  </a>
  <a href="/docs/guides/vuejs" class="d-f tc-accent">
    <Note icon="vuejs" />
    Vue.js
  </a>
</div>

## Next steps

Take your first steps toward mastering Yumma CSS and its incredible core features.

<Note icon="input"> **Styling elements**: Learn how to style elements and styling patterns in ["Styling elements"](/docs/first-steps#styling-elements).</Note>

<Note icon="mixer_horizontal"> **Conditional styles**: Discover how to apply styles conditionally in ["Conditional styles"](/docs/first-steps#conditional-styles).</Note>

<Note icon="crumpled_paper"> **Building styles**: Understand the build process and commands in ["Building styles"](/docs/first-steps#building-styles).</Note>

<Note icon="rocket"> **Production mode**: Optimize your CSS for production in ["Production mode"](/docs/first-steps#buildoptionsminify).</Note>

<Note icon="color_wheel"> **Color system**: Explore the comprehensive color system in ["Color system"](/docs/first-steps#color-system).</Note>
