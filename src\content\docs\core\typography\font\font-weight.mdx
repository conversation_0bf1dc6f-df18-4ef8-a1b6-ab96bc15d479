---
title: Font Weight
banner:
  content: N/A
description: Controls the font weight of an element.
slug: docs/font-weight
---

<Class category="font" name="font-weight" />

## 100

This example sets the font weight to **100**. This weight is very light and is typically used for fine text.

```html live "fw-100" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg fw-100 p-4 rad-1 ta-c tc-lead">Sphinx of black quartz, judge my vow.</p>
</div>
```

## 200

This example sets the font weight to **200**. This weight is light and is often used for subtle emphasis.

```html live "fw-200" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg fw-200 p-4 rad-1 ta-c tc-lead">Sphinx of black quartz, judge my vow.</p>
</div>
```

## 300

This example sets the font weight to **300**. This weight is slightly bolder than light and is used for emphasis.

```html live "fw-300" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg fw-300 p-4 rad-1 ta-c tc-lead">Sphinx of black quartz, judge my vow.</p>
</div>
```

## 400

> Initial value

This example sets the font weight to **400**. This is the normal weight for body text.

```html live "fw-400" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg fw-400 p-4 rad-1 ta-c tc-lead">Sphinx of black quartz, judge my vow.</p>
</div>
```

## 500

This example sets the font weight to **500**. This weight is medium and is often used for headings or important text.

```html live "fw-500" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg fw-500 p-4 rad-1 ta-c tc-lead">Sphinx of black quartz, judge my vow.</p>
</div>
```

## 600

This example sets the font weight to **600**. This weight is semi-bold and is typically used for prominent headings.

<Note icon="heart">Stylecent uses the value `600` for all headings. Learn more about it in the [docs](/docs/stylecent#typography).</Note>

```html live "fw-600" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg fw-600 p-4 rad-1 ta-c tc-lead">Sphinx of black quartz, judge my vow.</p>
</div>
```

## 700

This example sets the font weight to **700**. This weight is bold and is often used for major headings.

<Note icon="heart">Stylecent uses the value `700` for elements like `<b>` and `<strong>`. Learn more about it in the [docs](/docs/stylecent#typography).</Note>

```html live "fw-700" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg fw-700 p-4 rad-1 ta-c tc-lead">Sphinx of black quartz, judge my vow.</p>
</div>
```

## 800

This example sets the font weight to **800**. This weight is extra bold and is typically used for very prominent text.

```html live "fw-800" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg fw-800 p-4 rad-1 ta-c tc-lead">Sphinx of black quartz, judge my vow.</p>
</div>
```

## 900

This example sets the font weight to **900**. This weight is the heaviest and is often used for the most impactful text.

```html live "fw-900" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg fw-900 p-4 rad-1 ta-c tc-lead">Sphinx of black quartz, judge my vow.</p>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<MediaModifier class="fw-500" mediaModifier="fw-600" classPrefix="fw">
  ### Breakpoint modifier
</MediaModifier>

<HoverModifier class="fw-500" hoverModifier="fw-600" classPrefix="fw">
  ### Hover modifier
</HoverModifier>
