---
title: Next.js with Yumma CSS
banner:
  content: N/A
description: Integrate Yumma CSS into Next.js applications.
slug: docs/guides/nextjs
---

## Creating a new project

To create a new Next.js project, you need run the [create next app command](https://nextjs.org/docs/app/getting-started/installation) in your terminal.

<Steps>

    1. **Install Yumma CSS**

        Install `yummacss` using a package manager.

        <PackageManagers pkgManagers={["pnpm", "npm", "yarn"]} pkg="yummacss -D" title="Terminal" />

    2. **Initialize configuration**

            Create a configuration file in your project.

            <PackageManagers pkgManagers={["pnpm", "npm", "yarn"]} pkg="yummacss init" title="Terminal" type="dlx" />

    3. **Set up configuration**

        Specify the locations of all your project files in the config file.

        ```js title="yumma.config.js" mark={2-3}
        module.exports = {
            source: ["./app/**/*.{ts,tsx}"],
            output: "./app/globals.css",
            buildOptions: {
                reset: true,
                minify: false,
            }
        };
        ```

    4. **Build styles**

        You can now start generating your CSS with the [`build`](/docs/first-steps#build-command) or [`watch`](/docs/first-steps#watch-command) command.

        <PackageManagers pkgManagers={["pnpm", "npm", "yarn"]} pkg="yummacss build" title="Terminal" type="dlx" />

    5. **Done!**

        You're all set to start using Yumma CSS utility classes in your project.

        ```tsx title="page.tsx" mark={3}
        export default function Home() {
            return (
                <div className="bg-indigo-12 d-g h-dvh pi-c tc-white">
                    <h1 className="fs-3xl fw-500">Yumma CSS ⚙️ Next.js</h1>
                </div>
            );
        }
        ```

</Steps>

---

## Clone this project

Skip the guide steps entirely with our Next.js starter.

```bash title="Cloning the project..."
git clone https://github.com/yumma-lib/with-nextjs.git
```
