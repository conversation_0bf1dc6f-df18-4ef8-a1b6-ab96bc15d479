---
title: Box Shadow
banner:
  content: N/A
description: Controls the box shadow of an element.
slug: docs/box-shadow
---

<Class category="effect" name="box-shadow" />

This example showcases various `box-shadow` utilities:

- The **bs-xs** box shadow utility applies an extra small shadow to an element.
- The **bs-sm** box shadow utility applies a small shadow to an element.
- The **bs-md** box shadow utility applies a medium shadow to an element.
- Finally, **bs-lg** box shadow utility applies a large to an element.

```html live "bs-xs" "bs-sm" "bs-md" "bs-lg" layout="/src/layouts/lightOnly.astro"
<div class="d-g g-16 gtc-1 sm:gtc-4">
  <div class="ai-c bg-white bs-xs d-f d-18 jc-c rad-1 tc-lead">xs</div>
  <div class="ai-c bg-white bs-sm d-f d-18 jc-c rad-1 tc-lead">sm</div>
  <div class="ai-c bg-white bs-md d-f d-18 jc-c rad-1 tc-lead">md</div>
  <div class="ai-c bg-white bs-lg d-f d-18 jc-c rad-1 tc-lead">lg</div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<MediaModifier class="bs-sm" mediaModifier="bs-md" classPrefix="bs">
  ### Breakpoint modifier
</MediaModifier>

<HoverModifier class="bs-sm" hoverModifier="bs-md" classPrefix="bs">
  ### Hover modifier
</HoverModifier>
