---
title: Bottom / Left / Right / Top
banner:
  content: N/A
description: Controls placement of positioned elements.
slug: docs/bottom-left-right-top
---

## Bottom

Controls the bottom placement of positioned elements.

<Class category="positioning" name="bottom" />

```html live "bo-0"
<div class="p-r d-18 rad-2 sm:d-32 tc-white">
  <div class="p-a i-0 rad-2 bo-1" id="area"></div>
  <div class="p-a bo-0 l-0 d-f d-12 ai-c jc-c rad-2 bg-indigo p-4">A</div>
</div>
```

## Inset

Controls the placement of positioned elements in all directions.

<Class category="positioning" name="inset" />

```html live "i-0"
<div class="p-r d-18 rad-2 sm:d-32 tc-white">
  <div class="p-a i-0 rad-2 bo-1" id="area"></div>
  <div class="p-a i-0 d-f ai-c jc-c rad-2 bg-indigo p-4">A</div>
</div>
```

## Inset X

Controls the placement of positioned elements in the horizontal direction.

<Class category="positioning" name="inset-x" />

```html live "ix-0"
<div class="p-r d-18 rad-2 sm:d-32 tc-white">
  <div class="p-a i-0 rad-2 bo-1" id="area"></div>
  <div class="p-a t-0 ix-0 d-f h-12 ai-c jc-c rad-2 bg-indigo p-4">A</div>
</div>
```

## Inset Y

Controls the placement of positioned elements in the vertical direction.

<Class category="positioning" name="inset-y" />

```html live "iy-0"
<div class="p-r d-18 rad-2 sm:d-32 tc-white">
  <div class="p-a i-0 rad-2 bo-1" id="area"></div>
  <div class="p-a iy-0 l-0 d-f w-12 ai-c jc-c rad-2 bg-indigo p-4">A</div>
</div>
```

## Left

Controls the **left** placement of positioned elements.

<Class category="positioning" name="left" />

```html live "l-0"
<div class="p-r d-18 rad-2 sm:d-32 tc-white">
  <div class="p-a i-0 rad-2 bo-1" id="area"></div>
  <div class="p-a t-0 l-0 d-f d-12 ai-c jc-c rad-2 bg-indigo p-4">A</div>
</div>
```

## Right

Controls the **right** placement of positioned elements.

<Class category="positioning" name="right" />

```html live "r-0"
<div class="p-r d-18 rad-2 sm:d-32 tc-white">
  <div class="p-a i-0 rad-2 bo-1" id="area"></div>
  <div class="p-a bo-0 r-0 d-f d-12 ai-c jc-c rad-2 bg-indigo p-4">A</div>
</div>
```

## Top

Controls the **top** placement of positioned elements.

<Class category="positioning" name="top" />

```html live "t-0"
<div class="p-r d-18 rad-2 sm:d-32 tc-white">
  <div class="p-a i-0 rad-2 bo-1" id="area"></div>
  <div class="p-a t-0 r-0 d-f d-12 ai-c jc-c rad-2 bg-indigo p-4">A</div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<MediaModifier class="l-0" mediaModifier="r-0" classPrefix="l">
  ### Media modifier
</MediaModifier>

<HoverModifier class="l-0" hoverModifier="r-0" classPrefix="l">
  ### Hover modifier
</HoverModifier>
