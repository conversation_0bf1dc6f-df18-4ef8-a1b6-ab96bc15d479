---
title: Outline Width
banner:
  content: N/A
description: Controls the width of the outline of an element.
slug: docs/outline-width
---

<Class category="outline" name="outline-width" />

This example showcases various `outline-width` utilities:

- The **1px** outline width utility creates a thin outline.
- The **2px** outline width utility creates a medium outline.
- The **3px** outline width utility creates a thick outline.

```html live "ow-1" "ow-2" "ow-3"
<div class="d-g g-16 gtc-1 sm:gtc-3">
  <div class="b-1 bc-indigo d-16 oc-indigo oo-1 os-s ow-1 rad-1"></div>
  <div class="b-1 bc-indigo d-16 oc-indigo oo-1 os-s ow-2 rad-1"></div>
  <div class="b-1 bc-indigo d-16 oc-indigo oo-1 os-s ow-3 rad-1"></div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<MediaModifier class="ow-1" mediaModifier="ow-2" classPrefix="ow">
  ### Media modifier
</MediaModifier>

<HoverModifier class="ow-1" hoverModifier="ow-2" classPrefix="ow">
  ### Hover modifier
</HoverModifier>
