---
title: Grid Auto Rows
banner:
  content: N/A
description: Controls the size of grid lines created implicitly.
slug: docs/grid-auto-rows
---

<Class category="grid" name="grid-auto-rows" />

## Auto

> Initial value

This example sets the grid auto rows to **auto**. The rows will size themselves based on the content and available space in the grid container.

```html live "gar-auto" layout="/src/layouts/inline.astro"
<div class="o-auto rad-1">
  <div class="d-g gar-auto gtc-auto g-4">
    <div class="ai-c bg-indigo d-f jc-c p-4 rad-1 tc-white">Pneumonoultramicroscopicsilicovolcanoconiosis</div>
    <div class="ai-c bg-indigo-8 d-f jc-c p-4 rad-1 tc-indigo-5">B</div>
    <div class="ai-c bg-indigo-8 d-f jc-c p-4 rad-1 tc-indigo-5">C</div>
  </div>
</div>
```

## Min Content

This example sets the grid auto rows to **min-content**. The rows will size themselves to the smallest possible height that does not cause overflow.

```html live "gar-min" layout="/src/layouts/inline.astro"
<div class="rad-1">
  <div class="d-g gar-min gtc-min g-4">
    <div class="ai-c bg-indigo d-f jc-c p-4 rad-1 tc-white">Pneumonoultramicroscopicsilicovolcanoconiosis</div>
    <div class="ai-c bg-indigo-8 d-f jc-c p-4 rad-1 tc-indigo-5">B</div>
    <div class="ai-c bg-indigo-8 d-f jc-c p-4 rad-1 tc-indigo-5">C</div>
  </div>
</div>
```

## Max Content

This example sets the grid auto rows to **max-content**. The rows will size themselves to the largest possible height based on their content.

```html live "gar-max" layout="/src/layouts/inline.astro"
<div class="o-auto rad-1">
  <div class="d-g gar-max gtc-max g-4">
    <div class="ai-c bg-indigo d-f jc-c p-4 rad-1 tc-white">Pneumonoultramicroscopicsilicovolcanoconiosis</div>
    <div class="ai-c bg-indigo-8 d-f jc-c p-4 rad-1 tc-indigo-5">B</div>
    <div class="ai-c bg-indigo-8 d-f jc-c p-4 rad-1 tc-indigo-5">C</div>
  </div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/foundations/responsive-design/) or other factors, such as [hover states](/docs/foundations/variants/).

<BreakpointVariant class="gar-min" variant="gar-auto" classPrefix="gar">
  ### Breakpoint variant
</BreakpointVariant>

<HoverVariant class="gar-min" variant="gar-auto" classPrefix="gar">
  ### Hover variant
</HoverVariant>
