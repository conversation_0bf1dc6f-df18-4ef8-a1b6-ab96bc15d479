---
title: Background Attachment
banner:
  content: N/A
description: Controls how the background responds to scrolling.
slug: docs/background-attachment
---

<Class category="background" name="background-attachment" />

## Fixed

This example sets the image to **fixed**. The image will not move when the content is scrolled.

<Note icon="cursor_arrow">Try scrolling through the image to see how it behaves.</Note>

```html "ba-f"
<div class="o-auto ai-c ba-f d-f ff-c fs-i h-40 jc-c p-8 rad-2 ta-c tc-white w-84" style="background-image: url('https://picsum.photos/600/400?image=106');">
  <h1 class="bf-b-md fs-xl fw-700 px-4 py-2 rad-2">Hi, <PERSON>. It's finally springtime here on earth! I can't stand windy or cold days...</h1>
</div>
```

## Local

This example sets the image to **local**. The image will scroll with the content of the element.

<Note icon="cursor_arrow">Try scrolling through the image to see how it behaves.</Note>

```html live "ba-l"
<div class="o-auto ai-c ba-l bp-c bs-c d-f ff-c fs-i h-40 jc-c p-8 rad-2 ta-c tc-white w-84" style="background-image: url('https://picsum.photos/600/400?image=106');">
  <h1 class="bf-b-md fs-xl fw-700 px-4 py-2 rad-2">Hi, Anne. It's finally springtime here on earth! I can't stand windy or cold days...</h1>
</div>
```

## Scroll

> Initial value

This example sets the image to **scroll**. The image will move with the content of the page.

<Note icon="cursor_arrow">Try scrolling through the image to see how it behaves.</Note>

```html live "ba-s"
<div class="o-auto ai-c ba-s bp-c bs-c d-f ff-c fs-i h-40 jc-c p-8 rad-2 ta-c tc-white w-84" style="background-image: url('https://picsum.photos/600/400?image=106');">
  <h1 class="bf-b-md fs-xl fw-700 px-4 py-2 rad-2">Hi, Anne. It's finally springtime here on earth! I can't stand windy or cold days...</h1>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<MediaModifier class="ba-f" mediaModifier="ba-s" classPrefix="ba">
  ### Media modifier
</MediaModifier>

<HoverModifier class="ba-f" hoverModifier="ba-s" classPrefix="ba">
  ### Hover modifier
</HoverModifier>
