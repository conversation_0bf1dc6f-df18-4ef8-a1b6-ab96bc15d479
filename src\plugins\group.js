import { definePlugin } from '@expressive-code/core'  
import { h } from '@expressive-code/core/hast'  
  
export function group() {  
  return definePlugin({  
    name: 'Tabbed Code Blocks',  
    hookshooks: {  
      preprocessMetadata: ({ codeBlock }) => {  
        // Extract tab information from meta  
        const tabName = codeBlock.metaOptions.getString('tab')  
        const groupId = codeBlock.metaOptions.getString('group')  
          
        if (tabName && groupId) {  
          codeBlock.props.tabName = tabName  
          codeBlock.props.groupId = groupId  
        }  
      },  
      postprocessRenderedBlockGroup: ({ renderedGroupContents, groupAst }) => {  
        // Group blocks by their groupId  
        const groups = new Map()  
          
        renderedGroupContents.forEach(({ codeBlock, renderedBlockAst }, index) => {  
          const groupId = codeBlock.props.groupId  
          if (groupId) {  
            if (!groups.has(groupId)) {  
              groups.set(groupId, [])  
            }  
            groups.get(groupId).push({  
              codeBlock,  
              renderedBlockAst,  
              index  
            })  
          }  
        })  
          
        // Process each group  
        groups.forEach((blocks, groupId) => {  
          if (blocks.length > 1) {  
            createTabbedGroup(blocks, groupAst, groupId)  
          }  
        })  
      }  
    },  
    baseStyles: `  
      .tabbed-code-group {  
        border: 1px solid var(--ec-codeBorder, #333);  
        border-radius: 6px;  
        overflow: hidden;  
      }  
        
      .tab-buttons {  
        display: flex;  
        background: var(--ec-codeBg, #1e1e1e);  
        border-bottom: 1px solid var(--ec-codeBorder, #333);  
      }  
        
      .tab-button {  
        padding: 8px 16px;  
        background: none;  
        border: none;  
        color: var(--ec-codeFg, #fff);  
        cursor: pointer;  
        border-right: 1px solid var(--ec-codeBorder, #333);  
      }  
        
      .tab-button:hover {  
        background: rgba(255, 255, 255, 0.1);  
      }  
        
      .tab-button.active {  
        background: var(--ec-codeBg, #1e1e1e);  
        border-bottom: 2px solid var(--ec-codeAccent, #007acc);  
      }  
        
      .tab-content {  
        display: none;  
      }  
        
      .tab-content.active {  
        display: block;  
      }  
    `,  
    jsModules: [`  
      // Tab switching functionality  
      document.addEventListener('DOMContentLoaded', () => {  
        document.querySelectorAll('.tab-button').forEach(button => {  
          button.addEventListener('click', (e) => {  
            const groupId = e.target.dataset.group  
            const tabId = e.target.dataset.tab  
              
            // Update active button  
            document.querySelectorAll(\`[data-group="\${groupId}"] .tab-button\`).forEach(btn => {  
              btn.classList.remove('active')  
            })  
            e.target.classList.add('active')  
              
            // Update active content  
            document.querySelectorAll(\`[data-group="\${groupId}"] .tab-content\`).forEach(content => {  
              content.classList.remove('active')  
            })  
            document.querySelector(\`[data-group="\${groupId}"] [data-tab="\${tabId}"]\`).classList.add('active')  
          })  
        })  
      })  
    `]  
  })  
}