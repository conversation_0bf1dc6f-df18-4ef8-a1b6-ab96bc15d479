---
title: Outline Style
banner:
  content: N/A
description: Controls the outline style of an element.
slug: docs/outline-style
---

<Class category="outline" name="outline-style" />

This example showcases various `outline-style` utilities:

- The **dashed** utility creates a dashed outline around the element.
- The **none** utility removes any outline from the element.
- The **solid** utility creates a solid outline around the element.

```html live "os-none" "os-d" "os-s"
<div class="d-g g-16 gtc-1 sm:gtc-3">
  <div class="b-1 bc-indigo d-16 oc-indigo oo-1 os-none rad-1"></div>
  <div class="b-1 bc-indigo d-16 oc-indigo oo-1 os-d rad-1"></div>
  <div class="b-1 bc-indigo d-16 oc-indigo oo-1 os-s rad-1"></div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<BreakpointVariant class="os-h" variant="os-s" classPrefix="os">
  ### Breakpoint variant
</BreakpointVariant>

<HoverVariant class="os-h" variant="os-s" classPrefix="os">
  ### Hover variant
</HoverVariant>
