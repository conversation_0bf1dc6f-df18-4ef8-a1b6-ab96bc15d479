---
authors: Renildo
cover:
  alt: Yumma CSS Intellisense
  image: cover.png
date: 2024-08-16
description: We've got some great news to share! The Yumma CSS Intellisense extension for Visual Studio Code is now available. Start coding with Yumma CSS VS Code integration today!
pagefind: false
slug: blog/yummacss-intellisense
title: Introducing Yumma CSS Intellisense
---

We've got some great news to share! The Yumma CSS Intellisense extension for Visual Studio Code is now available. Start coding with Yumma CSS VS Code integration today!

{/* excerpt */}

<iframe
  allowfullscreen
  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
  class="ar-16/9 rad-1 w-full"
  frameborder="0"
  referrerpolicy="strict-origin-when-cross-origin"
  src="https://youtube.com/embed/MX65NiVYxYQ?si=s8meufbeDOxUE54l"
  title="What's new in Yumma CSS Play 0.1.0?"></iframe>

## What's in the box?

Here it is some features packaged with the extension:

- **Completions:** Helpful completions that can be accessed while typing.
- **Documentation**: Provides users with the opportunity to learn more about each completion.
- **Hovering:** Inspect the CSS behind the Yumma CSS classes.

Get [Yumma CSS Intellisense from the Visual Studio Marketplace](https://marketplace.visualstudio.com/items?itemName=yumma-css.yumma-css-intellisense).

---

## Completions

Get suggestions as you type, with information about their CSS properties and previews of the colors.

![Yumma CSS Intellisense - Completions](yummacss-intellisense-completions.png)

## Documentation

Take a closer look at the documentation with these helpful links.

![Yumma CSS Intellisense - Documentation](yummacss-intellisense-documentation.png)

## Hovering

Move your cursor over the name of a class to see each of its CSS properties.

![Yumma CSS Intellisense - Hovering](yummacss-intellisense-hovering.png)
