---
title: Grayscale
banner:
  content: N/A
description: Applies a grayscale filter to an element.
slug: docs/grayscale
---

<Class category="effect" name="grayscale" />

This example showcases various `grayscale()` filter utilities:

- The **f-g-0** grayscale utility sets the filter property to **grayscale(0%)**, meaning no grayscale is applied, and the colors remain unchanged.
- The **f-g-50** grayscale utility sets the filter property to **grayscale(50%)**, applying a moderate grayscale effect, resulting in a mix of color and gray.
- Finally, **f-g-100** grayscale utility sets the filter property to **grayscale(100%)**, completely removing all color and rendering the element in shades of gray.

```html live "f-g-0" "f-g-50" "f-g-100"
<div class="d-g g-16 gtc-1 sm:gtc-3">
  <div class="d-32 p-r rad-1">
    <img class="d-full f-g-0 of-c rad-1" src="https://picsum.photos/300?image=875" />
  </div>
  <div class="d-32 p-r rad-1">
    <img class="d-full f-g-50 of-c rad-1" src="https://picsum.photos/300?image=875" />
  </div>
  <div class="d-32 p-r rad-1">
    <img class="d-full f-g-100 of-c rad-1" src="https://picsum.photos/300?image=875" />
  </div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<MediaModifier class="f-g-0" mediaModifier="f-g-100" classPrefix="f-g">
  ### Breakpoint modifier
</MediaModifier>

<HoverModifier class="f-g-0" hoverModifier="f-g-100" classPrefix="f-g">
  ### Hover modifier
</HoverModifier>
