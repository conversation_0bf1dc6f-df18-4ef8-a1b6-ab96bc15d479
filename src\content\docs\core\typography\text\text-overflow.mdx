---
title: Text Overflow
banner:
  content: N/A
description: Controls how hidden text overflow content is displayed.
slug: docs/text-overflow
---

<Class category="text" name="text-overflow" />

## Clip

> Initial value

This example sets the text overflow to **clip**. The text will be clipped at the edge of the container without any indication that it has been cut off.

```html live "to-c" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <div class="bg-white rad-1">
    <p class="fs-lg o-h p-4 tc-lead to-c w-25 ws-nw">Pneumonoultramicroscopicsilicovolcanoconiosis</p>
  </div>
</div>
```

## Ellipsis

This example sets the text overflow to **ellipsis**. The text will be truncated with an ellipsis (`...`) at the end to indicate that there is more content that is not visible.

```html live "to-e" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <div class="bg-white rad-1">
    <p class="fs-lg o-h p-4 tc-lead to-e w-25 ws-nw">Pneumonoultramicroscopicsilicovolcanoconiosis</p>
  </div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<BreakpointVariant class="to-c" variant="to-e" classPrefix="to">
  ### Breakpoint variant
</BreakpointVariant>

<HoverVariant class="to-c" variant="to-e" classPrefix="to">
  ### Hover variant
</HoverVariant>
