---
title: Table Layout
banner:
  content: N/A
description: Controls the table cell layout algorithm.
slug: docs/table-layout
---

<Class category="table" name="table-layout" />

## Auto

> Initial value

This example sets the table layout to **auto**. The browser will determine the column widths based on the content.

```html live "tl-auto" layout="/src/layouts/inline.astro"
<table class="b-1  bc-c tc-indigo tl-auto w-full">
  <thead>
    <tr>
      <th class="b-1 bc-indigo bg-indigo-2 p-3">Title</th>
      <th class="b-1 bc-indigo bg-indigo-2 p-3">Opening Line</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td class="b-1 bc-indigo p-3">Letters to Anne</td>
      <td class="b-1 bc-indigo p-3">It's finally springtime here on earth!</td>
    </tr>
  </tbody>
</table>
```

## Fixed

This example sets the table layout to **fixed**. The column widths are set by the width of the table and the first row.

```html live "tl-f" layout="/src/layouts/inline.astro"
<table class="b-1  bc-c tc-indigo tl-f w-full">
  <thead>
    <tr>
      <th class="b-1 bc-indigo bg-indigo-2 p-3">Title</th>
      <th class="b-1 bc-indigo bg-indigo-2 p-3">Opening Line</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td class="b-1 bc-indigo p-3">Letters to Anne</td>
      <td class="b-1 bc-indigo p-3">It's finally springtime here on earth!</td>
    </tr>
  </tbody>
</table>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/foundations/responsive-design/) or other factors, such as [hover states](/docs/foundations/variants/).

<BreakpointVariant class="tl-f" variant="tl-a" classPrefix="tl">
  ### Breakpoint variant
</BreakpointVariant>

<HoverVariant class="tl-f" variant="tl-a" classPrefix="tl">
  ### Hover variant
</HoverVariant>
