---
title: Appearance
banner:
  content: N/A
description: Utilities to disable the styling of the original form controls.
slug: docs/appearance
---

<Class category="interactivity" name="appearance" />

## Auto

This example sets the appearance to **auto**. The **a-auto** utility allows the element to use the default styling provided by the browser, which can vary based on the element type and the user's operating system.

```html live "a-auto"
<button class="a-auto px-5 py-1">Send</button>
```

## None

> Initial value

This example sets the appearance to **none**. The **a-none** utility removes all default styling from the element.

```html live "a-none"
<button class="a-none px-5 py-1">Send</button>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<MediaModifier class="a-none" mediaModifier="a-auto" classPrefix="a">
  ### Media modifier
</MediaModifier>

<HoverModifier class="a-none" hoverModifier="a-auto" classPrefix="a">
  ### Hover modifier
</HoverModifier>
