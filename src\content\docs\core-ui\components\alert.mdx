---
title: Alert
description: A component that displays important messages and notifications to users.
slug: docs/ui/alert
draft: true
---

## Examples

A basic implementation of the Alert component with default styling.

```tsx showLineNumbers
import { Alert, AlertContent, AlertTitle, AlertDescription } from "yumma-ui";
import { InfoCircledIcon } from "@radix-ui/react-icons";

const App = () => {
  return (
    <Alert variant="base">
      <InfoCircledIcon />
      <AlertContent>
        <AlertTitle>Information</AlertTitle>
        <AlertDescription>This is an informational message to help guide users through the interface.</AlertDescription>
      </AlertContent>
    </Alert>
  );
};

export default App;
```

## Usage

```tsx showLineNumbers
import { Alert, AlertContent, AlertTitle, AlertDescription } from "yumma-ui";
```

```tsx showLineNumbers
<Alert>
  <AlertContent>
    <AlertTitle>Information</AlertTitle>
    <AlertDescription>This is an informational message to help guide users through the interface.</AlertDescription>
  </AlertContent>
</Alert>
```

## API reference

The Alert component can be adapted to suit specific requirements by utilizing the internal API of Yumma UI, thereby enabling the modification of its visual appearance and operational characteristics.

### `variant`

> Default value is `base`

The `variant` property allows you to choose from different pre-defined styles that match your design system.

```ts
(property) variant?: "base" | "error" | "info" | "success" | "warning" | null | undefined
```

## Custom styling

The Alert component exposes a simple API to control its look and behavior through props.

```tsx showLineNumbers 'className="..."'
import { Alert } from "yumma-ui";

<Alert className="...">Example</Alert>;
```

## Extend properties

The Alert component is fully extendable. It supports all native HTML `<div>` attributes through its props:

```tsx showLineNumbers "onClick={() => alert("Alert clicked!")}"
import { Alert } from "yumma-ui";

const App = () => {
  return <Alert onClick={() => alert("Alert clicked!")} {...props}>Example</Alert>;
};

export default App;
```
