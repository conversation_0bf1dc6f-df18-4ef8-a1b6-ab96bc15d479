---
title: Field Sizing
banner:
  content: N/A
description: Controls the sizing of elements that have a default preferred size.
slug: docs/field-sizing
---

<Class category="interactivity" name="field-sizing" />

## Content

This example sets the field sizing to **fixed**. The **fs-f** utility ensures that the field maintains a fixed size, regardless of the content within it.

```html live "fs-c"
<div class="d-f fd-c">
  <label class="mb-2">Write a message:</label>
  <textarea class="b-2 fs-c max-h-50 max-w-100 r-none rad-1"></textarea>
</div>
```

## Fixed

> Initial value

This example sets the field sizing to **content**. The **fs-c** utility allows the field to size itself based on the content, adjusting its size dynamically to fit the content inside.

```html live "fs-f"
<div class="d-f fd-c">
  <label class="mb-2">Write a message:</label>
  <textarea class="b-2 fs-f max-h-50 max-w-100 r-none rad-1"></textarea>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<MediaModifier class="fs-f" mediaModifier="fs-c" classPrefix="fs">
  ### Media modifier
</MediaModifier>

<HoverModifier class="fs-f" hoverModifier="fs-c" classPrefix="fs">
  ### Hover modifier
</HoverModifier>
