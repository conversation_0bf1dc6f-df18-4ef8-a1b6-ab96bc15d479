---
title: Place Items
banner:
  content: N/A
description: Control how items are justified and aligned simultaneously.
slug: docs/place-items
---

<Class category="grid" name="place-items" />

## Baseline

This example sets the place items to **baseline**. The items will be aligned along the baseline of the container.

```html live "pi-b" layout="/src/layouts/inline.astro"
<div class="tc-white" id="area">
  <div class="d-g g-4 gtc-2 h-50 pi-b">
    <div class="bg-indigo d-14 d-f jc-c ai-c p-4 rad-1">A</div>
    <div class="bg-indigo d-14 d-f jc-c ai-c p-4 rad-1">B</div>
    <div class="bg-indigo d-14 d-f jc-c ai-c p-4 rad-1">C</div>
    <div class="bg-indigo d-14 d-f jc-c ai-c p-4 rad-1">D</div>
  </div>
</div>
```

## Center

This example sets the place items to **center**. The items will be centered within the container.

```html live "pi-c" layout="/src/layouts/inline.astro"
<div class="tc-white" id="area">
  <div class="d-g g-4 gtc-2 h-50 pi-c">
    <div class="bg-indigo d-14 d-f jc-c ai-c p-4 rad-1">A</div>
    <div class="bg-indigo d-14 d-f jc-c ai-c p-4 rad-1">B</div>
    <div class="bg-indigo d-14 d-f jc-c ai-c p-4 rad-1">C</div>
    <div class="bg-indigo d-14 d-f jc-c ai-c p-4 rad-1">D</div>
  </div>
</div>
```

## End

This example sets the place items to **end**. The items will be aligned to the end of the container.

```html live "pi-e" layout="/src/layouts/inline.astro"
<div class="tc-white" id="area">
  <div class="d-g g-4 gtc-2 h-50 pi-e">
    <div class="bg-indigo d-14 d-f jc-c ai-c p-4 rad-1">A</div>
    <div class="bg-indigo d-14 d-f jc-c ai-c p-4 rad-1">B</div>
    <div class="bg-indigo d-14 d-f jc-c ai-c p-4 rad-1">C</div>
    <div class="bg-indigo d-14 d-f jc-c ai-c p-4 rad-1">D</div>
  </div>
</div>
```

## Start

This example sets the place items to **start**. The items will be aligned to the start of the container.

```html live "pi-s" layout="/src/layouts/inline.astro"
<div class="tc-white" id="area">
  <div class="d-g g-4 gtc-2 h-50 pi-s">
    <div class="bg-indigo d-14 d-f jc-c ai-c p-4 rad-1">A</div>
    <div class="bg-indigo d-14 d-f jc-c ai-c p-4 rad-1">B</div>
    <div class="bg-indigo d-14 d-f jc-c ai-c p-4 rad-1">C</div>
    <div class="bg-indigo d-14 d-f jc-c ai-c p-4 rad-1">D</div>
  </div>
</div>
```

## Stretch

This example sets the place items to **stretch**. The items will stretch to fill the container along the cross axis.

```html live "pi-st" layout="/src/layouts/inline.astro"
<div class="tc-white" id="area">
  <div class="d-g g-4 gtc-2 h-50 pi-st">
    <div class="bg-indigo d-14 d-f jc-c ai-c p-4 rad-1">A</div>
    <div class="bg-indigo d-14 d-f jc-c ai-c p-4 rad-1">B</div>
    <div class="bg-indigo d-14 d-f jc-c ai-c p-4 rad-1">C</div>
    <div class="bg-indigo d-14 d-f jc-c ai-c p-4 rad-1">D</div>
  </div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<BreakpointVariant class="pi-s" variant="pi-e" classPrefix="pi">
  ### Breakpoint variant
</BreakpointVariant>

<HoverVariant class="pi-s" variant="pi-e" classPrefix="pi">
  ### Hover variant
</HoverVariant>
