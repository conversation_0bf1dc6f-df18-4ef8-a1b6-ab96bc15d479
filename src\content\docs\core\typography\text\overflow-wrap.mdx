---
title: Overflow Wrap
banner:
  content: N/A
description: Controls word wrapping
slug: docs/overflow-wrap
---

<Class category="text" name="overflow-wrap" />

## Break Word

This example sets the overflow wrap to **break-word**. The browser will break long words and wrap them onto the next line to prevent overflow.

```html live "ow-bw" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1 ow-bw">
  <p class="bg-white fs-lg p-4 rad-1 ta-c tc-lead">Pneumonoultramicroscopicsilicovolcanoconiosis</p>
</div>
```

## Normal

> Initial value

This example sets the overflow wrap to **normal**. The browser will only break lines at allowed break points, and long words will overflow the container if they cannot fit.

```html live "ow-n" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg p-4 rad-1 ta-c tc-lead o-auto ow-n">Pneumonoultramicroscopicsilicovolcanoconiosis</p>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<MediaModifier class="ow-n" mediaModifier="ow-bw" classPrefix="ow">
  ### Media modifier
</MediaModifier>

<HoverModifier class="ow-n" hoverModifier="ow-bw" classPrefix="ow">
  ### Hover modifier
</HoverModifier>
