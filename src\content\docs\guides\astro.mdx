---
title: Astro with Yumma CSS
banner:
  content: N/A
description: Integrate Yumma CSS into Astro applications.
slug: docs/guides/astro
---

## Creating a new project

To create a new Astro project, you need run the [Astro CLI Wizard](https://docs.astro.build/en/install-and-setup/#install-from-the-cli-wizard) in your terminal.

<Steps>

    1. **Install Yumma CSS**

        Install `yummacss` using a package manager.

        <PackageManagers pkgManagers={["pnpm", "npm", "yarn"]} pkg="yummacss -D" title="Terminal" />

        <br />

    2. **Initialize configuration**

            Create a configuration file in your project.

            <PackageManagers pkgManagers={["pnpm", "npm", "yarn"]} pkg="yummacss init" title="Terminal" type="dlx" />

    3. **Set up configuration**

        Specify the locations of all your project files in the config file.

        ```js title="yumma.config.js" mark={2-3}
        export default {
            source: ["./src/**/*.{html,astro}"],
            output: "./src/styles.css",
            buildOptions: {
                reset: true,
                minify: false,
            }
        };
        ```

    4. **Build styles**

        You can now start generating your CSS with the [`build`](/docs/first-steps#build-command) or [`watch`](/docs/first-steps#watch-command) command.

        <PackageManagers pkgManagers={["pnpm", "npm", "yarn"]} pkg="yummacss build" title="Terminal" type="dlx" />

    5. **Done!**

        You're all set to start using Yumma CSS utility classes in your project.

        ```astro title="pages/index.astro" mark={6-8}
        ---
        import Layout from '../layouts/Layout.astro';
        ---

        <Layout title="Welcome to Astro.">
            <div class="bg-indigo-12 d-g h-dvh pi-c tc-white">
                <h1 class="fs-3xl fw-500">Yumma CSS ⚙️ Astro</h1>
            </div>
        </Layout>
        ```

</Steps>

---

## Clone this project

Skip the guide steps entirely with our Astro starter.

```bash title="Cloning the project..."
git clone https://github.com/yumma-lib/with-astro.git
```
