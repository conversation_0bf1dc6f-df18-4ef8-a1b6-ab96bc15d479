---
title: Svelte with Yumma CSS
banner:
  content: N/A
description: Integrate Yumma CSS into Svelte applications.
slug: docs/guides/svelte
---

## Creating a new project

To create a new Svelte project, you need run the [Svelte command](https://svelte.dev/docs/cli/sv-create) in your terminal.

<Steps>

    1. **Install Yumma CSS**

        Install `yummacss` using a package manager.

        <PackageManagers pkgManagers={["pnpm", "npm", "yarn"]} pkg="yummacss -D" title="Terminal" />

        <br />

    2. **Initialize configuration**

            Create a configuration file in your project.

            <PackageManagers pkgManagers={["pnpm", "npm", "yarn"]} pkg="yummacss init" title="Terminal" type="dlx" />

    3. **Set up configuration**

        Specify the locations of all your project files in the config file.

        ```js title="yumma.config.js" mark={2-3}
        export default {
            source: ["./src/**/*.svelte"],
            output: "./src/global.css",
            buildOptions: {
                reset: true,
                minify: false,
            }
        };
        ```

    4. **Build styles**

        You can now start generating your CSS with the [`build`](/docs/foundations/config/#build) or [`watch`](/docs/foundations/config/#watch) command.

        <PackageManagers pkgManagers={["pnpm", "npm", "yarn"]} pkg="yummacss build" title="Terminal" type="dlx" />

    5. **Done!**

        You're all set to start using Yumma CSS utility classes in your project.

        ```svelte title="routes/+page.svelte" mark={1}
        <h1 class="fs-sm fw-700 ta-c tc-indigo">Yumma CSS 🤝 Svelte</h1>
        ```

</Steps>

---

## Clone this project

Skip the guide steps entirely with our Svelte starter.

```bash title="Cloning the project..."
git clone https://github.com/yumma-lib/with-svelte.git
```
