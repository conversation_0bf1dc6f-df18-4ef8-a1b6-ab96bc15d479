---
title: Grid Auto Flow
banner:
  content: N/A
description: Controls the automatic placement of grid elements.
slug: docs/grid-auto-flow
---

<Class category="grid" name="grid-auto-flow" />

## Column

This example sets the grid auto flow to **column**. The items will be placed in the grid by filling each column before moving to the next one.

```html live "gaf-c" layout="/src/layouts/inline.astro"
<div class="d-g g-4 gaf-c gtc-3 gtr-3 ta-c" id="area">
  <div class="bg-indigo-8 gc-s-2 p-4 rad-1 tc-indigo-5">A</div>
  <div class="bg-indigo-8 gc-s-2 p-4 rad-1 tc-indigo-5">B</div>
  <div class="bg-indigo p-4 rad-1 tc-white">C</div>
  <div class="bg-indigo-8 p-4 rad-1 tc-indigo-5">D</div>
  <div class="bg-indigo-8 p-4 rad-1 tc-indigo-5">E</div>
</div>
```

## Dense

This example sets the grid auto flow to **dense**. The items will be placed in the grid in a way that fills in gaps as efficiently as possible.

```html live "gaf-d" layout="/src/layouts/inline.astro"
<div class="d-g g-4 gaf-d gtc-3 gtr-3 ta-c tc-white" id="area">
  <div class="bg-indigo-8 gc-s-2 p-4 rad-1 tc-indigo-5">A</div>
  <div class="bg-indigo-8 gc-s-2 p-4 rad-1 tc-indigo-5">B</div>
  <div class="bg-indigo p-4 rad-1">C</div>
  <div class="bg-indigo-8 p-4 rad-1 tc-indigo-5">D</div>
  <div class="bg-indigo-8 p-4 rad-1 tc-indigo-5">E</div>
</div>
```

## Row

> Initial value

This example sets the grid auto flow to **row**. The items will be placed in the grid by filling each row before moving to the next one.

```html live "gaf-r" layout="/src/layouts/inline.astro"
<div class="d-g g-4 gaf-r gtc-3 gtr-3 ta-c tc-white" id="area">
  <div class="bg-indigo-8 gc-s-2 p-4 rad-1 tc-indigo-5">A</div>
  <div class="bg-indigo-8 gc-s-2 p-4 rad-1 tc-indigo-5">B</div>
  <div class="bg-indigo p-4 rad-1">C</div>
  <div class="bg-indigo-8 p-4 rad-1 tc-indigo-5">D</div>
  <div class="bg-indigo-8 p-4 rad-1 tc-indigo-5">E</div>
</div>
```

## Row Dense

This example sets the grid auto flow to **row dense**. The items will be placed in the grid by filling each row first, while also attempting to fill any gaps efficiently.

```html live "gaf-rd" layout="/src/layouts/inline.astro"
<div class="d-g gaf-rd g-4 ta-c tc-white tc-white" id="area">
  <div class="bg-indigo p-4 rad-1">A</div>
  <div class="bg-indigo p-4 rad-1">B</div>
  <div class="bg-indigo p-4 rad-1">C</div>
  <div class="bg-indigo p-4 rad-1">D</div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<BreakpointVariant class="gaf-c" variant="gaf-r" classPrefix="gaf">
  ### Breakpoint variant
</BreakpointVariant>

<HoverVariant class="gaf-c" variant="gaf-r" classPrefix="gaf">
  ### Hover variant
</HoverVariant>
