---
title: Border Collapse
banner:
  content: N/A
description: Controls the collapsing or separation of table borders.
slug: docs/border-collapse
---

<Class category="border" name="border-collapse" />

## Collapse

This example sets the border collapse to **collapse**. The borders of the table will be merged together.

```html live "bc-c" layout="/src/layouts/inline.astro"
<table class="b-1 bc-indigo bc-c ta-c tc-indigo w-full">
  <tr>
    <td class="b-1 bc-indigo p-2">A</td>
    <td class="b-1 bc-indigo p-2">B</td>
  </tr>
  <tr>
    <td class="b-1 bc-indigo p-2">C</td>
    <td class="b-1 bc-indigo p-2">D</td>
  </tr>
</table>
```

## Separate

> Initial value

This example sets the border collapse to **separate**. The borders of the table cells will remain distinct, allowing for spacing between them.

```html live "bc-s" layout="/src/layouts/inline.astro"
<table class="b-1 bc-indigo bc-s ta-c tc-indigo w-full">
  <tr>
    <td class="b-1 bc-indigo p-2">A</td>
    <td class="b-1 bc-indigo p-2">B</td>
  </tr>
  <tr>
    <td class="b-1 bc-indigo p-2">C</td>
    <td class="b-1 bc-indigo p-2">D</td>
  </tr>
</table>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<BreakpointVariant class="bs-s" variant="bc-c" classPrefix="bs">
  ### Breakpoint variant
</BreakpointVariant>

<HoverVariant class="bs-s" variant="bc-c" classPrefix="bs">
  ### Hover variant
</HoverVariant>
