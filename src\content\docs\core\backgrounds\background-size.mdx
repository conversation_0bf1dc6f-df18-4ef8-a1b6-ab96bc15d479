---
title: Background Size
banner:
  content: N/A
description: Controls the size of the background image of an element.
slug: docs/background-size
---

<Class category="background" name="background-size" />

## Auto

> Initial value

This example sets the background size to **auto**. The background image retains its original size.

```html live "bs-auto"
<div class="d-30 rad-1">
  <div class="bs-auto bp-c h-30 rad-1" style="background-image:url(https://picsum.photos/300?image=870)"></div>
</div>
```

## Cover

This example sets the background size to **cover**. The background image will cover the entire element.

```html live "bs-c"
<div class="d-30 rad-1">
  <div class="bs-c bp-c h-30 rad-1" style="background-image:url(https://picsum.photos/300?image=870)"></div>
</div>
```

## Contain

This example sets the background size to **contain**. The background image will be scaled to fit within the element.

```html live "bs-co"
<div class="d-30 rad-1">
  <div class="bs-co bp-c h-30 rad-1" style="background-image:url(https://picsum.photos/300?image=870)"></div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<MediaModifier class="bs-auto" mediaModifier="bs-c" classPrefix="bs">
  ### Media modifier
</MediaModifier>

<HoverModifier class="bs-auto" hoverModifier="bs-c" classPrefix="bs">
  ### Hover modifier
</HoverModifier>
