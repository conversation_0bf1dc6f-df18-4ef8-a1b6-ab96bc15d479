---
title: Column Gap
banner:
  content: N/A
description: Controls the spacing between columns in a grid layout.
slug: docs/column-gap
---

<Class category="grid" name="column-gap" />

This example sets the column gap to **2.5rem**. The element will have a uniform spacing of **2.5rem** between its columns.

```html live "cg-10" layout="/src/layouts/inline.astro"
<div class="cg-10 d-g gtc-3 tc-white" id="area">
  <div class="ai-c bg-indigo d-f jc-c p-4 rad-1">A</div>
  <div class="ai-c bg-indigo d-f jc-c p-4 rad-1">B</div>
  <div class="ai-c bg-indigo d-f jc-c p-4 rad-1">C</div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/foundations/responsive-design/) or other factors, such as [hover states](/docs/foundations/variants/).

<BreakpointVariant class="cg-1" variant="cg-2" classPrefix="cg">
  ### Breakpoint variant
</BreakpointVariant>

<HoverVariant class="cg-1" variant="cg-2" classPrefix="cg">
  ### Hover variant
</HoverVariant>
