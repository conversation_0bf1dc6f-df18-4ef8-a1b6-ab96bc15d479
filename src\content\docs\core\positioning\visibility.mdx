---
title: Visibility
banner:
  content: N/A
description: Controls the visibility of elements without changing a document's layout.
slug: docs/visibility
---

<Class category="positioning" name="visibility" />

## Collapse

This example sets the visibility to **collapse**. The element will be hidden, and it will not take up any space in the layout.

```html live "v-c" layout="/src/layouts/inline.astro"
<table class="bc-c ta-c tc-indigo w-full">
  <thead>
    <tr>
      <th class="b-1 bc-indigo bg-indigo-1 p-2">H1</th>
      <th class="b-1 bc-indigo bg-indigo-1 p-2">H2</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td class="b-1 bc-indigo p-2">A</td>
      <td class="b-1 bc-indigo p-2">B</td>
    </tr>
    <tr class="v-c">
      <td class="b-1 bc-indigo p-2">C</td>
      <td class="b-1 bc-indigo p-2">D</td>
    </tr>
    <tr>
      <td class="b-1 bc-indigo p-2">E</td>
      <td class="b-1 bc-indigo p-2">F</td>
    </tr>
  </tbody>
</table>
```

## Hidden

This example sets the visibility to **hidden**. The element will be hidden, but it will still take up space in the layout.

```html live "v-h" layout="/src/layouts/inline.astro"
<div class="d-g g-4 gtc-1 sm:gtc-3">
  <div class="ai-c bg-indigo d-f jc-c p-6 rad-1 tc-white v-h">A</div>
  <div class="ai-c bg-indigo d-f jc-c p-6 rad-1 tc-white">B</div>
  <div class="ai-c bg-indigo d-f jc-c p-6 rad-1 tc-white">C</div>
</div>
```

## Visible

> Initial value

This example sets the visibility to **visible**. The element will be displayed and take up space in the layout.

```html live "v-v" layout="/src/layouts/inline.astro"
<div class="d-g g-4 gtc-1 sm:gtc-3">
  <div class="ai-c bg-indigo d-f jc-c p-6 rad-1 tc-white v-v">A</div>
  <div class="ai-c bg-indigo d-f jc-c p-6 rad-1 tc-white">B</div>
  <div class="ai-c bg-indigo d-f jc-c p-6 rad-1 tc-white">C</div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<MediaModifier class="v-i" mediaModifier="p-v" classPrefix="v">
  ### Media modifier
</MediaModifier>

<HoverModifier class="v-i" hoverModifier="p-v" classPrefix="v">
  ### Hover modifier
</HoverModifier>
