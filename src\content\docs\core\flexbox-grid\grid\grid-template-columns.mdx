---
title: Grid Template Columns
banner:
  content: N/A
description: Utilities to define the columns in a grid layout.
slug: docs/grid-template-columns
---

<Class category="grid" name="grid-template-columns" />

This example sets the grid template columns to **repeat(3, minmax(0, 1fr))**. The grid will have **3** columns, each with a minimum size of **0** and a maximum size of **1fr**, allowing them to grow and fill the available space equally.

```html live "gtc-3" layout="/src/layouts/inline.astro"
<div class="d-g g-4 gtc-3 ta-c tc-white" id="area">
  <div class="bg-indigo p-4 rad-1">A</div>
  <div class="bg-indigo p-4 rad-1">B</div>
  <div class="bg-indigo p-4 rad-1">C</div>
  <div class="bg-indigo p-4 rad-1">D</div>
  <div class="bg-indigo p-4 rad-1">E</div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<BreakpointVariant class="gtc-1" variant="gtc-2" classPrefix="gtc">
  ### Breakpoint variant
</BreakpointVariant>

<HoverVariant class="gtc-1" variant="gtc-2" classPrefix="gtc">
  ### Hover variant
</HoverVariant>
