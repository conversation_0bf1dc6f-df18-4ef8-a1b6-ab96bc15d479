---
title: Z-Index
banner:
  content: N/A
description: Controls the element's stack order.
slug: docs/z-index
---

<Class category="positioning" name="z-index" />

This example showcases various `z-index` utilities:

- The **zi-10** z index utility sets the z-index to **10**, positioning the element above elements with a lower z-index value.
- The **zi-20** z index utility sets the z-index to **20**, positioning the element even higher in the stacking context.

```html live "zi-10" "zi-20"
<div class="p-r">
  <div class="ai-c bg-indigo d-30 d-f jc-c p-r rad-1 zi-10">
    <p class="tc-white">A</p>
  </div>
  <div class="ai-c bg-indigo-8 d-15 d-f jc-c l-0 p-a rad-1 t-0 zi-20">
    <p class="tc-white">B</p>
  </div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<MediaModifier class="zi-10" mediaModifier="zi-20" classPrefix="zi">
  ### Breakpoint modifier
</MediaModifier>

<HoverModifier class="zi-10" hoverModifier="zi-20" classPrefix="zi">
  ### Hover modifier
</HoverModifier>
