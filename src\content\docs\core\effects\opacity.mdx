---
title: Opacity
banner:
  content: N/A
description: Controls the opacity of an element.
slug: docs/opacity
---

<Class category="effect" name="opacity" />

This example showcases various `opacity` utilities:

- The **o-20** opacity utility sets the opacity to **0.2**, making the element 20% visible and 80% transparent.
- The **o-50** opacity utility sets the opacity to **0.5**, resulting in a 50% visibility and 50% transparency.
- The **o-70** opacity utility sets the opacity to **0.7**, making the element 70% visible and 30% transparent.
- Finally, the **o-100** utility sets the opacity to **1.0**, making the element fully opaque and completely visible.

```html live "o-20" "o-50" "o-70" "o-100"
<div class="d-g g-16 gtc-1 sm:gtc-4">
  <div class="o-20 ai-c bg-indigo d-f d-18 jc-c rad-1 tc-white">20</div>
  <div class="o-50 ai-c bg-indigo d-f d-18 jc-c rad-1 tc-white">50</div>
  <div class="o-70 ai-c bg-indigo d-f d-18 jc-c rad-1 tc-white">70</div>
  <div class="o-100 ai-c bg-indigo d-f d-18 jc-c rad-1 tc-white">100</div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<MediaModifier class="o-10" mediaModifier="o-20" classPrefix="o">
  ### Breakpoint modifier
</MediaModifier>

<HoverModifier class="o-10" hoverModifier="o-20" classPrefix="o">
  ### Hover modifier
</HoverModifier>
