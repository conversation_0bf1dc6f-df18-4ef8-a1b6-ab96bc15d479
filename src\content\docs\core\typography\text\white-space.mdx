---
title: White Space
banner:
  content: N/A
description: Controls how white space is handled within an element.
slug: docs/white-space
---

<Class category="text" name="white-space" />

## Break Spaces

This example sets the white space to **break-spaces**. The text will respect spaces and line breaks, allowing for wrapping at spaces.

{/* prettier-ignore */}
```html live "ws-bs" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <div class="p-4 bg-white rad-1">
    <p class="tc-lead fs-lg ws-bs">Hi,    <PERSON>.    It's    finally    springtime    here    on    Earth!    I    can't    stand    windy    or    cold    days,    so    I'm    very    excited    for    the    spring.    There    are    so    many    beautiful,    colorful    trees    where    I    live.    I'll    try    to    send    you    some    pictures    I    took    with    the    camera    you    gave    me    for    my    birthday.    I've    been    a    little    under    the    weather    lately,    but    I'll    bounce    back    in    no    time    so    we    can    meet    up    and    hug    each    other    again.</p>
  </div>
</div>
```

## Normal

> Initial value

This example sets the white space to **normal**. The text will wrap normally, and multiple spaces will be collapsed into a single space.

{/* prettier-ignore */}
```html live "ws-n" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <div class="p-4 bg-white rad-1">
    <p class="tc-lead fs-lg ws-n">Hi,    Anne.    It's    finally    springtime    here    on    Earth!    I    can't    stand    windy    or    cold    days,    so    I'm    very    excited    for    the    spring.    There    are    so    many    beautiful,    colorful    trees    where    I    live.    I'll    try    to    send    you    some    pictures    I    took    with    the    camera    you    gave    me    for    my    birthday.    I've    been    a    little    under    the    weather    lately,    but    I'll    bounce    back    in    no    time    so    we    can    meet    up    and    hug    each    other    again.</p>
  </div>
</div>
```

## No Wrap

This example sets the white space to **nowrap**. The text will not wrap to the next line, and it will overflow the container if it exceeds the width.

{/* prettier-ignore */}
```html live "ws-nw" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <div class="p-4 bg-white rad-1 o-x-auto">
    <p class="tc-lead fs-lg ws-nw">Hi,    Anne.    It's    finally    springtime    here    on    Earth!    I    can't    stand    windy    or    cold    days,    so    I'm    very    excited    for    the    spring.    There    are    so    many    beautiful,    colorful    trees    where    I    live.    I'll    try    to    send    you    some    pictures    I    took    with    the    camera    you    gave    me    for    my    birthday.    I've    been    a    little    under    the    weather    lately,    but    I'll    bounce    back    in    no    time    so    we    can    meet    up    and    hug    each    other    again.</p>
  </div>
</div>
```

## Pre

This example sets the white space to **pre**. The text will be displayed as is, preserving spaces and line breaks.

{/* prettier-ignore */}
```html live "ws-p" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <div class="p-4 bg-white rad-1">
    <p class="tc-lead fs-lg ws-p">Hi,    Anne.    It's    finally    springtime    here    on    Earth!
It    is    a    well-known    pangram,
    I    can't    stand    windy    or    cold    days,
        so    I'm    very    excited    for    the    spring.
There    are    so    many    beautiful,    colorful    trees    where    I    live.
    I'll    try    to    send    you    some    pictures    I    took    with    the    camera    you    gave    me    for    my    birthday.
  </div>
</div>
```

## Pre-Line

This example sets the white space to **pre-line**. The text will preserve line breaks but collapse multiple spaces into a single space.

{/* prettier-ignore */}
```html live "ws-pl" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <div class="p-4 bg-white rad-1">
    <p class="tc-lead fs-lg ws-pl">Hi,    Anne.    It's    finally    springtime    here    on    Earth!
      I    can't    stand    windy    or    cold    days,    so    I'm    very
      excited    for    the    spring.    There    are    so    many    beautiful,    colorful    trees    where    I    live.
      I'll    try    to    send    you    some    pictures    I    took    with    the    camera    you    gave    me    for    my    birthday.
      I've    been    a    little    under    the    weather    lately,    but    I'll    bounce    back    in    no    time    so    we    can    meet    up    and    hug    each    other    again.
    </p>
  </div>
</div>
```

## Pre-Wrap

This example sets the white space to **pre-wrap**. The text will preserve spaces and line breaks, and it will wrap when necessary.

{/* prettier-ignore */}
```html live "ws-pw" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <div class="p-4 bg-white rad-1">
    <p class="tc-lead fs-lg ws-pw">Hi,    Anne.    It's    finally    springtime    here    on    Earth!    I    can't    stand    windy    or    cold    days,    so    I'm    very    excited    for    the    spring.    There    are    so    many    beautiful,    colorful    trees    where    I    live.    I'll    try    to    send    you    some    pictures    I    took    with    the    camera    you    gave    me    for    my    birthday.    I've    been    a    little    under    the    weather    lately,    but    I'll    bounce    back    in    no    time    so    we    can    meet    up    and    hug    each    other    again.</p>
  </div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/foundations/responsive-design/) or other factors, such as [hover states](/docs/foundations/variants/).

<BreakpointVariant class="ws-n" variant="ws-pw" classPrefix="ws">
  ### Breakpoint variant
</BreakpointVariant>

<HoverVariant class="ws-n" variant="ws-pw" classPrefix="ws">
  ### Hover variant
</HoverVariant>
