---
title: Background Position
banner:
  content: N/A
description: Controls the position of the background image of an element.
slug: docs/background-position
---

<Class category="background" name="background-position" />

This example showcases various `background-position` utilities:

- The **bottom** background position utility aligns the image element to the bottom edge of the element.
- The **center** background position utility centers the image element within the element.
- The **left** background position utility aligns the image element to the left edge of the element.
- The **right** background position utility aligns the image element to the right edge of the element.
- The **top** background position utility aligns the image element to the top edge of the element.

```html live "bp-lt" "bp-t" "bp-rt" "bp-l" "bp-c" "bp-r" "bp-lb" "bp-b" "bp-rb"
<div class="d-g g-1 gtc-3">
  <div class="bp-lt d-24 rad-1" style="background-image:url(https://picsum.photos/300?image=360)"></div>
  <div class="bp-t d-24 rad-1" style="background-image:url(https://picsum.photos/300?image=360)"></div>
  <div class="bp-rt d-24 rad-1" style="background-image:url(https://picsum.photos/300?image=360)"></div>
  <div class="bp-l d-24 rad-1" style="background-image:url(https://picsum.photos/300?image=360)"></div>
  <div class="bp-c d-24 rad-1" style="background-image:url(https://picsum.photos/300?image=360)"></div>
  <div class="bp-r d-24 rad-1" style="background-image:url(https://picsum.photos/300?image=360)"></div>
  <div class="bp-lb d-24 rad-1" style="background-image:url(https://picsum.photos/300?image=360)"></div>
  <div class="bp-b d-24 rad-1" style="background-image:url(https://picsum.photos/300?image=360)"></div>
  <div class="bp-rb d-24 rad-1" style="background-image:url(https://picsum.photos/300?image=360)"></div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<BreakpointVariant class="bp-l" variant="bp-r" classPrefix="bp">
  ### Breakpoint variant
</BreakpointVariant>

<HoverVariant class="bp-l" variant="bp-r" classPrefix="bp">
  ### Hover variant
</HoverVariant>
