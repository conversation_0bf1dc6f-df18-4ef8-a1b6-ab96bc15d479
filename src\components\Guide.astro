---
import { frameworkIcons } from "@/constants/icons";

type FrameworkIconName = keyof typeof frameworkIcons;

type Entry = {
  icon?: FrameworkIconName;
  title: string;
  description: string;
  href: string;
};

const { entries = [] } = Astro.props as { entries: Entry[] };
---

<div class="d-g g-4 gtc-2 sm:gtc-3">
  {
    entries.map((entry) => (
      <a href={entry.href} class="bg-card p-4 td-none" style="color: light-dark(var(--sl-color-gray-3), var(--sl-color-gray-3));">
        {entry.icon && <div class="mr-2 d-8" set:html={frameworkIcons[entry.icon]} />}
        <h2 class="fs-xxl mt-8">{entry.title}</h2>
      </a>
    ))
  }
</div>

<style>
  .bg-card {
    background: var(--framework-card);
    border: 1px solid light-dark(var(--sl-color-gray-6), var(--ec-brdCol));
  }

  .bg-card:hover {
    border: 1px solid light-dark(var(--sl-color-accent-high), var(--sl-color-accent-high));
  }
</style>
