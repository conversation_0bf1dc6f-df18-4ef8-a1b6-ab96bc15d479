---
title: Badge
description: A component that displays a small piece of information or status indicator.
slug: docs/ui/badge
draft: true
---

## Examples

A basic implementation of the Badge component with default styling.

```tsx showLineNumbers
import { Badge } from "yumma-ui";

const App = () => {
  return (
    <Badge variant="base" size="xs">
      New
    </Badge>
  );
};

export default App;
```

## Usage

```tsx showLineNumbers
import { Badge } from "yumma-ui";
```

```tsx showLineNumbers
<Badge>New</Badge>
```

## API reference

The Badge component can be adapted to suit specific requirements by utilizing the internal API of Yumma UI, thereby enabling the modification of its visual appearance and operational characteristics.

### `variant`

> Default value is `base`

The `variant` property allows you to choose from different pre-defined styles that match your design system.

```ts
(property) variant?: "base" | "outline" | null | undefined
```

### `size`

> Default value is `xs`

The `size` property allows you to choose from different pre-defined sizes that match your design system.

```ts
(property) size?: "lg" | "xs" | "sm" | "md" | null | undefined
```

## Custom styling

The Badge component exposes a simple API to control its look and behavior through props.

```tsx showLineNumbers 'className="..."'
import { Badge } from "yumma-ui";

<Badge className="...">New</Badge>;
```

## Extend properties

The Badge component is fully extendable. It supports all native HTML `<span>` attributes through its props:

```tsx showLineNumbers "onClick={() => alert("Badge clicked!")}"
import { Badge } from "yumma-ui";

const App = () => {
  return (
    <Badge onClick={() => alert("Badge clicked!")} {...props}>
      New
    </Badge>
  );
};

export default App;
```
