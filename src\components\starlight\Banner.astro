---
const { banner } = Astro.locals.starlightRoute.entry.data;
---

{
  banner && (
    <div class="ai-c d-f fd-r fs-sm fw-500 h-12 jc-c p-st px-4 t-0 ta-c zi-0">
      <div class="i-0 n:zi-1 p-a rainbow" id="rainbow" />
      <div class="i-0 n:zi-1 p-a" />
      <a href="/blog/yummacss-3.1" class="td-none" style="color: var(--sl-color-white);">
        Yumma CSS v3.1.0 is now live! 🎉
      </a>
    </div>
  )
}

<style>
  @layer starlight.core {
    body {
      background-color: var(--sl-color-black);
    }

    .n\:zi-1 {
      z-index: -1;
    }

    @keyframes moving-banner {
      0% {
        background-position: 0% 0;
      }
      100% {
        background-position: 100% 0;
      }
    }

    #rainbow.rainbow {
      mask-image: linear-gradient(to bottom, white, transparent), radial-gradient(circle at top center, white, transparent);
      mask-composite: intersect;
      animation: moving-banner 16s linear infinite;
      --start: rgba(243, 10, 138);
      --mid: rgba(208, 175, 97);
      --end: rgba(2, 163, 232);
      --via: rgba(81, 104, 236);

      animation-direction: reverse;
      background-image: repeating-linear-gradient(
        60deg,
        var(--end),
        var(--start) 2%,
        var(--start) 5%,
        transparent 8%,
        transparent 14%,
        var(--via) 18%,
        var(--via) 22%,
        var(--mid) 28%,
        var(--mid) 30%,
        var(--via) 34%,
        var(--via) 36%,
        transparent,
        var(--end) calc(50% - 12px)
      );
      background-size: 200% 100%;
      mix-blend-mode: difference;
    }
  }
</style>
