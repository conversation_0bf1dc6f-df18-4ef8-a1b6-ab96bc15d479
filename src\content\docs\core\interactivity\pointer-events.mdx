---
title: Pointer Events
banner:
  content: N/A
description: Controls how an item responds to pointer events.
slug: docs/pointer-events
---

<Class category="interactivity" name="pointer-events" />

## Auto

> Initial value

This example sets the pointer events to **auto**. The element can receive pointer events as normal.

<Note icon="cursor_arrow">Try clicking the search emoji to see how the input reacts.</Note>

```html live "pe-auto"
<div>
  <label class="d-b fw-600 mb-1">Auto</label>
  <div class="p-r">
    <input class="b-2 pl-10 pr-4 py-2 rad-1 w-full" placeholder="Type something..." />
    <div class="ai-c d-f bo-0 l-0 t-0 p-a pe-auto pl-3">🔎</div>
  </div>
</div>
```

## None

This example sets the pointer events to **none**. The element won't receive any pointer events, meaning you can't click on it.

<Note icon="cursor_arrow">Try clicking the search emoji to see how the input reacts.</Note>

```html live "pe-none"
<div>
  <label class="d-b fw-600 mb-1">None</label>
  <div class="p-r">
    <input class="b-2 pl-10 pr-4 py-2 rad-1 w-full" placeholder="Type something..." />
    <div class="ai-c d-f bo-0 l-0 t-0 p-a pe-none pl-3">🔎</div>
  </div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<BreakpointVariant class="pe-none" variant="pe-auto" classPrefix="pe">
  ### Breakpoint variant
</BreakpointVariant>

<HoverVariant class="pe-none" variant="pe-auto" classPrefix="pe">
  ### Hover variant
</HoverVariant>
