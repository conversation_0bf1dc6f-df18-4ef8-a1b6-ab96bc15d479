---
title: Preact with Yumma CSS
banner:
  content: N/A
description: Integrate Yumma CSS into Preact applications.
slug: docs/guides/preact
---

## Creating a new project

To create a new Preact project, you need run the [Preact command](https://preactjs.com/guide/v10/getting-started/#create-a-vite-powered-preact-app) in your terminal.

<Steps>

    1. **Install Yumma CSS**

        Install `yummacss` using a package manager.

        <PackageManagers pkgManagers={["pnpm", "npm", "yarn"]} pkg="yummacss -D" title="Terminal" />

    2. **Initialize configuration**

            Create a configuration file in your project.

            <PackageManagers pkgManagers={["pnpm", "npm", "yarn"]} pkg="yummacss init" title="Terminal" type="dlx" />

    3. **Set up configuration**

        Specify the locations of all your project files in the config file.

        ```js title="yumma.config.js" mark={2-3}
        export default {
            source: ["./src/**/*.{ts,tsx}"],
            output: "./src/style.css",
            buildOptions: {
                reset: true,
                minify: false,
            }
        };
        ```

    4. **Build styles**

        You can now start generating your CSS with the [`build`](/docs/foundations/config/#build) or [`watch`](/docs/foundations/config/#watch) command.

        <PackageManagers pkgManagers={["pnpm", "npm", "yarn"]} pkg="yummacss build" title="Terminal" type="dlx" />

    5. **Done!**

        You're all set to start using Yumma CSS utility classes in your project.

        ```tsx title="index.tsx" mark={3-5}
        export function Home() {
            return (
                <div class="bg-indigo-12 d-g h-dvh pi-c tc-white">
                    <h1 class="fs-3xl fw-500">Yumma CSS ⚙️ Preact</h1>
                </div>
            );
        }
        ```

</Steps>

---

## Clone this project

Skip the guide steps entirely with our Preact starter.

```bash title="Cloning the project..."
git clone https://github.com/yumma-lib/with-preact.git
```
