---
authors: Renildo
cover:
  alt: Yumma CSS
  image: cover.png
date: 2024-01-03
description: After a few months of development, we're ready to release the first major version. In this release, we're focusing on implementing a few utility classes and, more importantly, on improving the overall experience of using Yumma CSS.
pagefind: false
slug: blog/yummacss-1.0
tags: ["release"]
title: Yumma CSS 1.0
---

After a few months of development, we're ready to release the first major version. In this release, we're focusing on implementing a few utility classes and, more importantly, on improving the overall experience of using Yumma CSS.

{/* excerpt */}

<ShowcaseYouTube
  entries={[
    {
      href: "https://youtube.com/watch?v=oxSSkVJMB7k",
      title: "What's new in Yumma CSS 1.0?",
    },
  ]}
/>

You may also want to take a look at some of the [release notes](https://github.com/yumma-lib/yumma-css/releases/tag/v1.0.0) but, anyway, these are the most noticeable shifts:

- [Utilities Expansion](#utilities-expansion): Margin X and Y, Padding X and Y, Align Content, and several more.
- [Viewport Expansion](#viewport-expansion): Support for viewports for Height and Width utilities.
- [New Color](#new-color): Introducing Lead Color.
- [Syntax Changes](#syntax-changes): Refactoring display and Hover utility classes.

This is a major update that introduces groundbreaking features. Major releases follow [semantic versioning](https://docs.npmjs.com/about-semantic-versioning) conventions. In other words, you probably need refactoring after upgrading.

---

## Utilities Expansion

### Align Items

This update is mostly about flexbox classes, which were a big limitation of Yumma CSS before. We've added a set of utilities to make Yumma CSS even more flexible.

<LegacyClass
  data={[
    {
      className: "ai-fs",
      properties: ["align-items: flex-start;"],
    },
    {
      className: "ai-fe",
      properties: ["align-items: flex-end;"],
    },
    {
      className: "jc-e",
      properties: ["justify-content: end;"],
    },
    {
      className: "jc-fs",
      properties: ["justify-content: flex-start;"],
    },
    {
      className: "jc-fe",
      properties: ["justify-content: flex-end;"],
    },
    {
      className: "jc-l",
      properties: ["justify-content: left;"],
    },
    {
      className: "jc-r",
      properties: ["justify-content: right;"],
    },
    {
      className: "jc-n",
      properties: ["justify-content: normal;"],
    },
    {
      className: "jc-s",
      properties: ["justify-content: stretch;"],
    },
  ]}
/>

### Layout

As the layouts of the applications change, we think it'd be good to add more control over the page. We've listed a few of them below:

#### Display

<LegacyClass
  data={[
    {
      className: "d-fr",
      properties: ["display: flow-root;"],
    },
  ]}
/>

#### Float

<LegacyClass
  data={[
    {
      className: "f-is",
      properties: ["float: inline-start;"],
    },
    {
      className: "f-ie",
      properties: ["float: inline-end;"],
    },
  ]}
/>

### Text Align

Yumma CSS v1.0 also introduces a whole new collection of advanced text formatting utilities for typographers.

<LegacyClass
  data={[
    {
      className: "ta-ja",
      properties: ["text-align: justify-all;"],
    },
    {
      className: "ta-mp",
      properties: ["text-align: match-parent;"],
    },
    {
      className: "tdl-o",
      properties: ["text-decoration-line: overline;"],
    },
  ]}
/>

---

## Viewport Expansion

We're also going to add viewports for the Height and Width utilities, which will be a huge help with responsive designs. We can't wait to roll it out to the other utilities!

```html
<div class="... h-10 md:h-auto md:w-auto w-10">...</div>
```

---

## New Color

We're pretty sure nobody builds websites in dark mode using gray, right? The new lead color is great for creating single-theme apps.

<LegacyPalette
  data={[
    {
      name: "Lead",
      color: "#3f3f4e",
    },
  ]}
/>

---

## Syntax Changes

We're making some changes to the syntax for the Display utilities. Here's how it's going to work from now on.

<Tabs>
  <TabItem icon="approve-check-circle" label="Now">
    ```html
    <div class="d-*"></div>
    ```
  </TabItem>
  <TabItem icon="close" label="Previously">
    ```html
    <div class="dis-*"></div>
    ```
  </TabItem>
</Tabs>

We're also going to tweak the syntax for the hover state classes. Basically, we're going to move them to the start of the utility `h:*` to make the code easier to understand.

<Tabs>
  <TabItem icon="approve-check-circle" label="Now">
    ```html
    <div class="h:bg-blue"></div>
    ```
  </TabItem>
  <TabItem icon="close" label="Previously">
    ```html
    <div class="bg-h-blue"></div>
    ```
  </TabItem>
</Tabs>

---

## Upgrade

You can upgrade your projects by getting the latest version of `yummacss` from npm:

<PackageManagers pkgManagers={["pnpm", "npm", "yarn"]} pkg="yummacss@latest" title="Terminal" />
