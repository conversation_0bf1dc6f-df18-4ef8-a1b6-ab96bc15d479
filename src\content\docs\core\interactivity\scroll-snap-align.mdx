---
title: <PERSON><PERSON> Snap Align
banner:
  content: N/A
description: Controls where the scroll snaps to align with an element.
slug: docs/scroll-snap-align
---

<Class category="interactivity" name="scroll-snap-align" />

## Center

This example sets the scroll snap align to **center**. The **ssa-c** utility ensures that the element will snap to the center of the scroll container when scrolling.

<Note icon="cursor_arrow">Try scrolling through the image container to see how it behaves.</Note>

```html live "ssa-c" layout="/src/layouts/inline.astro"
<div class="o-h pb-4 p-r">
  <div class="d-f g-6 o-x-s pb-4 sst-x-m">
    <div class="fs-0 p-r ssa-c">
      <div class="h-40 rad-1 w-80" id="area"></div>
    </div>
    <div class="fs-0 p-r ssa-c">
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=849" />
    </div>
    <div class="fs-0 p-r ssa-c">
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=866" />
    </div>
    <div class="fs-0 p-r ssa-c">
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=872" />
    </div>
    <div class="fs-0 p-r ssa-c">
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=875" />
    </div>
    <div class="fs-0 p-r ssa-c">
      <div class="h-40 rad-1 w-80" id="area"></div>
    </div>
  </div>
</div>
```

## End

This example sets the scroll snap align to **end**. The **ssa-e** utility ensures that the element will snap to the end of the scroll container when scrolling.

<Note icon="cursor_arrow">Try scrolling through the image container to see how it behaves.</Note>

```html live "ssa-e" layout="/src/layouts/inline.astro"
<div class="o-h pb-4 p-r">
  <div class="d-f g-6 o-x-s pb-4 sst-x-m">
    <div class="fs-0 p-r ssa-e">
      <div class="h-40 rad-1 w-80" id="area"></div>
    </div>
    <div class="fs-0 p-r ssa-e">
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=849" />
    </div>
    <div class="fs-0 p-r ssa-e">
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=866" />
    </div>
    <div class="fs-0 p-r ssa-e">
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=872" />
    </div>
    <div class="fs-0 p-r ssa-e">
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=875" />
    </div>
    <div class="fs-0 p-r ssa-e">
      <div class="h-40 rad-1 w-80" id="area"></div>
    </div>
  </div>
</div>
```

## Start

This example sets the scroll snap align to **start**. The **ssa-s** utility ensures that the element will snap to the start of the scroll container when scrolling.

<Note icon="cursor_arrow">Try scrolling through the image container to see how it behaves.</Note>

```html live "ssa-s" layout="/src/layouts/inline.astro"
<div class="o-h pb-4 p-r">
  <div class="d-f g-6 o-x-s pb-4 sst-x-m">
    <div class="fs-0 p-r ssa-s">
      <div class="h-40 rad-1 w-80" id="area"></div>
    </div>
    <div class="fs-0 p-r ssa-s">
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=849" />
    </div>
    <div class="fs-0 p-r ssa-s">
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=866" />
    </div>
    <div class="fs-0 p-r ssa-s">
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=872" />
    </div>
    <div class="fs-0 p-r ssa-s">
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=875" />
    </div>
    <div class="fs-0 p-r ssa-s">
      <div class="h-40 rad-1 w-80" id="area"></div>
    </div>
  </div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/foundations/responsive-design/) or other factors, such as [hover states](/docs/foundations/variants/).

<BreakpointVariant class="ssa-s" variant="ssa-e" classPrefix="ssa">
  ### Breakpoint variant
</BreakpointVariant>

<HoverVariant class="ssa-s" variant="ssa-e" classPrefix="ssa">
  ### Hover variant
</HoverVariant>
