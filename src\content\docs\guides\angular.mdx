---
title: Angular with Yumma CSS
banner:
  content: N/A
description: Integrate Yumma CSS into Angular applications.
slug: docs/guides/angular
---

## Creating a new project

To create a new Angular project, you need to have the [Angular CLI](https://angular.dev/installation#install-angular-cli) installed on your machine.

<Steps>

    1. **Install Yumma CSS**

        Install `yummacss` using a package manager.

        <PackageManagers pkgManagers={["pnpm", "npm", "yarn"]} pkg="yummacss -D" title="Terminal" />

        <br />

    2. **Initialize configuration**

            Create a configuration file in your project.

            <PackageManagers pkgManagers={["pnpm", "npm", "yarn"]} pkg="yummacss init" title="Terminal" type="dlx" />

    3. **Set up configuration**

        Specify the locations of all your project files in the config file.

        ```js title="yumma.config.js" mark={2-3}
        module.exports = {
            source: ["./src/**/*.{html,ts}"],
            output: "./src/styles.css",
            buildOptions: {
                reset: true,
                minify: false,
            }
        };
        ```

    4. **Build styles**

        You can now start generating your CSS with the [`build`](/docs/foundations/config/#build) or [`watch`](/docs/foundations/config/#watch) command.

        <PackageManagers pkgManagers={["pnpm", "npm", "yarn"]} pkg="yummacss build" title="Terminal" type="dlx" />

    5. **Done!**

        You're all set to start using Yumma CSS utility classes in your project.

        ```html title="app.component.html" mark={1-3}
        <div class="bg-indigo-12 d-g h-dvh pi-c tc-white">
            <h1 class="fs-3xl fw-500">Yumma CSS ⚙️ Angular</h1>
        </div>

        <router-outlet />
        ```

</Steps>

---

## Clone this project

Skip the guide steps entirely with our Angular starter.

```bash title="Cloning the project..."
git clone https://github.com/yumma-lib/with-angular.git
```
