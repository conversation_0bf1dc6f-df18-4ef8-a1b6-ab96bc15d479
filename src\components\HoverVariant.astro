---
import { Code } from "@astrojs/starlight/components";
import Note from "./Note.astro";

interface Props {
  class: string;
  classPrefix: string;
  variant: string;
}

const { class: baseClass, classPrefix, variant } = Astro.props as Props;
---

<section>
  <Note icon="cursor_arrow">
    <slot class="ai-c" />
  </Note>

  <p>
    Alternatively, you can apply <code>:hover</code> by using <code>h:{classPrefix}-*</code> utility to override elements and change their values when hovering over them.
  </p>

  <Code code={`<div class="${baseClass} h:${variant} ..."></div>`} lang="html" mark={`h:${variant}`} />
</section>
