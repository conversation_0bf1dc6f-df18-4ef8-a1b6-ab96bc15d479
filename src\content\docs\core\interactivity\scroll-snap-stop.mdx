---
title: <PERSON><PERSON> Snap Stop
banner:
  content: N/A
description: Controls whether the scroll container passes over possible snap positions.
slug: docs/scroll-snap-stop
---

<Class category="interactivity" name="scroll-snap-stop" />

## Always

This example sets the scroll snap stop to **always**. The **sss-a** utility ensures that the scroll will always stop at the snap position.

<Note icon="cursor_arrow">Try scrolling through the image container to see how it behaves.</Note>

```html live "sss-a" layout="/src/layouts/inline.astro"
<div class="o-h pb-4 p-r">
  <div class="d-f g-6 o-x-s pb-4 sst-x-m">
    <div class="fs-0 p-r ssa-c">
      <div class="h-40 rad-1 w-80" id="area"></div>
    </div>
    <div class="fs-0 p-r ssa-c sss-a">
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=849" />
    </div>
    <div class="fs-0 p-r ssa-c sss-a">
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=866" />
    </div>
    <div class="fs-0 p-r ssa-c sss-a">
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=872" />
    </div>
    <div class="fs-0 p-r ssa-c sss-a">
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=875" />
    </div>
    <div class="fs-0 p-r ssa-c sss-a">
      <div class="h-40 rad-1 w-80" id="area"></div>
    </div>
  </div>
</div>
```

## Normal

> Initial value

This example sets the scroll snap stop to **normal**. The **sss-n** utility allows the scroll to stop at the snap position only when the user scrolls slowly, providing a more flexible scrolling experience.

<Note icon="cursor_arrow">Try scrolling through the image container to see how it behaves.</Note>

```html live "sss-n" layout="/src/layouts/inline.astro"
<div class="o-h pb-4 p-r">
  <div class="d-f g-6 o-x-s pb-4 sst-x-m">
    <div class="fs-0 p-r ssa-c">
      <div class="h-40 rad-1 w-80" id="area"></div>
    </div>
    <div class="fs-0 p-r ssa-c sss-n">
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=849" />
    </div>
    <div class="fs-0 p-r ssa-c sss-n">
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=866" />
    </div>
    <div class="fs-0 p-r ssa-c sss-n">
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=872" />
    </div>
    <div class="fs-0 p-r ssa-c sss-n">
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=875" />
    </div>
    <div class="fs-0 p-r ssa-c sss-n">
      <div class="h-40 rad-1 w-80" id="area"></div>
    </div>
  </div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/foundations/responsive-design/) or other factors, such as [hover states](/docs/foundations/variants/).

<BreakpointVariant class="sns-a" variant="sns-n" classPrefix="sns">
  ### Breakpoint variant
</BreakpointVariant>

<HoverVariant class="sns-a" variant="sns-n" classPrefix="sns">
  ### Hover variant
</HoverVariant>
