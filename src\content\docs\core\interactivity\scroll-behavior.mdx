---
title: <PERSON><PERSON> Behavior
banner:
  content: N/A
description: Controls the scroll behavior of an element.
slug: docs/scroll-behavior
---

<Class category="interactivity" name="scroll-behavior" />

## Auto

> Initial value

This example sets the scroll behavior to **auto**. The **sb-auto** utility allows the default scrolling behavior, which is immediate and without any animation.

```html "sb-auto" layout="/src/layouts/inline.astro"
<div class="sb-auto ..."></div>
```

## Smooth

This example sets the scroll behavior to **smooth**. The **sb-s** utility enables a smooth scrolling effect, providing a gradual transition when scrolling to a different part of the page.

```html "sb-s" layout="/src/layouts/inline.astro"
<div class="sb-s ..."></div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/foundations/responsive-design/) or other factors, such as [hover states](/docs/foundations/variants/).

<BreakpointVariant class="sb-auto" variant="sb-s" classPrefix="sb">
  ### Breakpoint variant
</BreakpointVariant>

<HoverVariant class="sb-auto" variant="sb-s" classPrefix="sb">
  ### Hover variant
</HoverVariant>
