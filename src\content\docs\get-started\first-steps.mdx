---
title: First Steps
banner:
  content: N/A
description: Get familiar with the Yumma CSS utility classes and how to use them in your projects.
slug: docs/first-steps
---

## Get started with Yumma CLI

Yumma CLI scans and compiles your template files into a single CSS file while eliminating dead styles, making your content load faster.

<PackageManagers pkgManagers={["pnpm", "npm", "yarn"]} type="exec" pkg="yummacss" args="init" title="Terminal" />

<Tabs>
  <TabItem label="ES Modules">
    For `esm` use the following configuration:

    ```js title="yumma.config.js"
    export default {
      source: ["..."],
      output: "...",
      buildOptions: {
        reset: true,
        minify: false,
      },
    };
    ```

    Add the following to your `package.json` file:

    ```json title="package.json"
    {
      "type": "module"
    }
    ```

  </TabItem>

  <TabItem label="Common Js">
    For `cjs` use the following configuration:

    ```js title="yumma.config.js"
    module.exports = {
      source: ["..."],
      output: "...",
      buildOptions: {
        reset: true,
        minify: false,
      },
    };
    ```

  </TabItem>
</Tabs>

## Set up configuration

Suit your project needs by creating a `yumma.config.js` configuration file where you can set up the following options:

### `source`

A collection of paths for your template files, such as `.js`, `.tsx`, or `.html` files. This is where the CLI will look for utility classes to include in the final CSS file.

<Note icon="info">Use [glob patterns](https://code.visualstudio.com/docs/editor/glob-patterns#_glob-pattern-syntax) to include subfolders and specific file types.</Note>

```js title="yumma.config.js" {2}
export default {
  source: ["./src/**/*.html"],
};
```

### `output`

A string that specifies the output path for the compiled CSS file. This is where the CLI will save the final CSS file after processing your template files.

```js title="yumma.config.js"
export default {
  output: "./src/styles.css",
};
```

### `buildOptions.reset`

A boolean that determines whether the inclusion of the [Stylecent reset system](/docs/stylecent).

```js title="yumma.config.js" mark={3}
export default {
  buildOptions: {
    reset: true, // default: true
  },
};
```

### `buildOptions.minify`

A boolean that determines whether the compiled CSS files should or not be minified. This is useful for production builds where you want to reduce the file size for faster loading times.

```js title="yumma.config.js" mark={3}
export default {
  buildOptions: {
    minify: true, // default: false
  },
};
```

---

## Styling elements

Yumma CSS is a utility-first CSS framework that allows you to style your elements using utility classes. This means you can apply styles directly in your HTML, making it easy to create responsive designs without writing custom CSS.

### Styling with cascade

Here's an example of how you can style a button element using the cascade:

```html live
<style>
  .button {
    background-color: #595cd9;
    border-radius: 0.25rem;
    border-width: 0px;
    color: white;
    cursor: pointer;
    font-weight: 600;
    padding-bottom: 0.25rem;
    padding-left: 1.25rem;
    padding-right: 1.25rem;
    padding-top: 0.25rem;
  }

  .button:hover {
    background-color: #40429c;
  }
</style>

<button class="button">Subscribe</button>
```

### Styling with Yumma CSS

Yumma CSS is a pretty abstract framework that's closely aligned with Vanilla CSS. It uses an abbreviated naming convention, which keeps your inline-classes nice and short.

Plus, it makes it easier to maintain and scale without any guesswork.

{/* prettier-ignore */}
```html live
<button class="bg-indigo rad-1 b-0 tc-white c-p fw-600 py-1 px-5 h:bg-indigo-8">
  Subscribe
</button>
```

Since each utility will have a specific style property, you can reuse it over and over again, making your design system consistent.

#### Quick example reference

Here's a quick reference for the examples above.

| Property                                              | Utility |
| ----------------------------------------------------- | ------- |
| [background-color](/docs/background-color)            | `bg-*`  |
| [border-radius](/docs/border-radius)                  | `rad-*` |
| [border-width](/docs/border-width)                    | `b-*`   |
| [color](/docs/text-color)                             | `tc-*`  |
| [cursor](/docs/cursor)                                | `c-*`   |
| [font-weight](/docs/font-weight)                      | `fw-*`  |
| [padding-y](/docs/padding#padding-y) — (bottom & top) | `py-*`  |
| [padding-x](/docs/padding#padding-x) — (left & right) | `px-*`  |
| [hover](/docs/first-steps#hover-modifiers)            | `h:*`   |

---

## Color System

Yumma CSS comes with a built-in color system that allows you to easily customize the colors of your elements.

Here's an example of how to use the color system to style a button:

```html live "bg-indigo-4" "bg-indigo-9"
<div class="d-g cg-4 gtc-2">
  <button class="b-0 bg-indigo-4 fw-600 px-5 py-1 rad-1 tc-black">Subscribe</button>
  <button class="b-0 bg-indigo-9 fw-600 px-5 py-1 rad-1 tc-white">Subscribe</button>
</div>
```

### Color palette

Here's a range of all the default colors from the Yumma CSS theme.

<Palette
  data={[
    { name: "Red", color: "rgb(215, 61, 61)" },
    { name: "Orange", color: "rgb(224, 104, 20)" },
    { name: "Yellow", color: "rgb(211, 161, 7)" },
    { name: "Green", color: "rgb(31, 177, 85)" },
    { name: "Teal", color: "rgb(18, 166, 149)" },
    { name: "Cyan", color: "rgb(5, 164, 191)" },
    { name: "Blue", color: "rgb(53, 117, 221)" },
    { name: "Indigo", color: "rgb(89, 92, 217)" },
    { name: "Violet", color: "rgb(125, 83, 221)" },
    { name: "Pink", color: "rgb(212, 65, 138)" },
    { name: "Lead", color: "rgb(63, 63, 78)" },
    { name: "Gray", color: "rgb(96, 103, 115)" },
    { name: "Silver", color: "rgb(191, 194, 199)" },
  ]}
/>

---

## Building styles

The Yumma CSS CLI provides several commands to help you work with your Yumma CSS files.

<Note icon="info">
  Make sure you have a [`yumma.config.js`](/docs/first-steps#get-started-with-yumma-cli) file set up before running these commands. If you haven't, run `npx yummacss init` first.
</Note>

### Build command

The `build` command will compile your Yumma CSS files once.

<PackageManagers pkgManagers={["pnpm", "npm", "yarn"]} type="exec" pkg="yummacss" args="build" title="Terminal" />

### Watch command

The `watch` command will watch for changes in your Yumma CSS files and recompile them automatically.

<PackageManagers pkgManagers={["pnpm", "npm", "yarn"]} type="exec" pkg="yummacss" args="watch" title="Terminal" />

---

## Conditional styles

Yumma CSS provides a set of modifiers that allow you to apply styles conditionally based on the context in which they are used. This is particularly useful for creating responsive designs and handling hover states.

### Breakpoint modifiers

Use responsive breakpoints such as `sm:*`, `md:*`, `lg:*`, `xl:*` and `xxl:*` to apply specific rules to different screen sizes. For example:

<Note icon="width">Try changing the size of the preview area to see how the text changes.</Note>

```html live "sm:d-b" "md:d-b" "lg:d-b" "xl:d-b" "xxl:d-b" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1 ta-c tc-lead">
  <p class="bg-white d-b p-4 rad-1 sm:d-none">This is a small screen.</p>
  <p class="bg-white d-none md:d-none p-4 rad-1 sm:d-b">This is a medium screen.</p>
  <p class="bg-white d-none lg:d-none md:d-b p-4 rad-1">This is a large screen.</p>
  <p class="bg-white d-none lg:d-b p-4 rad-1 ta-c xl:d-none">This is an extra large screen.</p>
  <p class="bg-white d-none p-4 rad-1 ta-c xxl:d-b">This is double extra large screen.</p>
</div>
```

#### Breakpoints reference

Here's a reference for the breakpoints used in Yumma CSS.

| Designation | Minimum width    |
| ----------- | ---------------- |
| sm          | 40rem (_640px_)  |
| md          | 48rem (_768px_)  |
| lg          | 64rem (_1024px_) |
| xl          | 80rem (_1280px_) |
| xxl         | 96rem (_1536px_) |

### Hover modifiers

You can use the `h:` modifiers in order to target the `:hover` function across the entirety of the Yumma CSS utility classes.

To illustrate, when overriding an existing utility class conditionally, ensure to utilize the `h:*` modifier, followed by the designated class to be applied on hover.

<Note icon="cursor_arrow">Try moving the mouse over the button to see how the background color changes.</Note>

```html live "h:bg-indigo-8"
<button class="b-0 bg-indigo fw-600 h:bg-indigo-8 px-5 py-1 rad-1 tc-white c-p">Subscribe</button>
```
