---
title: Isolation
banner:
  content: N/A
description: Controls whether an element must create a new stacking context.
slug: docs/isolation
---

<Class category="positioning" name="isolation" />

## Auto

> Initial value

This example sets the isolation to **auto**. The element will use the default isolation behavior, allowing overlapping elements to blend together.

```html "i-auto"
<div class="i-auto ..."></div>
```

## Isolate

This example sets the isolation to **isolate**. The element will create a new stacking context, preventing overlapping elements from blending with the background.

```html "i-i"
<div class="i-i ..."></div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<MediaModifier class="i-auto" mediaModifier="i-i" classPrefix="i">
  ### Breakpoint modifier
</MediaModifier>

<HoverModifier class="i-auto" hoverModifier="i-i" classPrefix="i">
  ### Hover modifier
</HoverModifier>
