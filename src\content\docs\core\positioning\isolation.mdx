---
title: Isolation
banner:
  content: N/A
description: Controls whether an element must create a new stacking context.
slug: docs/isolation
---

<Class category="positioning" name="isolation" />

## Auto

> Initial value

This example sets the isolation to **auto**. The element will use the default isolation behavior, allowing overlapping elements to blend together.

```html "i-auto"
<div class="i-auto ..."></div>
```

## Isolate

This example sets the isolation to **isolate**. The element will create a new stacking context, preventing overlapping elements from blending with the background.

```html "i-i"
<div class="i-i ..."></div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/foundations/responsive-design/) or other factors, such as [hover states](/docs/foundations/variants/).

<BreakpointVariant class="i-auto" variant="i-i" classPrefix="i">
  ### Breakpoint variant
</BreakpointVariant>

<HoverVariant class="i-auto" variant="i-i" classPrefix="i">
  ### Hover variant
</HoverVariant>
