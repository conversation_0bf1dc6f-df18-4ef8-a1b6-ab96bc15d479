import { pluginCollapsibleSections } from "@expressive-code/plugin-collapsible-sections";
import { pluginLineNumbers } from '@expressive-code/plugin-line-numbers'
import { group } from "./src/plugins/group";

/** @type {import('@astrojs/starlight/expressive-code').StarlightExpressiveCodeOptions} */
export default {
  plugins: [pluginCollapsibleSections(), pluginLineNumbers(), group()],
  defaultProps: { collapseStyle: "collapsible-start", showLineNumbers: false },
};
