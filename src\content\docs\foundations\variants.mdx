---
title: Variants
banner:
  content: N/A
description: Learn how to use variants in Yumma CSS.
slug: docs/foundations/variants
---

## Pseudo-classes

By default, all base utility classes can be used with pseudo-class variants, making it easier to create interactive and accessible UI's.

### Active

The `a:` variants allow you to target the `:active` state, which is triggered when an element is being actively pressed or clicked. Perfect for creating responsive button interactions.

<Note icon="cursor_arrow">Try clicking and holding the button to see the active state.</Note>

```html live "a:bg-indigo-9"
<button class="bg-indigo h:bg-indigo-7 a:bg-indigo-9 px-3 py-1 rad-1 tc-white">Press me!</button>
```

### Focus

Use the `f:` variants to target the `:focus` state across all Yumma CSS utility classes. This is particularly useful for creating accessible form elements and interactive components.

<Note icon="cursor_arrow">Try clicking on the input field to see how the border color and outline change.</Note>

```html live "f:oc-indigo-5 f:oo-2 f:os-s f:ow-2"
<button class="bg-indigo f:oc-indigo-5 f:oo-2 f:os-s f:ow-2 h:bg-indigo-7 px-3 py-1 rad-1 tc-white">Focus me!</button>
```

### Hover

You can use the `h:` variants in order to target the `:hover` function across the entirety of the Yumma CSS utility classes.

<Note icon="cursor_arrow">Try moving the mouse over the button to see how the background color changes.</Note>

```html live "h:bg-indigo-7"
<button class="bg-indigo h:bg-indigo-7 px-3 py-1 rad-1 tc-white c-p">Hover me!</button>
```

Here's a reference for all the pseudo-variants available in Yumma CSS.

| Pseudo-class | Prefix | Description                               |
| ------------ | ------ | ----------------------------------------- |
| `:active`    | `a:*`  | Applied when the element is being pressed |
| `:focus`     | `f:*`  | Applied when the element receives focus   |
| `:hover`     | `h:*`  | Applied when the element is hovered       |
