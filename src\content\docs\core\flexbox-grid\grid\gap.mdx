---
title: Gap
banner:
  content: N/A
description: Controls gaps between grid and flexbox elements.
slug: docs/gap
---

<Class category="grid" name="gap" />

This example sets the gap to **2.5rem**. The element will have uniform spacing of **2.5rem** between its rows and columns.

```html live "g-10" layout="/src/layouts/inline.astro"
<div class="g-10 d-g gtc-3 tc-white" id="area">
  <div class="ai-c bg-indigo d-f jc-c p-4 rad-1">A</div>
  <div class="ai-c bg-indigo d-f jc-c p-4 rad-1">B</div>
  <div class="ai-c bg-indigo d-f jc-c p-4 rad-1">C</div>
  <div class="ai-c bg-indigo d-f jc-c p-4 rad-1">D</div>
  <div class="ai-c bg-indigo d-f jc-c p-4 rad-1">E</div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<BreakpointVariant class="g-1" variant="g-2" classPrefix="g">
  ### Breakpoint variant
</BreakpointVariant>

<HoverVariant class="g-1" variant="g-2" classPrefix="g">
  ### Hover variant
</HoverVariant>
