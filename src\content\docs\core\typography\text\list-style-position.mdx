---
title: List Style Position
banner:
  content: N/A
description: Controls how bullets and numbers are positioned in lists.
slug: docs/list-style-position
---

<Class category="text" name="list-style-position" />

## Inside

This example sets the list style position to **inside**. The list item markers will be placed inside the list item's content area, affecting the text alignment.

```html live "lsp-i" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <ul class="bg-white lsp-i lst-d p-4 pl-6 rad-1">
    <li class="tc-lead">Butter 🧈</li>
    <li class="tc-lead">Egg 🥚</li>
    <li class="tc-lead">Milk 🥛</li>
  </ul>
</div>
```

## Outside

> Initial value

This example sets the list style position to **outside**. The list item markers will be placed outside the list item's content area, allowing the text to align normally.

```html live "lsp-o" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <ul class="bg-white lsp-o lst-d p-4 pl-6 rad-1">
    <li class="tc-lead">Butter 🧈</li>
    <li class="tc-lead">Egg 🥚</li>
    <li class="tc-lead">Milk 🥛</li>
  </ul>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<BreakpointVariant class="lsp-o" variant="lsp-i" classPrefix="lsp">
  ### Breakpoint variant
</BreakpointVariant>

<HoverVariant class="lsp-o" variant="lsp-i" classPrefix="lsp">
  ### Hover variant
</HoverVariant>
