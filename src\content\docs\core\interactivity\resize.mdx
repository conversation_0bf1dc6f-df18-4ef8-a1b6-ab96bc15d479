---
title: Resize
banner:
  content: N/A
description: Controls the way an item can be resized.
slug: docs/resize
---

<Class category="interactivity" name="resize" />

## Both

This example sets the resize property to **both**. The **r-b** utility allows the element to be resized both horizontally and vertically.

<Note icon="size">Try resizing the text area to see how it changes in size.</Note>

```html live "r-b"
<div class="d-f fd-c">
  <label class="mb-2">Write a message:</label>
  <textarea class="b-1 max-h-50 max-w-100 rad-1 r-b" rows="4"></textarea>
</div>
```

## Horizontal

This example sets the resize property to **horizontal**. The **r-h** utility allows the element to be resized only in the horizontal direction.

<Note icon="size">Try resizing the text area to see how it changes in size.</Note>

```html live "r-h"
<div class="d-f fd-c">
  <label class="mb-2">Write a message:</label>
  <textarea class="b-1 max-w-100 rad-1 r-h" rows="4"></textarea>
</div>
```

## None

> Initial value

This example sets the resize property to **none**. The **r-none** utility prevents the element from being resized by the user.

<Note icon="size">Try resizing the text area to see how it changes in size.</Note>

```html live "r-none"
<div class="d-f fd-c">
  <label class="mb-2">Write a message:</label>
  <textarea class="b-1 rad-1 r-none" rows="4"></textarea>
</div>
```

## Vertical

This example sets the resize property to **vertical**. The **r-v** utility allows the element to be resized only in the vertical direction.

<Note icon="size">Try resizing the text area to see how it changes in size.</Note>

```html live "r-v"
<div class="d-f fd-c">
  <label class="mb-2">Write a message:</label>
  <textarea class="b-1 max-h-50 rad-1 r-v" rows="4"></textarea>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<BreakpointVariant class="r-none" variant="r-h" classPrefix="r">
  ### Breakpoint variant
</BreakpointVariant>

<HoverVariant class="r-none" variant="r-h" classPrefix="r">
  ### Hover variant
</HoverVariant>
