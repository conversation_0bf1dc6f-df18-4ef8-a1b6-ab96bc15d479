---
title: Text Underline Offset
banner:
  content: N/A
description: Controls offsets of an underline text decoration line.
slug: docs/text-underline-offset
---

<Class category="text" name="text-underline-offset" />

## Auto

> Initial value

This example sets the text underline offset to **auto**. The browser will determine the appropriate offset for the underline based on the font and styling.

```html live "tuo-auto" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg p-4 rad-1 ta-c tc-lead td-u tuo-auto">Sphinx of black quartz, judge my vow.</p>
</div>
```

## 0

This example sets the text underline offset to **0px**. The underline will be positioned directly under the text with no additional offset.

```html live "tuo-0" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg p-4 rad-1 ta-c tc-lead td-u tuo-0">Sphinx of black quartz, judge my vow.</p>
</div>
```

## 1

This example sets the text underline offset to **1px**. The underline will be positioned 1 pixel below the text.

```html live "tuo-1" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg p-4 rad-1 ta-c tc-lead td-u tuo-1">Sphinx of black quartz, judge my vow.</p>
</div>
```

## 2

This example sets the text underline offset to **2px**. The underline will be positioned 2 pixels below the text.

```html live "tuo-2" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg p-4 rad-1 ta-c tc-lead td-u tuo-2">Sphinx of black quartz, judge my vow.</p>
</div>
```

## 4

This example sets the text underline offset to **4px**. The underline will be positioned 4 pixels below the text.

```html live "tuo-4" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg p-4 rad-1 ta-c tc-lead td-u tuo-4">Sphinx of black quartz, judge my vow.</p>
</div>
```

## 8

This example sets the text underline offset to **8px**. The underline will be positioned 8 pixels below the text.

```html live "tuo-8" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg p-4 rad-1 ta-c tc-lead td-u tuo-8">Sphinx of black quartz, judge my vow.</p>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<BreakpointVariant class="tuo-1" variant="tuo-2" classPrefix="tuo">
  ### Breakpoint variant
</BreakpointVariant>

<HoverVariant class="tuo-1" variant="tuo-2" classPrefix="tuo">
  ### Hover variant
</HoverVariant>
