---
title: Justify Content
banner:
  content: N/A
description: Controls the positioning of flex and grid elements on a container's main axis.
slug: docs/justify-content
---

<Class category="flexbox" name="justify-content" />

## Center

This example sets the justify content to **center**. The items will be centered within the container along the main axis.

```html live "jc-c" layout="/src/layouts/inline.astro"
<div class="d-f g-4 jc-c tc-white" id="area">
  <div class="ai-c bg-indigo d-f d-14 jc-c p-4 rad-1">A</div>
  <div class="ai-c bg-indigo d-f d-14 jc-c p-4 rad-1">B</div>
  <div class="ai-c bg-indigo d-f d-14 jc-c p-4 rad-1">C</div>
</div>
```

## Flex End

This example sets the justify content to **flex-end**. The items will be aligned to the end of the container along the main axis.

```html live "jc-fe" layout="/src/layouts/inline.astro"
<div class="d-f g-4 jc-fe tc-white" id="area">
  <div class="ai-c bg-indigo d-f d-14 jc-c p-4 rad-1">A</div>
  <div class="ai-c bg-indigo d-f d-14 jc-c p-4 rad-1">B</div>
  <div class="ai-c bg-indigo d-f d-14 jc-c p-4 rad-1">C</div>
</div>
```

## Flex Start

This example sets the justify content to **flex-start**. The items will be aligned to the start of the container along the main axis.

```html live "jc-fs" layout="/src/layouts/inline.astro"
<div class="d-f g-4 jc-fs tc-white" id="area">
  <div class="ai-c bg-indigo d-f d-14 jc-c p-4 rad-1">A</div>
  <div class="ai-c bg-indigo d-f d-14 jc-c p-4 rad-1">B</div>
  <div class="ai-c bg-indigo d-f d-14 jc-c p-4 rad-1">C</div>
</div>
```

## Normal

> Initial value

This example sets the justify content to **normal**. The items will be aligned according to the default alignment of the container.

```html live "jc-n" layout="/src/layouts/inline.astro"
<div class="d-f g-4 jc-n tc-white" id="area">
  <div class="ai-c bg-indigo d-f d-14 jc-c p-4 rad-1">A</div>
  <div class="ai-c bg-indigo d-f d-14 jc-c p-4 rad-1">B</div>
  <div class="ai-c bg-indigo d-f d-14 jc-c p-4 rad-1">C</div>
</div>
```

## Stretch

This example sets the justify content to **stretch**. The items will stretch to fill the available space along the main axis.

```html live "jc-s" layout="/src/layouts/inline.astro"
<div class="rad-1 tc-white">
  <div class="d-g g-4 gaf-c jc-s" id="area">
    <div class="ai-c bg-indigo d-f h-14 jc-c rad-1">A</div>
    <div class="ai-c bg-indigo d-f h-14 jc-c rad-1">B</div>
    <div class="ai-c bg-indigo d-f h-14 jc-c rad-1">C</div>
  </div>
</div>
```

## Space Between

This example sets the justify content to **space-between**. The items will be evenly distributed in the container, with the first item at the start and the last item at the end.

```html live "jc-sb" layout="/src/layouts/inline.astro"
<div class="d-f g-4 jc-sb tc-white" id="area">
  <div class="ai-c bg-indigo d-f d-14 jc-c p-4 rad-1">A</div>
  <div class="ai-c bg-indigo d-f d-14 jc-c p-4 rad-1">B</div>
  <div class="ai-c bg-indigo d-f d-14 jc-c p-4 rad-1">C</div>
</div>
```

## Space Around

This example sets the justify content to **space-around**. The items will be evenly distributed in the container with space around them.

```html live "jc-sa" layout="/src/layouts/inline.astro"
<div class="d-f g-4 jc-sa tc-white" id="area">
  <div class="ai-c bg-indigo d-f d-14 jc-c p-4 rad-1">A</div>
  <div class="ai-c bg-indigo d-f d-14 jc-c p-4 rad-1">B</div>
  <div class="ai-c bg-indigo d-f d-14 jc-c p-4 rad-1">C</div>
</div>
```

## Space Evenly

This example sets the justify content to **space-evenly**. The items will be evenly distributed in the container with equal space around them.

```html live "jc-se" layout="/src/layouts/inline.astro"
<div class="d-f g-4 gaf-c jc-se tc-white" id="area">
  <div class="ai-c bg-indigo d-f d-14 jc-c p-4 rad-1">A</div>
  <div class="ai-c bg-indigo d-f d-14 jc-c p-4 rad-1">B</div>
  <div class="ai-c bg-indigo d-f d-14 jc-c p-4 rad-1">C</div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<MediaModifier class="jc-fs" mediaModifier="jc-fe" classPrefix="jc">
  ### Media modifier
</MediaModifier>

<HoverModifier class="jc-fs" hoverModifier="jc-fe" classPrefix="jc">
  ### Hover modifier
</HoverModifier>
