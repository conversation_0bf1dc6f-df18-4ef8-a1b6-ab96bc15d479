---
import tinycolor from "tinycolor2";

interface ColorItem {
  name: string;
  color: string;
}

interface Props {
  data: ColorItem[];
  percentage?: number;
}

const { data, percentage = 10 } = Astro.props as Props;

const mixColors = (color1: string, color2: string, weight: number): string => {
  return tinycolor.mix(color1, color2, weight).toHexString();
};

const generateShades = (color: string): string[] => {
  const shades: string[] = [];
  for (let i = 6; i >= 1; i--) {
    shades.push(mixColors(color, "white", i * percentage));
  }

  shades.push(color);
  for (let i = 1; i <= 6; i++) {
    shades.push(mixColors(color, "black", i * percentage));
  }

  return shades;
};
---

<div class="d-f fd-c">
  <div class="d-none md:d-g fs-xs ta-c gtc-14">
    <div></div>
    {Array.from({ length: 13 }, (_, i) => <div>{i === 6 ? "Base" : i < 6 ? 6 - i : i - 6}</div>)}
  </div>

  {
    data.map((colorItem) => {
      const shades = generateShades(colorItem.color);
      return (
        <div class="d-g g-1 ai-c md:gtc-14 md:f-none d-f fd-c md:d-g">
          <div class="fs-sm ws-nw md:wm-vtb md:fs-sm ta-c fs-md ws-n">{colorItem.name}</div>
          {shades.map((shade) => (
            <div class="h-12 md:h-10 w-full" style={{ backgroundColor: shade }} onclick={`navigator.clipboard.writeText('${shade}')`} />
          ))}
        </div>
      );
    })
  }
</div>

<!-- TODO - yummacss@v3.1 -->
<style>
  @media (width >= 48rem) {
    .md\:wm-vtb {
      writing-mode: vertical-lr;
    }
  }

  .wm-htb {
    writing-mode: horizontal-tb;
  }
</style>
