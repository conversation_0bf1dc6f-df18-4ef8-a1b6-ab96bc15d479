---
authors: Renildo
date: 2024-11-04
description: We're excited to announce the release of Yumma CSS Intellisense v0.2.0! You'll now be able to use hover providers for media queries and pseudo classes.
pagefind: false
slug: blog/intellisense-0.2.0
title: Intellisense 0.2
---

We're excited to announce the release of Yumma CSS Intellisense v0.2.0! You'll now be able to use hover providers for media queries and pseudo classes.

{/* excerpt */}

<iframe
  allowfullscreen
  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
  class="ar-16/9 rad-1 w-full"
  frameborder="0"
  referrerpolicy="strict-origin-when-cross-origin"
  src="https://youtube.com/embed/n0tSAaHZTiw?si=AwWogjY861eJCqlt"
  title="What's new in Yumma CSS Intellisense 0.2.0?"></iframe>

You may also want to take a look at some of the [release notes](https://github.com/yumma-lib/yumma-css-intellisense/releases/tag/v0.2.0) but, anyway, these are the most noticeable shifts:

- [Media Queries](#media-queries): Add hover provider support to `sm:*`, `md:*`, `lg:*` and `xxl:*` variants
- [Pseudo Classes](#pseudo-classes): Add hover provider support to `h:*` variants

This is an incremental update that may contain bug fixes. Minor releases follow [semantic versioning](https://docs.npmjs.com/about-semantic-versioning) conventions. In other words, this should be an easy update for you.

---

## Media queries

If you hover over a media query utility variant, you'll see the utility class properties.

![Yumma CSS Intellisense - Media Query Hover](yummacss-intellisense-breakpoint-hover.png)

## Pseudo classes

The same goes for pseudo class utility variants like `h:*`.

![Yumma CSS Intellisense - Pseudo Class Hover](yummacss-intellisense-pseudo-hover.png)
