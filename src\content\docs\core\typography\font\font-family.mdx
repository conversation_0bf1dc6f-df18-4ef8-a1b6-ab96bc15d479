---
title: Font Family
banner:
  content: N/A
description: Controls the font family of an element.
slug: docs/font-family
---

<Class category="font" name="font-family" />

## Charter

This example sets the font family to **Charter**. If Charter is not available, it will fall back to **Cambria**, and then to a generic **serif** font.

```html live "ff-c" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white ff-c fs-lg p-4 rad-1 ta-c tc-lead">Sphinx of black quartz, judge my vow.</p>
</div>
```

## UI Monospace

This example sets the font family to **ui-monospace**. If ui-monospace is not available, it will fall back to **Consolas**, and then to a generic **monospace** font.

```html live "ff-m" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white ff-m fs-lg p-4 rad-1 ta-c tc-lead">Sphinx of black quartz, judge my vow.</p>
</div>
```

## System UI

This example sets the font family to **system-ui**. If system-ui is not available, it will fall back to a generic **sans-serif** font.

<Note icon="heart">Stylecent uses the `vars.$yma-font-system` as a default `font-family`. Learn more about it in the [docs](/docs/stylecent#document).</Note>

```html live "ff-s" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white ff-s fs-lg p-4 rad-1 ta-c tc-lead">Sphinx of black quartz, judge my vow.</p>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<BreakpointVariant class="ff-s" variant="ff-m" classPrefix="ff">
  ### Breakpoint variant
</BreakpointVariant>

<HoverVariant class="ff-s" variant="ff-m" classPrefix="ff">
  ### Hover variant
</HoverVariant>
