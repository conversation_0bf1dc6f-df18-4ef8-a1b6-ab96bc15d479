---
title: Flex Grow
banner:
  content: N/A
description: Controls the growth of flex elements.
slug: docs/flex-grow
---

<Class category="flexbox" name="flex-grow" />

This example showcases various `flex-grow` utilities:

- The **fg-0** flex grow utility sets the flex-grow property to **0**, preventing the item from growing.
- The **fg-1** flex grow utility sets the flex-grow property to **1**, allowing the item to grow and fill available space equally with other items.
- Finally, **fg-2** flex grow utility sets the flex-grow property to **2**, allowing the item to grow twice as much as an item with a flex-grow value of **1**.

```html live "fg-0" "fg-1" "fg-2" layout="/src/layouts/inline.astro"
<div class="d-f g-4 tc-white">
  <div class="bg-indigo fg-0 p-4 rad-1">A</div>
  <div class="bg-indigo fg-1 p-4 rad-1">B</div>
  <div class="bg-indigo fg-2 p-4 rad-1">C</div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/foundations/responsive-design/) or other factors, such as [hover states](/docs/foundations/variants/).

<BreakpointVariant class="fg-1" variant="fg-2" classPrefix="fg">
  ### Breakpoint variant
</BreakpointVariant>

<HoverVariant class="fg-1" variant="fg-2" classPrefix="fg">
  ### Hover variant
</HoverVariant>
