---
title: Letter Spacing
banner:
  content: N/A
description: Controls how the letters are spaced on an element.
slug: docs/letter-spacing
---

<Class category="text" name="letter-spacing" />

## 0

This example sets the letter spacing to **0em**. The letters will be spaced normally.

```html live "ls-0" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg ls-0 p-4 rad-1 ta-c tc-lead">Sphinx of black quartz, judge my vow.</p>
</div>
```

## 1

This example sets the letter spacing to **-0.05em**. The letters will be slightly closer together than normal.

```html live "ls-1" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg ls-1 p-4 rad-1 ta-c tc-lead">Sphinx of black quartz, judge my vow.</p>
</div>
```

## 2

This example sets the letter spacing to **-0.025em**. The letters will be slightly closer together.

```html live "ls-2" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg ls-2 p-4 rad-1 ta-c tc-lead">Sphinx of black quartz, judge my vow.</p>
</div>
```

## 3

This example sets the letter spacing to **0.025em**. The letters will be slightly more spaced apart.

```html live "ls-3" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg ls-3 p-4 rad-1 ta-c tc-lead">Sphinx of black quartz, judge my vow.</p>
</div>
```

## 4

This example sets the letter spacing to **0.05em**. The letters will be spaced further apart.

```html live "ls-4" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg ls-4 p-4 rad-1 ta-c tc-lead">Sphinx of black quartz, judge my vow.</p>
</div>
```

## 5

This example sets the letter spacing to **0.1em**. The letters will be significantly spaced apart.

```html live "ls-5" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg ls-5 p-4 rad-1 ta-c tc-lead">Sphinx of black quartz, judge my vow.</p>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<MediaModifier class="ls-1" mediaModifier="ls-2" classPrefix="ls">
  ### Media modifier
</MediaModifier>

<HoverModifier class="ls-1" hoverModifier="ls-2" classPrefix="ls">
  ### Hover modifier
</HoverModifier>
