---
title: Blur
banner:
  content: N/A
description: Applies a blur filter to an element.
slug: docs/blur
---

<Class category="effect" name="blur" />

This example showcases various `blur` utilities:

- The **f-b-none** blur utility sets the `filter` to **blur(0px)**.
- The **f-b-xs** blur utility sets the `filter` to **blur(4px)**.

```html live "f-b-none" "f-b-xs"
<div class="d-g g-16 gtc-2">
  <div class="d-32 p-r rad-1">
    <img class="d-full f-b-none of-c rad-1" src="https://picsum.photos/300?image=872" />
  </div>
  <div class="d-32 p-r rad-1">
    <img class="d-full f-b-xs of-c rad-1" src="https://picsum.photos/300?image=872" />
  </div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/foundations/responsive-design/) or other factors, such as [hover states](/docs/foundations/variants/).

<BreakpointVariant class="f-b-none" variant="f-b-md" classPrefix="f-b">
  ### Breakpoint variant
</BreakpointVariant>

<HoverVariant class="f-b-none" variant="f-b-md" classPrefix="f-b">
  ### Hover variant
</HoverVariant>
