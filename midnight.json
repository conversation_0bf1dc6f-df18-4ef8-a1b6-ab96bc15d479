{"name": "yumma-css-midnight", "type": "dark", "colors": {"foreground": "#bec6f2", "focusBorder": "#31365e", "selection.background": "#2d3151", "scrollbar.shadow": "#00000000", "activityBar.foreground": "#c4c7d9", "activityBar.background": "#21243f", "activityBar.inactiveForeground": "#bec6f2", "activityBarBadge.foreground": "#21243f", "activityBarBadge.background": "#bec6f2", "activityBar.border": "#31365e", "activityBar.activeBackground": "#2d3151", "sideBar.background": "#1e2039", "sideBar.foreground": "#c4c7d9", "sideBarSectionHeader.background": "#00000000", "sideBarSectionHeader.foreground": "#bec6f2", "sideBarSectionHeader.border": "#cccccc33", "sideBarTitle.foreground": "#bec6f2", "sideBar.border": "#31365e", "list.inactiveSelectionBackground": "#2d3151", "list.inactiveSelectionForeground": "#ffffff", "list.hoverBackground": "#2d3151", "list.hoverForeground": "#ffffff", "list.activeSelectionBackground": "#2d3151", "list.activeSelectionForeground": "#ffffff", "tree.indentGuidesStroke": "#9ea5cd", "list.dropBackground": "#2d3151", "list.highlightForeground": "#bec6f2", "list.focusBackground": "#2d3151", "list.focusForeground": "#bec6f2", "listFilterWidget.background": "#21243f", "listFilterWidget.outline": "#31365e", "listFilterWidget.noMatchesOutline": "#31365e", "statusBar.foreground": "#bec6f2", "statusBar.background": "#2d3151", "statusBarItem.hoverBackground": "#ffffff1f", "statusBar.border": "#31365e", "statusBar.debuggingBackground": "#2d3151", "statusBar.debuggingForeground": "#ffffff", "statusBar.debuggingBorder": "#31365e", "statusBar.noFolderBackground": "#21243f", "statusBar.noFolderForeground": "#ffffff", "statusBarItem.remoteBackground": "#21243f", "statusBarItem.remoteForeground": "#bec6f2", "titleBar.activeBackground": "#1e2039", "titleBar.activeForeground": "#bec6f2", "titleBar.inactiveBackground": "#1e2039", "titleBar.inactiveForeground": "#cbd1f5", "titleBar.border": "#31365e", "menubar.selectionForeground": "#ffffff", "menubar.selectionBackground": "#ffffff1a", "menubar.selectionBorder": "#bec6f2", "menu.foreground": "#cbd1f5", "menu.background": "#21243f", "menu.selectionForeground": "#ffffff", "menu.selectionBackground": "#151724", "menu.selectionBorder": "#00000000", "menu.separatorBackground": "#31365e", "menu.border": "#31365e", "button.background": "#2a2d4c", "button.foreground": "#ffffff", "button.hoverBackground": "#2d3151", "button.secondaryForeground": "#ffffff", "button.secondaryBackground": "#424662", "button.secondaryHoverBackground": "#45494e", "input.background": "#151724", "input.border": "#00000000", "input.foreground": "#bec6f2", "inputOption.activeBackground": "#2d3151", "inputOption.activeBorder": "#007acc00", "inputOption.activeForeground": "#ffffff", "input.placeholderForeground": "#bec6f2", "textLink.foreground": "#bec6f2", "editor.background": "#21243f", "editor.foreground": "#b9bed5", "editorLineNumber.foreground": "#bec6f2", "editorCursor.foreground": "#bec6f2", "editorCursor.background": "#ffffff", "editor.selectionBackground": "#2d3151", "editor.inactiveSelectionBackground": "#292c49", "editorWhitespace.foreground": "#e3e4e229", "editor.selectionHighlightBackground": "#151724", "editor.selectionHighlightBorder": "#2d3151", "editor.findMatchBackground": "#2d3151", "editor.findMatchBorder": "#bec6f2", "editor.findMatchHighlightBackground": "#bec6f283", "editor.findMatchHighlightBorder": "#ffffff00", "editor.findRangeHighlightBackground": "#2d3151", "editor.findRangeHighlightBorder": "#ffffff00", "editor.rangeHighlightBackground": "#ffffff0b", "editor.rangeHighlightBorder": "#ffffff00", "editor.hoverHighlightBackground": "#264f7840", "editor.wordHighlightStrongBackground": "#2d3151", "editor.wordHighlightBackground": "#bec6f260", "editor.lineHighlightBackground": "#ffffff0A", "editor.lineHighlightBorder": "#bec6f232", "editorLineNumber.activeForeground": "#ffffff", "editorLink.activeForeground": "#bec6f2", "editorIndentGuide.background": "#bec6f2", "editorIndentGuide.activeBackground": "#989ec2", "editorRuler.foreground": "#2d3151", "editorBracketMatch.background": "#0064001a", "editorBracketMatch.border": "#888888", "editor.foldBackground": "#2d3151", "editorOverviewRuler.background": "#25252500", "editorOverviewRuler.border": "#7f7f7f4d", "editorError.foreground": "#ff9499", "editorError.background": "#B73A3400", "editorError.border": "#ffffff00", "editorWarning.foreground": "#ccaf75", "editorWarning.background": "#A9904000", "editorWarning.border": "#ffffff00", "editorInfo.foreground": "#bec6f2", "editorInfo.background": "#4490BF00", "editorInfo.border": "#4490BF00", "editorGutter.background": "#1e2039", "editorGutter.modifiedBackground": "#bec6f2", "editorGutter.addedBackground": "#c1f0cc", "editorGutter.deletedBackground": "#ff9499", "editorGutter.foldingControlForeground": "#bec6f2", "editorCodeLens.foreground": "#bec6f2", "editorGroup.border": "#2d3151", "diffEditor.insertedTextBackground": "#354050", "diffEditor.insertedTextBorder": "#c1f0cc60", "diffEditor.removedTextBackground": "#35314a", "diffEditor.removedTextBorder": "#ff949956", "diffEditor.border": "#2d3151", "panel.background": "#21243f", "panel.border": "#31365e", "panelTitle.activeBorder": "#c4c7d9", "panelTitle.activeForeground": "#c4c7d9", "panelTitle.inactiveForeground": "#989ec2", "badge.background": "#bec6f2", "badge.foreground": "#151724", "terminal.foreground": "#bec6f2", "terminal.selectionBackground": "#2d3151", "terminalCursor.background": "#bec6f2", "terminalCursor.foreground": "#ffffff", "terminal.border": "#31365e", "terminal.ansiBlack": "#000000", "terminal.ansiBlue": "#2472c8", "terminal.ansiBrightBlack": "#666666", "terminal.ansiBrightBlue": "#3b8eea", "terminal.ansiBrightCyan": "#29b8db", "terminal.ansiBrightGreen": "#23d18b", "terminal.ansiBrightMagenta": "#d670d6", "terminal.ansiBrightRed": "#f14c4c", "terminal.ansiBrightWhite": "#e5e5e5", "terminal.ansiBrightYellow": "#f5f543", "terminal.ansiCyan": "#11a8cd", "terminal.ansiGreen": "#0dbc79", "terminal.ansiMagenta": "#bc3fbc", "terminal.ansiRed": "#cd3131", "terminal.ansiWhite": "#ffffff", "terminal.ansiYellow": "#e5e510", "breadcrumb.background": "#21243f", "breadcrumb.foreground": "#bec6f2", "breadcrumb.focusForeground": "#ffffff", "editorGroupHeader.border": "#31365e", "editorGroupHeader.tabsBackground": "#151724", "editorGroupHeader.tabsBorder": "#2d3151", "tab.activeForeground": "#ffffff", "tab.border": "#2d3151", "tab.activeBackground": "#21243f", "tab.activeBorder": "#00000000", "tab.activeBorderTop": "#00000000", "tab.inactiveBackground": "#151724", "tab.inactiveForeground": "#bec6f2", "tab.hoverBackground": "#21243f", "tab.hoverForeground": "#ffffff", "tab.hoverBorder": "#31365e", "scrollbarSlider.background": "#2a2d4c", "scrollbarSlider.hoverBackground": "#1e2039", "scrollbarSlider.activeBackground": "#2d3151", "progressBar.background": "#bec6f2", "widget.shadow": "#0d0e16", "editorWidget.foreground": "#d2d7f6", "editorWidget.background": "#21243f", "editorWidget.resizeBorder": "#bec6f2", "pickerGroup.border": "#31365e", "pickerGroup.foreground": "#bec6f2", "debugToolBar.background": "#21243f", "debugToolBar.border": "#31365e", "notifications.foreground": "#bec6f2", "notifications.background": "#21243f", "notificationToast.border": "#2d3151", "notificationsErrorIcon.foreground": "#ff9499", "notificationsWarningIcon.foreground": "#ccaf75", "notificationsInfoIcon.foreground": "#89b8d6", "notificationCenter.border": "#2d3151", "notificationCenterHeader.foreground": "#bec6f2", "notificationCenterHeader.background": "#151724", "notifications.border": "#2d3151", "gitDecoration.addedResourceForeground": "#c1f0cc", "gitDecoration.conflictingResourceForeground": "#f0e4c1", "gitDecoration.deletedResourceForeground": "#f498a9", "gitDecoration.ignoredResourceForeground": "#989ec2", "gitDecoration.modifiedResourceForeground": "#dda2f6", "gitDecoration.stageDeletedResourceForeground": "#ff9499", "gitDecoration.stageModifiedResourceForeground": "#c1f0cc", "gitDecoration.submoduleResourceForeground": "#bec6f2", "gitDecoration.untrackedResourceForeground": "#989ec2", "editorMarkerNavigation.background": "#151724", "editorMarkerNavigationError.background": "#f498a9", "editorMarkerNavigationWarning.background": "#f0e4c1", "editorMarkerNavigationInfo.background": "#bec6f2", "merge.currentHeaderBackground": "#367366", "merge.currentContentBackground": "#27403B", "merge.incomingHeaderBackground": "#395F8F", "merge.incomingContentBackground": "#28384B", "merge.commonHeaderBackground": "#383838", "merge.commonContentBackground": "#282828", "editorSuggestWidget.background": "#151724", "editorSuggestWidget.border": "#2d3151", "editorSuggestWidget.foreground": "#d4d4d4", "editorSuggestWidget.highlightForeground": "#bec6f2", "editorSuggestWidget.selectedBackground": "#2d3151", "editorHoverWidget.foreground": "#bec6f2", "editorHoverWidget.background": "#151724", "editorHoverWidget.border": "#2d3151", "peekView.border": "#2d3151", "peekViewEditor.background": "#1a1d32", "peekViewEditorGutter.background": "#151724", "peekViewEditor.matchHighlightBackground": "#2d3151", "peekViewEditor.matchHighlightBorder": "#bec6f2", "peekViewResult.background": "#151724", "peekViewResult.fileForeground": "#ffffff", "peekViewResult.lineForeground": "#bbbbbb", "peekViewResult.matchHighlightBackground": "#2d3151", "peekViewResult.selectionBackground": "#3399ff33", "peekViewResult.selectionForeground": "#ffffff", "peekViewTitle.background": "#151724", "peekViewTitleDescription.foreground": "#ccccccb3", "peekViewTitleLabel.foreground": "#ffffff", "icon.foreground": "#bec6f2", "checkbox.background": "#151724", "checkbox.foreground": "#bec6f2", "checkbox.border": "#00000000", "dropdown.background": "#151724", "dropdown.foreground": "#bec6f2", "dropdown.border": "#00000000", "minimapGutter.addedBackground": "#c1f0cc", "minimapGutter.modifiedBackground": "#bec6f2", "minimapGutter.deletedBackground": "#ff9499", "minimap.findMatchHighlight": "#2d3151", "minimap.selectionHighlight": "#2d3151", "minimap.errorHighlight": "#ff9499", "minimap.warningHighlight": "#ccaf75", "minimap.background": "#21243f", "sideBar.dropBackground": "#2d3151", "editorGroup.emptyBackground": "#21243f", "panelSection.border": "#31365e", "statusBarItem.activeBackground": "#FFFFFF25", "settings.headerForeground": "#bec6f2", "settings.focusedRowBackground": "#ffffff07", "walkThrough.embeddedEditorBackground": "#00000050", "breadcrumb.activeSelectionForeground": "#ffffff", "editorGutter.commentRangeForeground": "#bec6f2", "debugExceptionWidget.background": "#21243f", "debugExceptionWidget.border": "#31365e"}, "tokenColors": [{"name": "unison punctuation", "scope": "punctuation.definition.delayed.unison,punctuation.definition.list.begin.unison,punctuation.definition.list.end.unison,punctuation.definition.ability.begin.unison,punctuation.definition.ability.end.unison,punctuation.operator.assignment.as.unison,punctuation.separator.pipe.unison,punctuation.separator.delimiter.unison,punctuation.definition.hash.unison", "settings": {"foreground": "#85b1e0"}}, {"name": "haskell variable generic-type", "scope": "variable.other.generic-type.haskell", "settings": {"foreground": "#9595e3"}}, {"name": "haskell storage type", "scope": "storage.type.haskell", "settings": {"foreground": "#dda2f6"}}, {"name": "support.variable.magic.python", "scope": "support.variable.magic.python", "settings": {"foreground": "#85b1e0"}}, {"name": "punctuation.separator.parameters.python", "scope": "punctuation.separator.period.python,punctuation.separator.element.python,punctuation.parenthesis.begin.python,punctuation.parenthesis.end.python", "settings": {"foreground": "#b9bed5"}}, {"name": "variable.parameter.function.language.special.self.python", "scope": "variable.parameter.function.language.special.self.python", "settings": {"foreground": "#85b1e0"}}, {"name": "storage.modifier.lifetime.rust", "scope": "storage.modifier.lifetime.rust", "settings": {"foreground": "#b9bed5"}}, {"name": "support.function.std.rust", "scope": "support.function.std.rust", "settings": {"foreground": "#f5faff"}}, {"name": "entity.name.lifetime.rust", "scope": "entity.name.lifetime.rust", "settings": {"foreground": "#85b1e0"}}, {"name": "variable.language.rust", "scope": "variable.language.rust", "settings": {"foreground": "#85b1e0"}}, {"name": "support.constant.edge", "scope": "support.constant.edge", "settings": {"foreground": "#9595e3"}}, {"name": "regexp constant character-class", "scope": "constant.other.character-class.regexp", "settings": {"foreground": "#85b1e0"}}, {"name": "regexp operator.quantifier", "scope": "keyword.operator.quantifier.regexp", "settings": {"foreground": "#dda2f6"}}, {"name": "punctuation.definition", "scope": "punctuation.definition.string.begin,punctuation.definition.string.end", "settings": {"foreground": "#bec6f2"}}, {"name": "Text", "scope": "variable.parameter.function", "settings": {"foreground": "#b9bed5"}}, {"name": "Comment Markup Link", "scope": "comment markup.link", "settings": {"foreground": "#979cac"}}, {"name": "markup diff", "scope": "markup.changed.diff", "settings": {"foreground": "#85b1e0"}}, {"name": "diff", "scope": "meta.diff.header.from-file,meta.diff.header.to-file,punctuation.definition.from-file.diff,punctuation.definition.to-file.diff", "settings": {"foreground": "#f5faff"}}, {"name": "inserted.diff", "scope": "markup.inserted.diff", "settings": {"foreground": "#bec6f2"}}, {"name": "deleted.diff", "scope": "markup.deleted.diff", "settings": {"foreground": "#85b1e0"}}, {"name": "c++ function", "scope": "meta.function.c,meta.function.cpp", "settings": {"foreground": "#85b1e0"}}, {"name": "c++ block", "scope": "punctuation.section.block.begin.bracket.curly.cpp,punctuation.section.block.end.bracket.curly.cpp,punctuation.terminator.statement.c,punctuation.section.block.begin.bracket.curly.c,punctuation.section.block.end.bracket.curly.c,punctuation.section.parens.begin.bracket.round.c,punctuation.section.parens.end.bracket.round.c,punctuation.section.parameters.begin.bracket.round.c,punctuation.section.parameters.end.bracket.round.c", "settings": {"foreground": "#b9bed5"}}, {"name": "js/ts punctuation separator key-value", "scope": "punctuation.separator.key-value", "settings": {"foreground": "#b9bed5"}}, {"name": "js/ts import keyword", "scope": "keyword.operator.expression.import", "settings": {"foreground": "#f5faff"}}, {"name": "math js/ts", "scope": "support.constant.math", "settings": {"foreground": "#85b1e0"}}, {"name": "math property js/ts", "scope": "support.constant.property.math", "settings": {"foreground": "#dda2f6"}}, {"name": "js/ts variable.other.constant", "scope": "variable.other.constant", "settings": {"foreground": "#85b1e0"}}, {"name": "java type", "scope": ["storage.type.annotation.java", "storage.type.object.array.java"], "settings": {"foreground": "#85b1e0"}}, {"name": "java source", "scope": "source.java", "settings": {"foreground": "#85b1e0"}}, {"name": "java modifier.import", "scope": "punctuation.section.block.begin.java,punctuation.section.block.end.java,punctuation.definition.method-parameters.begin.java,punctuation.definition.method-parameters.end.java,meta.method.identifier.java,punctuation.section.method.begin.java,punctuation.section.method.end.java,punctuation.terminator.java,punctuation.section.class.begin.java,punctuation.section.class.end.java,punctuation.section.inner-class.begin.java,punctuation.section.inner-class.end.java,meta.method-call.java,punctuation.section.class.begin.bracket.curly.java,punctuation.section.class.end.bracket.curly.java,punctuation.section.method.begin.bracket.curly.java,punctuation.section.method.end.bracket.curly.java,punctuation.separator.period.java,punctuation.bracket.angle.java,punctuation.definition.annotation.java,meta.method.body.java", "settings": {"foreground": "#b9bed5"}}, {"name": "java modifier.import", "scope": "meta.method.java", "settings": {"foreground": "#f5faff"}}, {"name": "java modifier.import", "scope": "storage.modifier.import.java,storage.type.java,storage.type.generic.java", "settings": {"foreground": "#85b1e0"}}, {"name": "java instanceof", "scope": "keyword.operator.instanceof.java", "settings": {"foreground": "#9595e3"}}, {"name": "java variable.name", "scope": "meta.definition.variable.name.java", "settings": {"foreground": "#85b1e0"}}, {"name": "operator logical", "scope": "keyword.operator.logical", "settings": {"foreground": "#93ddfb"}}, {"name": "operator bitwise", "scope": "keyword.operator.bitwise", "settings": {"foreground": "#93ddfb"}}, {"name": "operator channel", "scope": "keyword.operator.channel", "settings": {"foreground": "#93ddfb"}}, {"name": "support.constant.property-value.scss", "scope": "support.constant.property-value.scss,support.constant.property-value.css", "settings": {"foreground": "#dda2f6"}}, {"name": "CSS/SCSS/LESS Operators", "scope": "keyword.operator.css,keyword.operator.scss,keyword.operator.less", "settings": {"foreground": "#93ddfb"}}, {"name": "css color standard name", "scope": "support.constant.color.w3c-standard-color-name.css,support.constant.color.w3c-standard-color-name.scss", "settings": {"foreground": "#dda2f6"}}, {"name": "css comma", "scope": "punctuation.separator.list.comma.css", "settings": {"foreground": "#b9bed5"}}, {"name": "css attribute-name.id", "scope": "support.constant.color.w3c-standard-color-name.css", "settings": {"foreground": "#dda2f6"}}, {"name": "css property-name", "scope": "support.type.vendored.property-name.css", "settings": {"foreground": "#93ddfb"}}, {"name": "js/ts module", "scope": "support.module.node,support.type.object.module,support.module.node", "settings": {"foreground": "#85b1e0"}}, {"name": "entity.name.type.module", "scope": "entity.name.type.module", "settings": {"foreground": "#85b1e0"}}, {"name": "js variable readwrite", "scope": "variable.other.readwrite,meta.object-literal.key,support.variable.property,support.variable.object.process,support.variable.object.node", "settings": {"foreground": "#85b1e0"}}, {"name": "js/ts json", "scope": "support.constant.json", "settings": {"foreground": "#dda2f6"}}, {"name": "js/ts Keyword", "scope": ["keyword.operator.expression.instanceof", "keyword.operator.new", "keyword.operator.ternary", "keyword.operator.optional", "keyword.operator.expression.keyof"], "settings": {"foreground": "#9595e3"}}, {"name": "js/ts console", "scope": "support.type.object.console", "settings": {"foreground": "#85b1e0"}}, {"name": "js/ts support.variable.property.process", "scope": "support.variable.property.process", "settings": {"foreground": "#dda2f6"}}, {"name": "js console function", "scope": "entity.name.function,support.function.console", "settings": {"foreground": "#f5faff"}}, {"name": "keyword.operator.misc.rust", "scope": "keyword.operator.misc.rust", "settings": {"foreground": "#b9bed5"}}, {"name": "keyword.operator.sigil.rust", "scope": "keyword.operator.sigil.rust", "settings": {"foreground": "#9595e3"}}, {"name": "operator", "scope": "keyword.operator.delete", "settings": {"foreground": "#9595e3"}}, {"name": "js dom", "scope": "support.type.object.dom", "settings": {"foreground": "#93ddfb"}}, {"name": "js dom variable", "scope": "support.variable.dom,support.variable.property.dom", "settings": {"foreground": "#85b1e0"}}, {"name": "keyword.operator", "scope": "keyword.operator.arithmetic,keyword.operator.comparison,keyword.operator.decrement,keyword.operator.increment,keyword.operator.relational", "settings": {"foreground": "#93ddfb"}}, {"name": "C operator assignment", "scope": "keyword.operator.assignment.c,keyword.operator.comparison.c,keyword.operator.c,keyword.operator.increment.c,keyword.operator.decrement.c,keyword.operator.bitwise.shift.c,keyword.operator.assignment.cpp,keyword.operator.comparison.cpp,keyword.operator.cpp,keyword.operator.increment.cpp,keyword.operator.decrement.cpp,keyword.operator.bitwise.shift.cpp", "settings": {"foreground": "#9595e3"}}, {"name": "Punctuation", "scope": "punctuation.separator.delimiter", "settings": {"foreground": "#b9bed5"}}, {"name": "Other punctuation .c", "scope": "punctuation.separator.c,punctuation.separator.cpp", "settings": {"foreground": "#9595e3"}}, {"name": "C type posix-reserved", "scope": "support.type.posix-reserved.c,support.type.posix-reserved.cpp", "settings": {"foreground": "#93ddfb"}}, {"name": "keyword.operator.sizeof.c", "scope": "keyword.operator.sizeof.c,keyword.operator.sizeof.cpp", "settings": {"foreground": "#9595e3"}}, {"name": "python parameter", "scope": "variable.parameter.function.language.python", "settings": {"foreground": "#dda2f6"}}, {"name": "python type", "scope": "support.type.python", "settings": {"foreground": "#93ddfb"}}, {"name": "python logical", "scope": "keyword.operator.logical.python", "settings": {"foreground": "#9595e3"}}, {"name": "pyCs", "scope": "variable.parameter.function.python", "settings": {"foreground": "#dda2f6"}}, {"name": "python block", "scope": "punctuation.definition.arguments.begin.python,punctuation.definition.arguments.end.python,punctuation.separator.arguments.python,punctuation.definition.list.begin.python,punctuation.definition.list.end.python", "settings": {"foreground": "#b9bed5"}}, {"name": "python function-call.generic", "scope": "meta.function-call.generic.python", "settings": {"foreground": "#f5faff"}}, {"name": "python placeholder reset to normal string", "scope": "constant.character.format.placeholder.other.python", "settings": {"foreground": "#dda2f6"}}, {"name": "Operators", "scope": "keyword.operator", "settings": {"foreground": "#b9bed5"}}, {"name": "Compound Assignment Operators", "scope": "keyword.operator.assignment.compound", "settings": {"foreground": "#9595e3"}}, {"name": "Compound Assignment Operators js/ts", "scope": "keyword.operator.assignment.compound.js,keyword.operator.assignment.compound.ts", "settings": {"foreground": "#93ddfb"}}, {"name": "Keywords", "scope": "keyword", "settings": {"foreground": "#9595e3"}}, {"name": "Namespaces", "scope": "entity.name.namespace", "settings": {"foreground": "#85b1e0"}}, {"name": "Variables", "scope": "variable", "settings": {"foreground": "#85b1e0"}}, {"name": "Variables", "scope": "variable.c", "settings": {"foreground": "#b9bed5"}}, {"name": "Language variables", "scope": "variable.language", "settings": {"foreground": "#85b1e0"}}, {"name": "Java Variables", "scope": "token.variable.parameter.java", "settings": {"foreground": "#b9bed5"}}, {"name": "Java Imports", "scope": "import.storage.java", "settings": {"foreground": "#85b1e0"}}, {"name": "Packages", "scope": "token.package.keyword", "settings": {"foreground": "#9595e3"}}, {"name": "Packages", "scope": "token.package", "settings": {"foreground": "#b9bed5"}}, {"name": "Functions", "scope": ["entity.name.function", "meta.require", "support.function.any-method", "variable.function"], "settings": {"foreground": "#f5faff"}}, {"name": "Classes", "scope": "entity.name.type.namespace", "settings": {"foreground": "#85b1e0"}}, {"name": "Classes", "scope": "support.class, entity.name.type.class", "settings": {"foreground": "#85b1e0"}}, {"name": "Class name", "scope": "entity.name.class.identifier.namespace.type", "settings": {"foreground": "#85b1e0"}}, {"name": "Class name", "scope": ["entity.name.class", "variable.other.class.js", "variable.other.class.ts"], "settings": {"foreground": "#85b1e0"}}, {"name": "Class name php", "scope": "variable.other.class.php", "settings": {"foreground": "#85b1e0"}}, {"name": "Type Name", "scope": "entity.name.type", "settings": {"foreground": "#85b1e0"}}, {"name": "Keyword Control", "scope": "keyword.control", "settings": {"foreground": "#9595e3"}}, {"name": "Control Elements", "scope": "control.elements, keyword.operator.less", "settings": {"foreground": "#dda2f6"}}, {"name": "Methods", "scope": "keyword.other.special-method", "settings": {"foreground": "#f5faff"}}, {"name": "Storage", "scope": "storage", "settings": {"foreground": "#9595e3"}}, {"name": "Storage JS TS", "scope": "token.storage", "settings": {"foreground": "#9595e3"}}, {"name": "Source Js Keyword Operator Delete,source Js Keyword Operator In,source Js Keyword Operator Of,source Js Keyword Operator Instanceof,source Js Keyword Operator New,source Js Keyword Operator Typeof,source Js Keyword Operator Void", "scope": "keyword.operator.expression.delete,keyword.operator.expression.in,keyword.operator.expression.of,keyword.operator.expression.instanceof,keyword.operator.new,keyword.operator.expression.typeof,keyword.operator.expression.void", "settings": {"foreground": "#9595e3"}}, {"name": "Java Storage", "scope": "token.storage.type.java", "settings": {"foreground": "#85b1e0"}}, {"name": "Support", "scope": "support.function", "settings": {"foreground": "#93ddfb"}}, {"name": "Support type", "scope": "support.type.property-name", "settings": {"foreground": "#b9bed5"}}, {"name": "Support type", "scope": "support.constant.property-value", "settings": {"foreground": "#b9bed5"}}, {"name": "Support type", "scope": "support.constant.font-name", "settings": {"foreground": "#dda2f6"}}, {"name": "Meta tag", "scope": "meta.tag", "settings": {"foreground": "#b9bed5"}}, {"name": "Strings", "scope": "string", "settings": {"foreground": "#bec6f2"}}, {"name": "Inherited Class", "scope": "entity.other.inherited-class", "settings": {"foreground": "#85b1e0"}}, {"name": "Constant other symbol", "scope": "constant.other.symbol", "settings": {"foreground": "#93ddfb"}}, {"name": "Integers", "scope": "constant.numeric", "settings": {"foreground": "#dda2f6"}}, {"name": "Constants", "scope": "constant", "settings": {"foreground": "#dda2f6"}}, {"name": "Constants", "scope": "punctuation.definition.constant", "settings": {"foreground": "#dda2f6"}}, {"name": "Tags", "scope": "entity.name.tag", "settings": {"foreground": "#85b1e0"}}, {"name": "Attributes", "scope": "entity.other.attribute-name", "settings": {"foreground": "#dda2f6"}}, {"name": "Attribute IDs", "scope": "entity.other.attribute-name.id", "settings": {"fontStyle": "normal", "foreground": "#f5faff"}}, {"name": "Attribute class", "scope": "entity.other.attribute-name.class.css", "settings": {"fontStyle": "normal", "foreground": "#dda2f6"}}, {"name": "Selector", "scope": "meta.selector", "settings": {"foreground": "#9595e3"}}, {"name": "Headings", "scope": "markup.heading", "settings": {"foreground": "#85b1e0"}}, {"name": "Headings", "scope": "markup.heading punctuation.definition.heading, entity.name.section", "settings": {"foreground": "#f5faff"}}, {"name": "Units", "scope": "keyword.other.unit", "settings": {"foreground": "#85b1e0"}}, {"name": "Bold", "scope": "markup.bold,todo.bold", "settings": {"foreground": "#dda2f6"}}, {"name": "Bold", "scope": "punctuation.definition.bold", "settings": {"foreground": "#85b1e0"}}, {"name": "markup Italic", "scope": "markup.italic, punctuation.definition.italic,todo.emphasis", "settings": {"foreground": "#9595e3"}}, {"name": "emphasis md", "scope": "emphasis md", "settings": {"foreground": "#9595e3"}}, {"name": "[VSCODE-CUSTOM] Markdown headings", "scope": "entity.name.section.markdown", "settings": {"foreground": "#85b1e0"}}, {"name": "[VSCODE-CUSTOM] Markdown heading Punctuation Definition", "scope": "punctuation.definition.heading.markdown", "settings": {"foreground": "#85b1e0"}}, {"name": "punctuation.definition.list.begin.markdown", "scope": "punctuation.definition.list.begin.markdown", "settings": {"foreground": "#85b1e0"}}, {"name": "[VSCODE-CUSTOM] Markdown heading setext", "scope": "markup.heading.setext", "settings": {"foreground": "#b9bed5"}}, {"name": "[VSCODE-CUSTOM] Markdown Punctuation Definition Bold", "scope": "punctuation.definition.bold.markdown", "settings": {"foreground": "#dda2f6"}}, {"name": "[VSCODE-CUSTOM] Markdown Inline Raw", "scope": "markup.inline.raw.markdown", "settings": {"foreground": "#bec6f2"}}, {"name": "[VSCODE-CUSTOM] Markdown Inline Raw", "scope": "markup.inline.raw.string.markdown", "settings": {"foreground": "#bec6f2"}}, {"name": "[VSCODE-CUSTOM] Markdown List Punctuation Definition", "scope": "punctuation.definition.list.markdown", "settings": {"foreground": "#85b1e0"}}, {"name": "[VSCODE-CUSTOM] Markdown Punctuation Definition String", "scope": ["punctuation.definition.string.begin.markdown", "punctuation.definition.string.end.markdown", "punctuation.definition.metadata.markdown"], "settings": {"foreground": "#85b1e0"}}, {"name": "beginning.punctuation.definition.list.markdown", "scope": ["beginning.punctuation.definition.list.markdown"], "settings": {"foreground": "#85b1e0"}}, {"name": "[VSCODE-CUSTOM] Markdown Punctuation Definition Link", "scope": "punctuation.definition.metadata.markdown", "settings": {"foreground": "#85b1e0"}}, {"name": "[VSCODE-CUSTOM] Markdown Underline Link/Image", "scope": "markup.underline.link.markdown,markup.underline.link.image.markdown", "settings": {"foreground": "#9595e3"}}, {"name": "[VSCODE-CUSTOM] Markdown Link Title/Description", "scope": "string.other.link.title.markdown,string.other.link.description.markdown", "settings": {"foreground": "#f5faff"}}, {"name": "Regular Expressions", "scope": "string.regexp", "settings": {"foreground": "#93ddfb"}}, {"name": "Escape Characters", "scope": "constant.character.escape", "settings": {"foreground": "#93ddfb"}}, {"name": "Embedded", "scope": "punctuation.section.embedded, variable.interpolation", "settings": {"foreground": "#85b1e0"}}, {"name": "Embedded", "scope": "punctuation.section.embedded.begin,punctuation.section.embedded.end", "settings": {"foreground": "#9595e3"}}, {"name": "illegal", "scope": "invalid.illegal", "settings": {"foreground": "#ffffff"}}, {"name": "illegal", "scope": "invalid.illegal.bad-ampersand.html", "settings": {"foreground": "#b9bed5"}}, {"name": "Broken", "scope": "invalid.broken", "settings": {"foreground": "#ffffff"}}, {"name": "Deprecated", "scope": "invalid.deprecated", "settings": {"foreground": "#ffffff"}}, {"name": "Unimplemented", "scope": "invalid.unimplemented", "settings": {"foreground": "#ffffff"}}, {"name": "Source Json Meta Structure Dictionary Json > String Quoted <PERSON><PERSON>", "scope": "source.json meta.structure.dictionary.json > string.quoted.json", "settings": {"foreground": "#85b1e0"}}, {"name": "Source Json Meta Structure Dictionary Json > String Quoted J<PERSON> > Punctuation String", "scope": "source.json meta.structure.dictionary.json > string.quoted.json > punctuation.string", "settings": {"foreground": "#85b1e0"}}, {"name": "Source Json Meta Structure Dictionary Json > Value Json > String Quoted Json,source Json Meta Structure Array Json > Value Json > String Quoted Json,source Json Meta Structure Dictionary Json > Value Json > String Quoted Json > Punctuation,source Json Meta Structure Array Json > Value Json > String Quoted Json > Punctuation", "scope": "source.json meta.structure.dictionary.json > value.json > string.quoted.json,source.json meta.structure.array.json > value.json > string.quoted.json,source.json meta.structure.dictionary.json > value.json > string.quoted.json > punctuation,source.json meta.structure.array.json > value.json > string.quoted.json > punctuation", "settings": {"foreground": "#bec6f2"}}, {"name": "Source Json Meta Structure Dictionary Json > Constant Language Json,source Json Meta Structure Array Json > Constant Language Json", "scope": "source.json meta.structure.dictionary.json > constant.language.json,source.json meta.structure.array.json > constant.language.json", "settings": {"foreground": "#93ddfb"}}, {"name": "[VSCODE-CUSTOM] JSON Property Name", "scope": "support.type.property-name.json", "settings": {"foreground": "#85b1e0"}}, {"name": "[VSCODE-CUSTOM] JSON Punctuation for Property Name", "scope": "support.type.property-name.json punctuation", "settings": {"foreground": "#85b1e0"}}, {"name": "laravel blade tag", "scope": "text.html.laravel-blade source.php.embedded.line.html entity.name.tag.laravel-blade", "settings": {"foreground": "#9595e3"}}, {"name": "laravel blade @", "scope": "text.html.laravel-blade source.php.embedded.line.html support.constant.laravel-blade", "settings": {"foreground": "#9595e3"}}, {"name": "use statement for other classes", "scope": "support.other.namespace.use.php,support.other.namespace.use-as.php,support.other.namespace.php,entity.other.alias.php,meta.interface.php", "settings": {"foreground": "#85b1e0"}}, {"name": "error suppression", "scope": "keyword.operator.error-control.php", "settings": {"foreground": "#9595e3"}}, {"name": "php instanceof", "scope": "keyword.operator.type.php", "settings": {"foreground": "#9595e3"}}, {"name": "style double quoted array index normal begin", "scope": "punctuation.section.array.begin.php", "settings": {"foreground": "#b9bed5"}}, {"name": "style double quoted array index normal end", "scope": "punctuation.section.array.end.php", "settings": {"foreground": "#b9bed5"}}, {"name": "php illegal.non-null-typehinted", "scope": "invalid.illegal.non-null-typehinted.php", "settings": {"foreground": "#f44747"}}, {"name": "php types", "scope": "storage.type.php,meta.other.type.phpdoc.php,keyword.other.type.php,keyword.other.array.phpdoc.php", "settings": {"foreground": "#85b1e0"}}, {"name": "php call-function", "scope": "meta.function-call.php,meta.function-call.object.php,meta.function-call.static.php", "settings": {"foreground": "#f5faff"}}, {"name": "php function-resets", "scope": "punctuation.definition.parameters.begin.bracket.round.php,punctuation.definition.parameters.end.bracket.round.php,punctuation.separator.delimiter.php,punctuation.section.scope.begin.php,punctuation.section.scope.end.php,punctuation.terminator.expression.php,punctuation.definition.arguments.begin.bracket.round.php,punctuation.definition.arguments.end.bracket.round.php,punctuation.definition.storage-type.begin.bracket.round.php,punctuation.definition.storage-type.end.bracket.round.php,punctuation.definition.array.begin.bracket.round.php,punctuation.definition.array.end.bracket.round.php,punctuation.definition.begin.bracket.round.php,punctuation.definition.end.bracket.round.php,punctuation.definition.begin.bracket.curly.php,punctuation.definition.end.bracket.curly.php,punctuation.definition.section.switch-block.end.bracket.curly.php,punctuation.definition.section.switch-block.start.bracket.curly.php,punctuation.definition.section.switch-block.begin.bracket.curly.php,punctuation.definition.section.switch-block.end.bracket.curly.php", "settings": {"foreground": "#b9bed5"}}, {"name": "support php constants", "scope": "support.constant.core.rust", "settings": {"foreground": "#dda2f6"}}, {"name": "support php constants", "scope": "support.constant.ext.php,support.constant.std.php,support.constant.core.php,support.constant.parser-token.php", "settings": {"foreground": "#dda2f6"}}, {"name": "php goto", "scope": "entity.name.goto-label.php,support.other.php", "settings": {"foreground": "#f5faff"}}, {"name": "php logical/bitwise operator", "scope": "keyword.operator.logical.php,keyword.operator.bitwise.php,keyword.operator.arithmetic.php", "settings": {"foreground": "#93ddfb"}}, {"name": "php regexp operator", "scope": "keyword.operator.regexp.php", "settings": {"foreground": "#9595e3"}}, {"name": "php comparison", "scope": "keyword.operator.comparison.php", "settings": {"foreground": "#93ddfb"}}, {"name": "php heredoc/nowdoc", "scope": "keyword.operator.heredoc.php,keyword.operator.nowdoc.php", "settings": {"foreground": "#9595e3"}}, {"name": "python function decorator @", "scope": "meta.function.decorator.python", "settings": {"foreground": "#f5faff"}}, {"name": "python function support", "scope": "support.token.decorator.python,meta.function.decorator.identifier.python", "settings": {"foreground": "#93ddfb"}}, {"name": "parameter function js/ts", "scope": "function.parameter", "settings": {"foreground": "#b9bed5"}}, {"name": "brace function", "scope": "function.brace", "settings": {"foreground": "#b9bed5"}}, {"name": "parameter function ruby cs", "scope": "function.parameter.ruby, function.parameter.cs", "settings": {"foreground": "#b9bed5"}}, {"name": "constant.language.symbol.ruby", "scope": "constant.language.symbol.ruby", "settings": {"foreground": "#93ddfb"}}, {"name": "rgb-value", "scope": "rgb-value", "settings": {"foreground": "#93ddfb"}}, {"name": "rgb value", "scope": "inline-color-decoration rgb-value", "settings": {"foreground": "#dda2f6"}}, {"name": "rgb value less", "scope": "less rgb-value", "settings": {"foreground": "#dda2f6"}}, {"name": "sass selector", "scope": "selector.sass", "settings": {"foreground": "#85b1e0"}}, {"name": "ts primitive/builtin types", "scope": "support.type.primitive.ts,support.type.builtin.ts,support.type.primitive.tsx,support.type.builtin.tsx", "settings": {"foreground": "#85b1e0"}}, {"name": "block scope", "scope": "block.scope.end,block.scope.begin", "settings": {"foreground": "#b9bed5"}}, {"name": "cs storage type", "scope": "storage.type.cs", "settings": {"foreground": "#85b1e0"}}, {"name": "cs local variable", "scope": "entity.name.variable.local.cs", "settings": {"foreground": "#85b1e0"}}, {"scope": "token.info-token", "settings": {"foreground": "#f5faff"}}, {"scope": "token.warn-token", "settings": {"foreground": "#dda2f6"}}, {"scope": "token.error-token", "settings": {"foreground": "#f44747"}}, {"scope": "token.debug-token", "settings": {"foreground": "#9595e3"}}, {"name": "String interpolation", "scope": ["punctuation.definition.template-expression.begin", "punctuation.definition.template-expression.end", "punctuation.section.embedded"], "settings": {"foreground": "#9595e3"}}, {"name": "Reset JavaScript string interpolation expression", "scope": ["meta.template.expression"], "settings": {"foreground": "#b9bed5"}}, {"name": "Import module JS", "scope": ["keyword.operator.module"], "settings": {"foreground": "#9595e3"}}, {"name": "js Flowtype", "scope": ["support.type.type.flowtype"], "settings": {"foreground": "#f5faff"}}, {"name": "js Flow", "scope": ["support.type.primitive"], "settings": {"foreground": "#85b1e0"}}, {"name": "js class prop", "scope": ["meta.property.object"], "settings": {"foreground": "#85b1e0"}}, {"name": "js func parameter", "scope": ["variable.parameter.function.js"], "settings": {"foreground": "#85b1e0"}}, {"name": "js template literals begin", "scope": ["keyword.other.template.begin"], "settings": {"foreground": "#bec6f2"}}, {"name": "js template literals end", "scope": ["keyword.other.template.end"], "settings": {"foreground": "#bec6f2"}}, {"name": "js template literals variable braces begin", "scope": ["keyword.other.substitution.begin"], "settings": {"foreground": "#bec6f2"}}, {"name": "js template literals variable braces end", "scope": ["keyword.other.substitution.end"], "settings": {"foreground": "#bec6f2"}}, {"name": "js operator.assignment", "scope": ["keyword.operator.assignment"], "settings": {"foreground": "#93ddfb"}}, {"name": "go operator", "scope": ["keyword.operator.assignment.go"], "settings": {"foreground": "#85b1e0"}}, {"name": "go operator", "scope": ["keyword.operator.arithmetic.go", "keyword.operator.address.go"], "settings": {"foreground": "#9595e3"}}, {"name": "Go package name", "scope": ["entity.name.package.go"], "settings": {"foreground": "#85b1e0"}}, {"name": "elm prelude", "scope": ["support.type.prelude.elm"], "settings": {"foreground": "#93ddfb"}}, {"name": "elm constant", "scope": ["support.constant.elm"], "settings": {"foreground": "#dda2f6"}}, {"name": "template literal", "scope": ["punctuation.quasi.element"], "settings": {"foreground": "#9595e3"}}, {"name": "html/pug (jade) escaped characters and entities", "scope": ["constant.character.entity"], "settings": {"foreground": "#85b1e0"}}, {"name": "styling css pseudo-elements/classes to be able to differentiate from classes which are the same colour", "scope": ["entity.other.attribute-name.pseudo-element", "entity.other.attribute-name.pseudo-class"], "settings": {"foreground": "#93ddfb"}}, {"name": "Clojure globals", "scope": ["entity.global.clojure"], "settings": {"foreground": "#85b1e0"}}, {"name": "Clojure symbols", "scope": ["meta.symbol.clojure"], "settings": {"foreground": "#85b1e0"}}, {"name": "Clojure constants", "scope": ["constant.keyword.clojure"], "settings": {"foreground": "#93ddfb"}}, {"name": "CoffeeScript Function Argument", "scope": ["meta.arguments.coffee", "variable.parameter.function.coffee"], "settings": {"foreground": "#85b1e0"}}, {"name": "<PERSON><PERSON> Default Text", "scope": ["source.ini"], "settings": {"foreground": "#bec6f2"}}, {"name": "Makefile prerequisities", "scope": ["meta.scope.prerequisites.makefile"], "settings": {"foreground": "#85b1e0"}}, {"name": "Makefile text colour", "scope": ["source.makefile"], "settings": {"foreground": "#85b1e0"}}, {"name": "Groovy import names", "scope": ["storage.modifier.import.groovy"], "settings": {"foreground": "#85b1e0"}}, {"name": "Groovy Methods", "scope": ["meta.method.groovy"], "settings": {"foreground": "#f5faff"}}, {"name": "Groovy Variables", "scope": ["meta.definition.variable.name.groovy"], "settings": {"foreground": "#85b1e0"}}, {"name": "Groovy Inheritance", "scope": ["meta.definition.class.inherited.classes.groovy"], "settings": {"foreground": "#bec6f2"}}, {"name": "HLSL Semantic", "scope": ["support.variable.semantic.hlsl"], "settings": {"foreground": "#85b1e0"}}, {"name": "HLSL Types", "scope": ["support.type.texture.hlsl", "support.type.sampler.hlsl", "support.type.object.hlsl", "support.type.object.rw.hlsl", "support.type.fx.hlsl", "support.type.object.hlsl"], "settings": {"foreground": "#9595e3"}}, {"name": "SQL Variables", "scope": ["text.variable", "text.bracketed"], "settings": {"foreground": "#85b1e0"}}, {"name": "types", "scope": ["support.type.swift", "support.type.vb.asp"], "settings": {"foreground": "#85b1e0"}}, {"name": "heading 1, keyword", "scope": ["entity.name.function.xi"], "settings": {"foreground": "#85b1e0"}}, {"name": "heading 2, callable", "scope": ["entity.name.class.xi"], "settings": {"foreground": "#93ddfb"}}, {"name": "heading 3, property", "scope": ["constant.character.character-class.regexp.xi"], "settings": {"foreground": "#85b1e0"}}, {"name": "heading 4, type, class, interface", "scope": ["constant.regexp.xi"], "settings": {"foreground": "#9595e3"}}, {"name": "heading 5, enums, preprocessor, constant, decorator", "scope": ["keyword.control.xi"], "settings": {"foreground": "#93ddfb"}}, {"name": "heading 6, number", "scope": ["invalid.xi"], "settings": {"foreground": "#b9bed5"}}, {"name": "string", "scope": ["beginning.punctuation.definition.quote.markdown.xi"], "settings": {"foreground": "#bec6f2"}}, {"name": "comments", "scope": ["beginning.punctuation.definition.list.markdown.xi"], "settings": {"foreground": "#979cac"}}, {"name": "link", "scope": ["constant.character.xi"], "settings": {"foreground": "#f5faff"}}, {"name": "accent", "scope": ["accent.xi"], "settings": {"foreground": "#f5faff"}}, {"name": "wikiword", "scope": ["wikiword.xi"], "settings": {"foreground": "#dda2f6"}}, {"name": "language operators like '+', '-' etc", "scope": ["constant.other.color.rgb-value.xi"], "settings": {"foreground": "#ffffff"}}, {"name": "elements to dim", "scope": ["punctuation.definition.tag.xi"], "settings": {"foreground": "#979cac"}}, {"name": "C++/C#", "scope": ["entity.name.label.cs", "entity.name.scope-resolution.function.call", "entity.name.scope-resolution.function.definition"], "settings": {"foreground": "#85b1e0"}}, {"name": "Markdown underscore-style headers", "scope": ["entity.name.label.cs", "markup.heading.setext.1.markdown", "markup.heading.setext.2.markdown"], "settings": {"foreground": "#85b1e0"}}, {"name": "meta.brace.square", "scope": [" meta.brace.square"], "settings": {"foreground": "#b9bed5"}}, {"name": "Comments", "scope": "comment, punctuation.definition.comment", "settings": {"fontStyle": "italic", "foreground": "#979cac"}}, {"name": "[VSCODE-CUSTOM] Markdown Quote", "scope": "markup.quote.markdown", "settings": {"foreground": "#979cac"}}, {"name": "punctuation.definition.block.sequence.item.yaml", "scope": "punctuation.definition.block.sequence.item.yaml", "settings": {"foreground": "#b9bed5"}}, {"scope": ["constant.language.symbol.elixir"], "settings": {"foreground": "#93ddfb"}}, {"name": "js/ts italic", "scope": "entity.other.attribute-name.js,entity.other.attribute-name.ts,entity.other.attribute-name.jsx,entity.other.attribute-name.tsx,variable.parameter,variable.language.super", "settings": {"fontStyle": "italic"}}, {"name": "comment", "scope": "comment.line.double-slash,comment.block.documentation", "settings": {"fontStyle": "italic"}}, {"name": "Python Keyword Control", "scope": "keyword.control.import.python,keyword.control.flow.python", "settings": {"fontStyle": "italic"}}, {"name": "markup.italic.markdown", "scope": "markup.italic.markdown", "settings": {"fontStyle": "italic"}}], "semanticHighlighting": true, "semanticTokenColors": {"enumMember": {"foreground": "#93ddfb"}, "variable.constant": {"foreground": "#dda2f6"}, "variable.defaultLibrary": {"foreground": "#85b1e0"}}}