---
title: Font Style
banner:
  content: N/A
description: Controls the font style of an element.
slug: docs/font-style
---

<Class category="font" name="font-style" />

## Italic

This example sets the font style to **italic**. The text will be displayed in an italicized format.

```html live "fs-i" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg fs-i p-4 rad-1 ta-c tc-lead">Sphinx of black quartz, judge my vow.</p>
</div>
```

## Normal

> Initial value

This example sets the font style to **normal**. The text will be displayed in a standard format without any italicization.

```html live "fs-n" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg fs-n p-4 rad-1 ta-c tc-lead">Sphinx of black quartz, judge my vow.</p>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<BreakpointVariant class="fs-n" variant="fs-i" classPrefix="fs">
  ### Breakpoint variant
</BreakpointVariant>

<HoverVariant class="fs-n" variant="fs-i" classPrefix="fs">
  ### Hover variant
</HoverVariant>
