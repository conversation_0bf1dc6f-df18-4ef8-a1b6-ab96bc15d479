---
title: Text Indent
banner:
  content: N/A
description: Controls the indentation, or the space before a line of block text.
slug: docs/text-indent
---

<Class category="text" name="text-indent" />

## 0

> Initial value

This example sets the text indent to **0px**. The text will have no indentation at the start of the paragraph.

```html live "ti-0" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg p-4 rad-1 tc-lead ti-0">Sphinx of black quartz, judge my vow.</p>
</div>
```

## 1

This example sets the text indent to **1px**. The text will be indented slightly at the start of the paragraph.

```html live "ti-1" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg p-4 rad-1 tc-lead ti-1">Sphinx of black quartz, judge my vow.</p>
</div>
```

## 2

This example sets the text indent to **0.25rem**. The text will be indented a small amount at the start of the paragraph.

```html live "ti-2" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg p-4 rad-1 tc-lead ti-2">Sphinx of black quartz, judge my vow.</p>
</div>
```

## 3

This example sets the text indent to **0.5rem**. The text will be indented moderately at the start of the paragraph.

```html live "ti-3" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg p-4 rad-1 tc-lead ti-3">Sphinx of black quartz, judge my vow.</p>
</div>
```

## 4

This example sets the text indent to **0.75rem**. The text will be indented more significantly at the start of the paragraph.

```html live "ti-4" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg p-4 rad-1 tc-lead ti-4">Sphinx of black quartz, judge my vow.</p>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<BreakpointVariant class="ti-1" variant="ti-2" classPrefix="ti">
  ### Breakpoint variant
</BreakpointVariant>

<HoverVariant class="ti-1" variant="ti-2" classPrefix="ti">
  ### Hover variant
</HoverVariant>
