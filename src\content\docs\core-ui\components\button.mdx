---
title: Button
description: An interactive element used to trigger user actions.
slug: docs/ui/button
draft: true
---

## Examples

A basic implementation of the Button component with default styling.

```tsx showLineNumbers
import { Button } from "yumma-ui";

const App = () => {
  return (
    <Button variant="base" size="md">
      Login
    </Button>
  );
};

export default App;
```

## Usage

```tsx showLineNumbers
import { Button } from "yumma-ui";
```

```tsx showLineNumbers
<Button>Login</Button>
```

## API reference

The Button component can be adapted to suit specific requirements by utilizing the internal API of Yumma UI, thereby enabling the modification of its visual appearance and operational characteristics.

### `variant`

> Default value is `base`

The `variant` property allows you to choose from different pre-defined styles that match your design system.

```ts
(property) variant?: "link" | "destructive" | "base" | "ghost" | "outlined" | null | undefined
```

### `size`

> Default value is `md`

The `size` property allows you to choose from different pre-defined sizes that match your design system.

```ts
(property) size?: "sm" | "md" | "lg" | null | undefined
```

## Custom styling

The Button component exposes a simple API to control its look and behavior through props.

```tsx showLineNumbers 'className="..."'
import { Button } from "yumma-ui";

<Button className="...">Login</Button>;
```

## Extend properties

The Button component is fully extendable. It supports all native HTML `<button>` attributes through its props:

```tsx showLineNumbers "onClick={() => alert("Button clicked!")}"
import { Button } from "yumma-ui";

const App = () => {
  return (
    <Button onClick={() => alert("Button clicked!")} {...props}>
      Login
    </Button>
  );
};

export default App;
```
