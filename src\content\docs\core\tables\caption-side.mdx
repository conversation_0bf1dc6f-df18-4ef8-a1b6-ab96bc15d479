---
title: Caption Side
banner:
  content: N/A
description: Controls the caption element orientation in a table.
slug: docs/caption-side
---

<Class category="table" name="caption-side" />

## Bottom

This example sets the caption side to **bottom**. The caption will be displayed below the table.

```html live "cs-b" layout="/src/layouts/inline.astro"
<table class="b-1 bc-c bc-indigo ta-c w-full">
  <caption class="cs-b p-2">
    Bottom Caption
  </caption>
  <tbody>
    <tr>
      <td class="b-1 bc-indigo p-3 tc-indigo">A</td>
      <td class="b-1 bc-indigo p-3 tc-indigo">B</td>
      <td class="b-1 bc-indigo p-3 tc-indigo">C</td>
    </tr>
  </tbody>
</table>
```

## Top

> Initial value

This example sets the caption side to **top**. The caption will be displayed above the table.

```html live "cs-t" layout="/src/layouts/inline.astro"
<table class="b-1 bc-c bc-indigo ta-c w-full">
  <caption class="cs-t p-2">
    Top Caption
  </caption>
  <tbody>
    <tr>
      <td class="b-1 bc-indigo p-3 tc-indigo">A</td>
      <td class="b-1 bc-indigo p-3 tc-indigo">B</td>
      <td class="b-1 bc-indigo p-3 tc-indigo">C</td>
    </tr>
  </tbody>
</table>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<MediaModifier class="cs-b" mediaModifier="cs-t" classPrefix="cs">
  ### Breakpoint modifier
</MediaModifier>

<HoverModifier class="cs-b" hoverModifier="cs-t" classPrefix="cs">
  ### Hover modifier
</HoverModifier>
