---
title: Outline Color
banner:
  content: N/A
description: Controls the color of the outline of an element.
slug: docs/outline-color
---

<Class category="color" name="outline-color" />

This example showcases various outline colors using the **indigo** colors from the default color palette.

- The **oc-indigo-3** utility applies a lighter indigo shade to the outline of an element.
- The **oc-indigo** utility applies the base indigo shade to the outline of an element.
- The **oc-indigo-8** utility applies a darker indigo shade to the outline of an element.

```html live "oc-indigo" "oc-indigo-3" "oc-indigo-8"
<div class="d-g g-16 gtc-1 sm:gtc-3">
  <div class="b-1 bc-indigo d-16 oc-indigo-3 oo-1 os-s rad-1"></div>
  <div class="b-1 bc-indigo d-16 oc-indigo oo-1 os-s rad-1"></div>
  <div class="b-1 bc-indigo d-16 oc-indigo-8 oo-1 os-s rad-1"></div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<HoverVariant class="oc-lead" variant="oc-indigo" classPrefix="oc">
  ### Hover variant
</HoverVariant>
