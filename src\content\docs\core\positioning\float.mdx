---
title: Float
banner:
  content: N/A
description: Controls the content wrapping around an element.
slug: docs/float
---

<Class category="positioning" name="float" />

## Inline End

This example sets the float property to **inline-end**. The element will float to the end of the inline direction, which is typically the right side in left-to-right languages.

```html live "fl-ie" layout="/src/layouts/inline.astro"
<div class="h-14 p-r tc-white" id="area">
  <div class="ai-c bg-indigo d-f d-14 fl-ie jc-c rad-1">A</div>
</div>
```

## Inline Start

This example sets the float property to **inline-start**. The element will float to the start of the inline direction, which is typically the left side in left-to-right languages.

```html live "fl-is" layout="/src/layouts/inline.astro"
<div class="h-14 p-r tc-white" id="area">
  <div class="ai-c bg-indigo d-f d-14 fl-is jc-c rad-1">A</div>
</div>
```

## Left

This example sets the float property to **left**. The element will float to the left side of its container, allowing text and inline elements to wrap around it.

```html live "fl-l" layout="/src/layouts/inline.astro"
<div class="h-14 p-r tc-white" id="area">
  <div class="ai-c bg-indigo d-f d-14 fl-l jc-c rad-1">A</div>
</div>
```

## None

> Initial value

This example sets the float property to **none**. The element will not float, and it will be displayed in the normal flow of the document.

```html live "fl-none" layout="/src/layouts/inline.astro"
<div class="h-14 p-r tc-white" id="area">
  <div class="ai-c bg-indigo d-f d-14 fl-none jc-c rad-1">A</div>
</div>
```

## Right

This example sets the float property to **right**. The element will float to the right side of its container, allowing text and inline elements to wrap around it.

```html live "fl-r" layout="/src/layouts/inline.astro"
<div class="h-14 p-r tc-white" id="area">
  <div class="ai-c bg-indigo d-f d-14 fl-r jc-c rad-1">A</div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<MediaModifier class="fl-l" mediaModifier="fl-r" classPrefix="fl">
  ### Breakpoint modifier
</MediaModifier>

<HoverModifier class="fl-l" hoverModifier="fl-r" classPrefix="fl">
  ### Hover modifier
</HoverModifier>
