---
title: Aspect Ratio
banner:
  content: N/A
description: Controls the aspect ratio of an element.
slug: docs/aspect-ratio
---

<Class category="positioning" name="aspect-ratio" />

## Auto

> Initial value

This example sets the aspect ratio to **auto**. The **ar-auto** utility allows the element to maintain its default aspect ratio based on its content.

```html live "ar-auto"
<div class="ar-auto h-32">
  <img class="d-full of-c rad-1" src="https://picsum.photos/600/400?image=106" />
</div>
```

## 1/1

This example sets the aspect ratio to **1/1**. The **ar-1/1** utility enforces a square aspect ratio.

```html live "ar-1/1"
<div class="ar-1/1 h-32">
  <img class="d-full of-c rad-1" src="https://picsum.photos/600/400?image=106" />
</div>
```

## 1/2

This example sets the aspect ratio to **1/2**. The **ar-1/2** utility enforces a vertical aspect ratio.

```html live "ar-1/2"
<div class="ar-1/2 h-32">
  <img class="d-full of-c rad-1" src="https://picsum.photos/600/400?image=106" />
</div>
```

## 16/9

This example sets the aspect ratio to **16/9**. The **ar-16/9** utility enforces a widescreen aspect ratio.

```html live "ar-16/9"
<div class="ar-16/9 h-32">
  <img class="d-full of-c rad-1" src="https://picsum.photos/600/400?image=106" />
</div>
```

## 2/1

This example sets the aspect ratio to **2/1**. The **ar-2/1** utility enforces a horizontal aspect ratio.

```html live "ar-2/1"
<div class="ar-2/1 h-32">
  <img class="d-full of-c rad-1" src="https://picsum.photos/600/400?image=106" />
</div>
```

## 2/3

This example sets the aspect ratio to **2/3**. The **ar-2/3** utility enforces a vertical aspect ratio.

```html live "ar-2/3"
<div class="ar-2/3 h-32">
  <img class="d-full of-c rad-1" src="https://picsum.photos/600/400?image=106" />
</div>
```

## 3/2

This example sets the aspect ratio to **3/2**. The **ar-3/2** utility enforces a horizontal aspect ratio.

```html live "ar-3/2"
<div class="ar-3/2 h-32">
  <img class="d-full of-c rad-1" src="https://picsum.photos/600/400?image=106" />
</div>
```

## 9/16

This example sets the aspect ratio to **9/16**. The **ar-9/16** utility enforces a vertical aspect ratio.

```html live "ar-9/16"
<div class="ar-9/16 h-32">
  <img class="d-full of-c rad-1" src="https://picsum.photos/600/400?image=106" />
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/foundations/responsive-design/) or other factors, such as [hover states](/docs/foundations/variants/).

<BreakpointVariant class="ar-1/1" variant="ar-16/9" classPrefix="ar">
  ### Breakpoint variant
</BreakpointVariant>

<HoverVariant class="ar-1/1" variant="ar-16/9" classPrefix="ar">
  ### Hover variant
</HoverVariant>
