---
title: Text Align
banner:
  content: N/A
description: Controls the alignment of text.
slug: docs/text-align
---

<Class category="text" name="text-align" />

## Center

This example sets the text alignment to **center**. The text will be centered within its container.

```html live "ta-c" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-md p-4 rad-1 ta-c tc-lead">
    Hi, <PERSON>. It's finally springtime here on Earth! I can't stand windy or cold days, so I'm very excited for the spring. There are so many beautiful, colorful trees where I live.
    I'll try to send you some pictures I took with the camera you gave me for my birthday. I've been a little under the weather lately, but I'll bounce back in no time so we can
    meet up and hug each other again. I can't wait to go to the grocery store with you like we did a decade ago. I hope you feel the same way and are as excited as much as I am to
    see you tomorrow night. I love you, and take care. — Vigo
  </p>
</div>
```

## End

This example sets the text alignment to **end**. The text will be aligned to the end of the container, which is typically the right side-to-right languages.

```html live "ta-e" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-md p-4 rad-1 ta-e tc-lead">
    Hi, Anne. It's finally springtime here on Earth! I can't stand windy or cold days, so I'm very excited for the spring. There are so many beautiful, colorful trees where I live.
    I'll try to send you some pictures I took with the camera you gave me for my birthday. I've been a little under the weather lately, but I'll bounce back in no time so we can
    meet up and hug each other again. I can't wait to go to the grocery store with you like we did a decade ago. I hope you feel the same way and are as excited as much as I am to
    see you tomorrow night. I love you, and take care. — Vigo
  </p>
</div>
```

## Justify

This example sets the text alignment to **justify**. The text will be stretched to align with both and right edges of the container, creating a clean look.

```html live "ta-j" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-md p-4 rad-1 ta-j tc-lead">
    Hi, Anne. It's finally springtime here on Earth! I can't stand windy or cold days, so I'm very excited for the spring. There are so many beautiful, colorful trees where I live.
    I'll try to send you some pictures I took with the camera you gave me for my birthday. I've been a little under the weather lately, but I'll bounce back in no time so we can
    meet up and hug each other again. I can't wait to go to the grocery store with you like we did a decade ago. I hope you feel the same way and are as excited as much as I am to
    see you tomorrow night. I love you, and take care. — Vigo
  </p>
</div>
```

## Justify All

This example sets the text alignment to **justify-all**. The text will be justified, including the last line, which will also stretch to fill the width of the container.

```html live "ta-ja" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-md p-4 rad-1 ta-ja tc-lead">
    Hi, Anne. It's finally springtime here on Earth! I can't stand windy or cold days, so I'm very excited for the spring. There are so many beautiful, colorful trees where I live.
    I'll try to send you some pictures I took with the camera you gave me for my birthday. I've been a little under the weather lately, but I'll bounce back in no time so we can
    meet up and hug each other again. I can't wait to go to the grocery store with you like we did a decade ago. I hope you feel the same way and are as excited as much as I am to
    see you tomorrow night. I love you, and take care. — Vigo
  </p>
</div>
```

This example sets the text alignment t\*\*. The text will be aligned to side of the container, which is the default alignment in most cases.

```html live "ta-l" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-md p-4 rad-1 ta-l tc-lead">
    Hi, Anne. It's finally springtime here on Earth! I can't stand windy or cold days, so I'm very excited for the spring. There are so many beautiful, colorful trees where I live.
    I'll try to send you some pictures I took with the camera you gave me for my birthday. I've been a little under the weather lately, but I'll bounce back in no time so we can
    meet up and hug each other again. I can't wait to go to the grocery store with you like we did a decade ago. I hope you feel the same way and are as excited as much as I am to
    see you tomorrow night. I love you, and take care. — Vigo
  </p>
</div>
```

## Match Parent

This example sets the text alignment to **match-parent**. The text will inherit the text alignment from its parent element.

```html live "ta-mp" "ta-j" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1 ta-j">
  <p class="bg-white fs-md p-4 rad-1 ta-mp tc-lead">
    Hi, Anne. It's finally springtime here on Earth! I can't stand windy or cold days, so I'm very excited for the spring. There are so many beautiful, colorful trees where I live.
    I'll try to send you some pictures I took with the camera you gave me for my birthday. I've been a little under the weather lately, but I'll bounce back in no time so we can
    meet up and hug each other again. I can't wait to go to the grocery store with you like we did a decade ago. I hope you feel the same way and are as excited as much as I am to
    see you tomorrow night. I love you, and take care. — Vigo
  </p>
</div>
```

## Right

This example sets the text alignment to **right**. The text will be aligned to the right side of the container.

```html live "ta-r" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-md p-4 rad-1 ta-r tc-lead">
    Hi, Anne. It's finally springtime here on Earth! I can't stand windy or cold days, so I'm very excited for the spring. There are so many beautiful, colorful trees where I live.
    I'll try to send you some pictures I took with the camera you gave me for my birthday. I've been a little under the weather lately, but I'll bounce back in no time so we can
    meet up and hug each other again. I can't wait to go to the grocery store with you like we did a decade ago. I hope you feel the same way and are as excited as much as I am to
    see you tomorrow night. I love you, and take care. — Vigo
  </p>
</div>
```

## Start

> Initial value

This example sets the text alignment to **start**. The text will be aligned to the start of the container, which is typically side-to-right languages.

```html live "ta-s" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-md p-4 rad-1 ta-s tc-lead">
    Hi, Anne. It's finally springtime here on Earth! I can't stand windy or cold days, so I'm very excited for the spring. There are so many beautiful, colorful trees where I live.
    I'll try to send you some pictures I took with the camera you gave me for my birthday. I've been a little under the weather lately, but I'll bounce back in no time so we can
    meet up and hug each other again. I can't wait to go to the grocery store with you like we did a decade ago. I hope you feel the same way and are as excited as much as I am to
    see you tomorrow night. I love you, and take care. — Vigo
  </p>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<MediaModifier class="ta-l" mediaModifier="ta-c" classPrefix="ta">
  ### Media modifier
</MediaModifier>

<HoverModifier class="ta-l" hoverModifier="ta-c" classPrefix="ta">
  ### Hover modifier
</HoverModifier>
