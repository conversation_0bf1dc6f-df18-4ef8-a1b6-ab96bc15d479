---
title: Text Decoration Color
banner:
  content: N/A
description: Controls the color of the text decoration.
slug: docs/text-decoration-color
---

<Class category="color" name="text-decoration-color" />

This example showcases various `text-decoration-color` utilities:

- The **tdc-teal** utility applies the base teal shade.
- The **tdc-red** utility applies the base red shade.
- The **tdc-yellow** utility applies the base yellow shade.

```html live "tdc-yellow" "tdc-red" "tdc-teal" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <div class="bg-white p-4 rad-1 ta-j tc-lead">
    <span class="h:bg-red-1 tdc-red tdl-u tdt-2">Sup</span>, Anne. It's finally springtime here on Earth! I can't stand windy or cold days, so I'm very excited for the spring.
    There are so many beautiful, colorful trees where I live. <span class="h:bg-yellow-1 tdc-yellow tdl-u tdt-2">I will try</span> to send you some pictures I took with the camera
    you gave me for my birthday. I've been a little under the weather lately, but <span class="h:bg-yellow-1 tdc-yellow tdl-u tdt-2">I'll bounce back</span> in no time so we can
    meet up and hug each other again. I can't wait to go to the grocery store with you like we did a decade ago. I hope you feel the same way and are as excited as much as I am to
    see you tomorrow night. I love you, and take care.
    <span class="h:bg-teal-1 tdc-teal tdl-u tdt-2">— Vigo</span>
  </div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/foundations/responsive-design/) or other factors, such as [hover states](/docs/foundations/variants/).

<HoverVariant class="tdc-lead" variant="tdc-teal" classPrefix="tdc">
  ### Hover variant
</HoverVariant>
