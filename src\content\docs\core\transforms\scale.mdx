---
title: Scale
banner:
  content: N/A
description: Controls how an element changes size on a 2D plane.
slug: docs/scale
---

<Class category="transform" name="scale" />

This example showcases various `scale()` utilities:

- The **t-s-70** utility scales the element to **70%** of its original size.
- The **t-s-80** utility scales the element to **80%** of its original size.
- The **t-s-90** utility scales the element to **90%** of its original size.

```html live "t-s-70" "t-s-80" "t-s-90"
<div class="d-g g-16 gtc-1 sm:gtc-3">
  <div class="my-4 p-r">
    <img class="d-18 d-ib rad-1 t-s-70" src="https://picsum.photos/600?image=360" />
    <img class="o-20 d-18 d-ib l-0 p-a rad-1" src="https://picsum.photos/600?image=360" />
  </div>
  <div class="my-4 p-r">
    <img class="d-18 d-ib rad-1 t-s-80" src="https://picsum.photos/600?image=360" />
    <img class="o-20 d-18 d-ib l-0 p-a rad-1" src="https://picsum.photos/600?image=360" />
  </div>
  <div class="my-4 p-r">
    <img class="d-18 d-ib rad-1 t-s-90" src="https://picsum.photos/600?image=360" />
    <img class="o-20 d-18 d-ib l-0 p-a rad-1" src="https://picsum.photos/600?image=360" />
  </div>
</div>
```

## Scale X

This example showcases various horizontal scaling transformations:

- The **t-sx-70** utility scales the element horizontally to **70%** of its original width.
- The **t-sx-80** utility scales the element horizontally to **80%** of its original width.
- The **t-sx-90** utility scales the element horizontally to **90%** of its original width.

```html live "t-sx-70" "t-sx-80" "t-sx-90"
<div class="d-g g-16 gtc-1 sm:gtc-3">
  <div class="my-4 p-r">
    <img class="d-18 d-ib rad-1 t-sx-70" src="https://picsum.photos/600?image=360" />
    <img class="o-20 d-18 d-ib l-0 p-a rad-1" src="https://picsum.photos/600?image=360" />
  </div>
  <div class="my-4 p-r">
    <img class="d-18 d-ib rad-1 t-sx-80" src="https://picsum.photos/600?image=360" />
    <img class="o-20 d-18 d-ib l-0 p-a rad-1" src="https://picsum.photos/600?image=360" />
  </div>
  <div class="my-4 p-r">
    <img class="d-18 d-ib rad-1 t-sx-90" src="https://picsum.photos/600?image=360" />
    <img class="o-20 d-18 d-ib l-0 p-a rad-1" src="https://picsum.photos/600?image=360" />
  </div>
</div>
```

## Scale Y

This example showcases various vertical scaling transformations:

- The **t-sy-70** utility scales the element vertically to **70%** of its original height.
- The **t-sy-80** utility scales the element vertically to **80%** of its original height.
- The **t-sy-90** utility scales the element vertically to **90%** of its original height.

```html live "t-sy-70" "t-sy-80" "t-sy-90"
<div class="d-g g-16 gtc-1 sm:gtc-3">
  <div class="my-4 p-r">
    <img class="d-18 d-ib rad-1 t-sy-70" src="https://picsum.photos/600?image=360" />
    <img class="o-20 d-18 d-ib l-0 p-a rad-1" src="https://picsum.photos/600?image=360" />
  </div>
  <div class="my-4 p-r">
    <img class="d-18 d-ib rad-1 t-sy-80" src="https://picsum.photos/600?image=360" />
    <img class="o-20 d-18 d-ib l-0 p-a rad-1" src="https://picsum.photos/600?image=360" />
  </div>
  <div class="my-4 p-r">
    <img class="d-18 d-ib rad-1 t-sy-90" src="https://picsum.photos/600?image=360" />
    <img class="o-20 d-18 d-ib l-0 p-a rad-1" src="https://picsum.photos/600?image=360" />
  </div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<MediaModifier class="t-s-10" mediaModifier="t-s-20" classPrefix="t-s">
  ### Media modifier
</MediaModifier>

<HoverModifier class="t-s-10" hoverModifier="t-s-20" classPrefix="t-s">
  ### Hover modifier
</HoverModifier>
