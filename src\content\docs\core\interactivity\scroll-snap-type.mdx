---
title: <PERSON><PERSON> Snap Type
banner:
  content: N/A
description: Controls the strictness of snap points in a snap container.
slug: docs/scroll-snap-type
---

<Class category="interactivity" name="scroll-snap-type" />

## Both Mandatory

This example sets the scroll snap type to **both mandatory**. The **sst-b-m** utility enables snapping in both horizontal and vertical directions, requiring the scroll to stop at defined snap points.

<Note icon="cursor_arrow">Try scrolling through the image container to see how it behaves.</Note>

```html live "sst-b-m" layout="/src/layouts/inline.astro"
<div class="o-h p-r">
  <div class="d-f g-6 o-x-s p-6 sst-b-m">
    <div class="fs-0 p-6 p-r sm-6 ssa-s">
      <img class="h-40 p-r rad-1 w-80 zi-10" src="https://picsum.photos/400/200?image=849" />
    </div>
    <div class="fs-0 p-6 p-r sm-6 ssa-s">
      <img class="h-40 p-r rad-1 w-80 zi-10" src="https://picsum.photos/400/200?image=866" />
    </div>
    <div class="fs-0 p-6 p-r sm-6 ssa-s">
      <img class="h-40 p-r rad-1 w-80 zi-10" src="https://picsum.photos/400/200?image=872" />
    </div>
    <div class="fs-0 p-6 p-r sm-6 ssa-s">
      <img class="h-40 p-r rad-1 w-80 zi-10" src="https://picsum.photos/400/200?image=875" />
    </div>
  </div>
</div>
```

## None

> Initial value

This example sets the scroll snap type to **none**. The **sst-none** utility disables any snapping behavior, allowing for free scrolling without restrictions.

<Note icon="cursor_arrow">Try scrolling through the image container to see how it behaves.</Note>

```html live "sst-none" layout="/src/layouts/inline.astro"
<div class="o-h p-r">
  <div class="d-f g-6 o-x-s p-6 sst-none">
    <div class="fs-0 p-6 p-r sm-6 ssa-none">
      <img class="h-40 p-r rad-1 w-80 zi-10" src="https://picsum.photos/400/200?image=849" />
    </div>
    <div class="fs-0 p-6 p-r sm-6 ssa-s">
      <img class="h-40 p-r rad-1 w-80 zi-10" src="https://picsum.photos/400/200?image=866" />
    </div>
    <div class="fs-0 p-6 p-r sm-6 ssa-s">
      <img class="h-40 p-r rad-1 w-80 zi-10" src="https://picsum.photos/400/200?image=872" />
    </div>
    <div class="fs-0 p-6 p-r sm-6 ssa-s">
      <img class="h-40 p-r rad-1 w-80 zi-10" src="https://picsum.photos/400/200?image=875" />
    </div>
  </div>
</div>
```

## X Mandatory

This example sets the scroll snap type to **x mandatory**. The **sst-x-m** utility enables snapping in the horizontal direction, requiring the scroll to stop at defined snap points along the x-axis.

<Note icon="cursor_arrow">Try scrolling through the image container to see how it behaves.</Note>

```html live "sst-x-m" layout="/src/layouts/inline.astro"
<div class="o-h p-r">
  <div class="d-f g-6 o-x-s p-6 sst-x-m">
    <div class="fs-0 p-6 p-r sm-6 ssa-s">
      <img class="h-40 p-r rad-1 w-80 zi-10" src="https://picsum.photos/400/200?image=849" />
    </div>
    <div class="fs-0 p-6 p-r sm-6 ssa-s">
      <img class="h-40 p-r rad-1 w-80 zi-10" src="https://picsum.photos/400/200?image=866" />
    </div>
    <div class="fs-0 p-6 p-r sm-6 ssa-s">
      <img class="h-40 p-r rad-1 w-80 zi-10" src="https://picsum.photos/400/200?image=872" />
    </div>
    <div class="fs-0 p-6 p-r sm-6 ssa-s">
      <img class="h-40 p-r rad-1 w-80 zi-10" src="https://picsum.photos/400/200?image=875" />
    </div>
  </div>
</div>
```

## X Proximity

This example sets the scroll snap type to **x proximity**. The **sst-x-p** utility enables snapping in the horizontal direction, allowing the scroll to stop at defined snap points only when the user scrolls slowly.

<Note icon="cursor_arrow">Try scrolling through the image container to see how it behaves.</Note>

```html live "sst-x-p" layout="/src/layouts/inline.astro"
<div class="o-h p-r">
  <div class="d-f g-6 o-x-s p-6 sst-x-p">
    <div class="fs-0 p-6 p-r sm-6 ssa-s">
      <img class="h-40 p-r rad-1 w-80 zi-10" src="https://picsum.photos/400/200?image=849" />
    </div>
    <div class="fs-0 p-6 p-r sm-6 ssa-s">
      <img class="h-40 p-r rad-1 w-80 zi-10" src="https://picsum.photos/400/200?image=866" />
    </div>
    <div class="fs-0 p-6 p-r sm-6 ssa-s">
      <img class="h-40 p-r rad-1 w-80 zi-10" src="https://picsum.photos/400/200?image=872" />
    </div>
    <div class="fs-0 p-6 p-r sm-6 ssa-s">
      <img class="h-40 p-r rad-1 w-80 zi-10" src="https://picsum.photos/400/200?image=875" />
    </div>
  </div>
</div>
```

## Y Mandatory

This example sets the scroll snap type to **y mandatory**. The **sst-y-m** utility enables snapping in the vertical direction, requiring the scroll to stop at defined snap points along the y-axis.

<Note icon="cursor_arrow">Try scrolling through the image container to see how it behaves.</Note>

```html live "sst-y-m"
<div class="o-h p-r w-fc">
  <div class="d-f fd-c g-6 h-96 o-y-auto p-6 sst-y-m">
    <div class="fs-0 p-r pb-6 smb-6 ssa-s">
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=849" />
    </div>
    <div class="fs-0 p-r pb-6 smb-6 ssa-s">
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=866" />
    </div>
    <div class="fs-0 p-r pb-6 smb-6 ssa-s">
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=872" />
    </div>
    <div class="fs-0 p-r pb-6 smb-6 ssa-s">
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=875" />
    </div>
  </div>
</div>
```

## Y Proximity

This example sets the scroll snap type to **y proximity**. The **sst-y-p** utility enables snapping in the vertical direction, allowing the scroll to stop at defined snap points only when the user scrolls slowly.

<Note icon="cursor_arrow">Try scrolling through the image container to see how it behaves.</Note>

```html live "sst-y-p"
<div class="o-h p-r w-fc">
  <div class="d-f fd-c g-6 h-96 o-y-auto p-6 sst-y-p">
    <div class="fs-0 p-r pb-6 smb-6 ssa-s">
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=849" />
    </div>
    <div class="fs-0 p-r pb-6 smb-6 ssa-s">
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=866" />
    </div>
    <div class="fs-0 p-r pb-6 smb-6 ssa-s">
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=872" />
    </div>
    <div class="fs-0 p-r pb-6 smb-6 ssa-s">
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=875" />
    </div>
  </div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<MediaModifier class="sst-none" mediaModifier="sst-b-m" classPrefix="sst">
  ### Media modifier
</MediaModifier>

<HoverModifier class="sst-none" hoverModifier="sst-b-m" classPrefix="sst">
  ### Hover modifier
</HoverModifier>
