---
authors: Renildo
cover:
  alt: Yumma CSS
  image: cover.png
date: 2024-03-21
description: We've been planning to revamp the color palette of Yumma CSS for a while, happy to announce the new version [Yumma CSS v1.2.0](https://github.com/yumma-lib/yumma-css/releases/tag/v1.2.0)! It has a reworked color palette, new variants for dimension classes, line height classes, and more.
pagefind: false
slug: blog/yummacss-1.2
tags: ["release"]
title: Yumma CSS 1.2
---

We've been planning to revamp the color palette of Yumma CSS for a while, happy to announce the new version [Yumma CSS v1.2.0](https://github.com/yumma-lib/yumma-css/releases/tag/v1.2.0)! It has a reworked color palette, new variants for dimension classes, line height classes, and more.

{/* excerpt */}

You may also want to take a look at some of the [release notes](https://github.com/yumma-lib/yumma-css/releases/tag/v1.2.0) but, anyway these are the most noticeable shifts:

- [All-new utilities](#all-new-utilities): Heights, Widths, Flex, Box Shadow and Line Height.
- [New Colors](#new-colors): New palette with more vivid colors.
- [Dimension Modifiers](#dimension-modifiers): Dimension utility class media query extension.

This is an incremental update that may contain bug fixes. Minor releases follow [semantic versioning](https://docs.npmjs.com/about-semantic-versioning) conventions. In other words, this should be an easy update for you.

---

## All-new utilities

### Box Model

It's been a while since we've seen new utility classes for `height`, `width`, and `dimension`. Well, here it is — `fit-content`!

<LegacyClass
  data={[
    {
      className: "h-fc",
      properties: ["height: fit-content;"],
    },
    {
      className: "w-fc",
      properties: ["width: fit-content;"],
    },
    {
      className: "max-h-fc",
      properties: ["max-height: fit-content;"],
    },
    {
      className: "min-h-fc",
      properties: ["min-height: fit-content;"],
    },
    {
      className: "max-w-*",
      properties: ["max-width: fit-content;"],
    },
    {
      className: "min-w-*",
      properties: ["min-width: fit-content;"],
    },
    {
      className: "dim-1",
      properties: ["height: 0.25rem;", "width: 0.25rem;"],
    },
    {
      className: "max-dim-1",
      properties: ["max-height: 0.25rem;", "max-width: 0.25rem;"],
    },
    {
      className: "min-dim-1",
      properties: ["min-height: 0.25rem;", "min-width: 0.25rem;"],
    },
  ]}
/>

### Flex

We've added `auto`, `full` and `half`

<LegacyClass
  data={[
    {
      className: "f-auto",
      properties: ["flex: auto;"],
    },
    {
      className: "f-full",
      properties: ["flex: 100%;"],
    },
    {
      className: "f-half",
      properties: ["flex: 50%;"],
    },
  ]}
/>

### Line Height

We've added some new classes for Line Height, which makes it easier to differentiate between text styles.

<LegacyClass
  data={[
    {
      className: "lh-1",
      properties: ["line-height: 1;"],
    },
    {
      className: "lh-2",
      properties: ["line-height: 1.25;"],
    },
    {
      className: "lh-3",
      properties: ["line-height: 1.375;"],
    },
    {
      className: "lh-4",
      properties: ["line-height: 1.5;"],
    },
    {
      className: "lh-5",
      properties: ["line-height: 1.625;"],
    },
    {
      className: "lh-6",
      properties: ["line-height: 2;"],
    },
  ]}
/>

## New Colors

In the past, the colors weren't as rich as we wanted them to be, which was a bit of a limitation because colors with less contrast can go unnoticed in many scenarios.

<Tabs>
  <TabItem icon="approve-check-circle" label="Now">
    <LegacyPalette
      data={[
        {
          name: "Blue",
          color: "#3575dd",
        },
        {
          name: "Indigo",
          color: "#595cd9",
        },
      ]}
    />
  </TabItem>
  <TabItem icon="close" label="Previously">
    <LegacyPalette
      data={[
        {
          name: "Blue",
          color: "#438cca",
        },
        {
          name: "Indigo",
          color: "#595cd9",
        },
      ]}
    />
  </TabItem>
</Tabs>

### Transparent Support

Oh, another thing, though it's not a color, Yumma CSS v1.2 now supports — `transparent`.

---

## Dimension Modifiers

Yumma CSS is working on getting around this responsive issue, but for now, let's focus on what we've got. We've added media queries for the `dim-*`, `max-dim-*`, and `min-dim-*` classes.

---

## Upgrade

You can upgrade your projects by getting the latest version of `yummacss` from npm:

<PackageManagers pkgManagers={["pnpm", "npm", "yarn"]} pkg="yummacss@latest" title="Terminal" />
