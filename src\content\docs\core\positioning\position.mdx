---
title: Position
banner:
  content: N/A
description: Controls the positioning of elements.
slug: docs/position
---

<Class category="positioning" name="position" />

## Absolute

This example sets the position to **absolute**. The element is positioned relative to its nearest positioned ancestor (not static), allowing for precise placement.

```html live "p-a" layout="/src/layouts/inline.astro"
<div class="bg-indigo-2 h-32 p-4 p-r tc-white">
  <div class="bg-indigo l-4 t-4 p-4 p-a">Absolute Position</div>
</div>
```

## Fixed

This example sets the position to **fixed**. The element is positioned relative to the viewport, meaning it stays in the same place even when the page is scrolled.

```html "p-f" layout="/src/layouts/inline.astro"
<div class="tc-white">
  <div class="bg-indigo r-4 t-4 p-4 p-f">Fixed Position</div>
  <div class="bg-indigo-8 h-s p-4">Scroll down to see the fixed element.</div>
</div>
```

## Relative

This example sets the position to **relative**. The element is positioned relative to its normal position, allowing for adjustments without affecting the layout of surrounding elements.

```html live "p-r" layout="/src/layouts/inline.astro"
<div class="tc-white">
  <div class="bg-indigo l-4 t-4 p-4 p-r">Relative Position</div>
  <div class="bg-indigo-2 p-4">Another Element</div>
</div>
```

## Static

> Initial value

This example sets the position to **static**. The element is positioned according to the normal flow of the document, and top, right, bottom, and left properties have no effect.

```html live "p-s" layout="/src/layouts/inline.astro"
<div class="tc-white">
  <div class="bg-indigo p-4 p-s">Static Position</div>
  <div class="bg-indigo-2 p-4">Another Element</div>
</div>
```

## Sticky

This example sets the position to **sticky**. The element is treated as relative until it crosses a specified threshold, at which point it is treated as fixed.

<Note icon="cursor_arrow">Try scrolling through the container to see how the sticky position works.</Note>

```html live "p-st" layout="/src/layouts/inline.astro"
<div class="bo-1 h-64 o-y-s tc-white">
  <div class="bg-indigo t-0 p-4 p-st">Sticky Position</div>
  <div class="bg-indigo-2 h-96 p-4">Scroll down to see the sticky behavior.</div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/foundations/responsive-design/) or other factors, such as [hover states](/docs/foundations/variants/).

<BreakpointVariant class="p-a" variant="p-s" classPrefix="p">
  ### Breakpoint variant
</BreakpointVariant>

<HoverVariant class="p-a" variant="p-s" classPrefix="p">
  ### Hover variant
</HoverVariant>
