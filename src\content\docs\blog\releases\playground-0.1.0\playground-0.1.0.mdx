---
authors: Renildo
date: 2024-12-08
description: We've got a quick update for you today on the Yumma CSS Play website. It's a small but really useful quality-of-life improvement.
pagefind: false
slug: blog/playground-0.1.0
title: Playground 0.1
---

We've got a quick update for you today on the Yumma CSS Play website. It's a small but really useful quality-of-life improvement.

<iframe
  allowfullscreen
  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
  class="ar-16/9 rad-1 w-full"
  frameborder="0"
  referrerpolicy="strict-origin-when-cross-origin"
  src="https://youtube.com/embed/IoFSJtqo9No?si=0WlpscTmqMmvPtf0"
  title="What's new in Yumma CSS Play 0.1.0?"></iframe>

{/* excerpt */}

You may also want to take a look at some of the [release notes](https://github.com/yumma-lib/yumma-css-play/releases/tag/v0.1.0) but, anyway, these are the most noticeable shifts:

- [Utility Completions](#utility-completions): Access helpful completions while typing.

This is an incremental update that may contain bug fixes. Minor releases follow [semantic versioning](https://docs.npmjs.com/about-semantic-versioning) conventions. In other words, this should be an easy update for you.

---

- **Utility Completions:** Access helpful completions while typing.

## Utility Completions

As you type, you'll see suggestions for CSS properties.

![Yumma CSS Play - Utility Completions](yummacss-playground-completions.png)
