---
title: Dimension
banner:
  content: N/A
description: Controls the height and width of an element simultaneously.
slug: docs/dimension
---

<Class category="boxModel" name="dimension" />

This example showcases various dimension utilities:

- The **4rem** dimension utility creates a relatively small size element.
- The **5rem** dimension utility creates a medium size element.
- The **6rem** dimension utility creates a large size element.

```html live "d-16" "d-20" "d-24"
<div class="d-g g-16 gtc-1 sm:gtc-3">
  <div class="ai-c bg-indigo d-f d-16 jc-c rad-1 tc-white">16</div>
  <div class="ai-c bg-indigo d-f d-20 jc-c rad-1 tc-white">20</div>
  <div class="ai-c bg-indigo d-f d-24 jc-c rad-1 tc-white">24</div>
</div>
```

## Max Dimension

Controls the maximum height and max width of an element at once.

<Class category="boxModel" name="max-dimension" />

This example sets the maximum height and width of an element to **16rem**. The **max-d-64** utility ensures the element does not exceed a size of 16rem in both height and width.

<Note icon="size">Try changing the vertical size of the box to see how wide it can be at the maximum size.</Note>

```html live "max-d-64"
<div class="o-auto ai-c bg-indigo d-16 d-f jc-c max-d-64 min-d-32 p-6 r-b rad-1 tc-white">64</div>
```

## Min Dimension

Controls the minimum height and min width of an element at once.

<Class category="boxModel" name="min-dimension" />

This example sets the minimum height and width of an element to **16rem**. The **min-d-64** utility ensures the element does not shrink below a size of 16rem in both height and width.

<Note icon="size">Try changing the vertical size of the box to see how wide it can be at the maximum size.</Note>

```html live "min-d-32"
<div class="o-auto ai-c bg-indigo d-16 d-f jc-c max-d-64 min-d-32 p-6 r-b rad-1 tc-white">32</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<MediaModifier class="d-1" mediaModifier="d-2" classPrefix="d">
  ### Breakpoint modifier
</MediaModifier>

<HoverModifier class="d-1" hoverModifier="d-2" classPrefix="d">
  ### Hover modifier
</HoverModifier>
