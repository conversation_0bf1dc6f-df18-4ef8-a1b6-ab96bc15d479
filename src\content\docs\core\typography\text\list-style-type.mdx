---
title: List Style Type
banner:
  content: N/A
description: Controls the style of a list.
slug: docs/list-style-type
---

<Class category="text" name="list-style-type" />

This example showcases various `list-style-types` utilities:

- The **circle** style marks list items with hollow circles.
- The **disc** style marks list items with solid circles (default for unordered lists).
- The **square** style marks list items with solid squares.

```html live "lst-c" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <ul class="bg-white lsp-o lst-c p-4 pl-6 rad-1">
    <li class="tc-lead">Butter 🧈</li>
    <li class="tc-lead">Egg 🥚</li>
    <li class="tc-lead">Milk 🥛</li>
  </ul>
</div>
```

## Disc

> Initial value

This example sets the list style type to **disc**. The list items will be marked with solid circles, which is the default style for unordered lists.

```html live "lst-d" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <ul class="bg-white lsp-o lst-d p-4 pl-6 rad-1">
    <li class="tc-lead">Butter 🧈</li>
    <li class="tc-lead">Egg 🥚</li>
    <li class="tc-lead">Milk 🥛</li>
  </ul>
</div>
```

## Square

This example sets the list style type to **square**. The list items will be marked with solid squares, providing a different visual style for the list.

```html live "lst-s" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <ul class="bg-white lsp-o lst-s p-4 pl-6 rad-1">
    <li class="tc-lead">Butter 🧈</li>
    <li class="tc-lead">Egg 🥚</li>
    <li class="tc-lead">Milk 🥛</li>
  </ul>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<BreakpointVariant class="lst-d" variant="lst-s" classPrefix="lst">
  ### Breakpoint variant
</BreakpointVariant>

<HoverVariant class="lst-d" variant="lst-s" classPrefix="lst">
  ### Hover variant
</HoverVariant>
