---
title: Text Color
banner:
  content: N/A
description: Controls the text color of an element.
slug: docs/text-color
---

<Class category="color" name="color" />

This example showcases various `color` utilities using the **indigo** colors from the default color palette.

- The **tc-indigo-3** utility applies a lighter indigo shade.
- The **tc-indigo** utility applies the base indigo shade.
- The **tc-indigo-8** utility applies a darker indigo shade.

```html live "tc-indigo-3" "tc-indigo" "tc-indigo-8" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <div class="bg-white d-g p-4 rad-1 rg-4">
    <p class="ta-c tc-indigo-3">Sphinx of black quartz, judge my vow.</p>
    <p class="ta-c tc-indigo">Sphinx of black quartz, judge my vow.</p>
    <p class="ta-c tc-indigo-8">Sphinx of black quartz, judge my vow.</p>
  </div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<HoverVariant class="tc-lead" variant="tc" classPrefix="tc">
  ### Hover variant
</HoverVariant>
