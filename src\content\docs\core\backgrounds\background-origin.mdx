---
title: Background Origin
banner:
  content: N/A
description: Controls background positioning relative to borders, padding, and content.
slug: docs/background-origin
---

<Class category="background" name="background-origin" />

## Border Box

This example sets the background origin to **border-box**. The background positioning area includes the border area.

```html live "bo-bb"
<div class="b-3 b-d bc-bb bo-bb bc-indigo-4 bg-indigo d-20 p-2 rad-1"></div>
```

## Content Box

This example sets the background origin to **content-box**. The background positioning area is confined to the content area, excluding padding and borders.

```html live "bo-cb"
<div class="b-3 b-d bc-cb bo-cb bc-indigo-4 bg-indigo d-20 p-2 rad-1"></div>
```

## Padding Box

> Initial value

This example sets the background origin to **padding-box**. The background positioning area extends to the outer edge of the padding, but does not include the border.

```html live "bo-pb"
<div class="b-3 b-d bc-pb bo-pb bc-indigo-4 bg-indigo d-20 p-2 rad-1"></div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<MediaModifier class="bo-cb" mediaModifier="bo-bb" classPrefix="bo">
  ### Breakpoint modifier
</MediaModifier>

<HoverModifier class="bo-cb" hoverModifier="bo-bb" classPrefix="bo">
  ### Hover modifier
</HoverModifier>
