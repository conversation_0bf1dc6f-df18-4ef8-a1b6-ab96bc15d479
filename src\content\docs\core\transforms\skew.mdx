---
title: Skew
banner:
  content: N/A
description: Controls how an element on the 2D plane is transformed.
slug: docs/skew
---

<Class category="transform" name="skew" />

This example showcases various `skew()` utilities:

- The **t-sk-3** utility skews the element by **3 degrees**.
- The **t-sk-6** utility skews the element by **6 degrees**.
- The **t-sk-12** utility skews the element by **12 degrees**

```html live "t-sk-3" "t-sk-6" "t-sk-12"
<div class="d-g g-16 gtc-1 sm:gtc-3">
  <div class="my-4 p-r">
    <img class="d-18 d-ib rad-1 t-sk-3" src="https://picsum.photos/600?image=360" />
    <img class="o-20 d-18 d-ib l-0 p-a rad-1" src="https://picsum.photos/600?image=360" />
  </div>
  <div class="my-4 p-r">
    <img class="d-18 d-ib rad-1 t-sk-6" src="https://picsum.photos/600?image=360" />
    <img class="o-20 d-18 d-ib l-0 p-a rad-1" src="https://picsum.photos/600?image=360" />
  </div>
  <div class="my-4 p-r">
    <img class="d-18 d-ib rad-1 t-sk-12" src="https://picsum.photos/600?image=360" />
    <img class="o-20 d-18 d-ib l-0 p-a rad-1" src="https://picsum.photos/600?image=360" />
  </div>
</div>
```

## Skew X

This example showcases various skew transformations along the X-axis:

- The **t-skx-3** utility skews the element by **3 degrees** horizontally.
- The **t-skx-6** utility skews the element by **6 degrees** horizontally.
- The **t-skx-12** utility skews the element by **12 degrees** horizontally.

```html live "t-skx-3" "t-skx-6" "t-skx-12"
<div class="d-g g-16 gtc-1 sm:gtc-3">
  <div class="my-4 p-r">
    <img class="d-18 d-ib rad-1 t-skx-3" src="https://picsum.photos/600?image=360" />
    <img class="o-20 d-18 d-ib l-0 p-a rad-1" src="https://picsum.photos/600?image=360" />
  </div>
  <div class="my-4 p-r">
    <img class="d-18 d-ib rad-1 t-skx-6" src="https://picsum.photos/600?image=360" />
    <img class="o-20 d-18 d-ib l-0 p-a rad-1" src="https://picsum.photos/600?image=360" />
  </div>
  <div class="my-4 p-r">
    <img class="d-18 d-ib rad-1 t-skx-12" src="https://picsum.photos/600?image=360" />
    <img class="o-20 d-18 d-ib l-0 p-a rad-1" src="https://picsum.photos/600?image=360" />
  </div>
</div>
```

## Skew Y

This example showcases various skew transformations along the Y-axis:

- The **t-sky-3** utility skews the element by **3 degrees** vertically.
- The **t-sky-6** utility skews the element by **6 degrees** vertically.
- The **t-sky-12** utility skews the element by **12 degrees** vertically.

```html live "t-sky-3" "t-sky-6" "t-sky-12"
<div class="d-g g-16 gtc-1 sm:gtc-3">
  <div class="my-4 p-r">
    <img class="d-18 d-ib rad-1 t-sky-3" src="https://picsum.photos/600?image=360" />
    <img class="o-20 d-18 d-ib l-0 p-a rad-1" src="https://picsum.photos/600?image=360" />
  </div>
  <div class="my-4 p-r">
    <img class="d-18 d-ib rad-1 t-sky-6" src="https://picsum.photos/600?image=360" />
    <img class="o-20 d-18 d-ib l-0 p-a rad-1" src="https://picsum.photos/600?image=360" />
  </div>
  <div class="my-4 p-r">
    <img class="d-18 d-ib rad-1 t-sky-12" src="https://picsum.photos/600?image=360" />
    <img class="o-20 d-18 d-ib l-0 p-a rad-1" src="https://picsum.photos/600?image=360" />
  </div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<BreakpointVariant class="t-sk-10" variant="t-sk-20" classPrefix="t-sk">
  ### Breakpoint variant
</BreakpointVariant>

<HoverVariant class="t-sk-10" variant="t-sk-20" classPrefix="t-sk">
  ### Hover variant
</HoverVariant>
