---
title: Row Gap
banner:
  content: N/A
description: Controls the gap between rows in a grid layout.
slug: docs/row-gap
---

<Class category="grid" name="row-gap" />

This example sets the row gap to **0.75rem**. The element will have a uniform spacing of **0.75rem** between its rows.

```html live "rg-10" layout="/src/layouts/inline.astro"
<div class="rg-10 d-g gtr-3 tc-white" id="area">
  <div class="bg-indigo p-4 rad-1 ta-c">A</div>
  <div class="bg-indigo p-4 rad-1 ta-c">B</div>
  <div class="bg-indigo p-4 rad-1 ta-c">C</div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/foundations/responsive-design/) or other factors, such as [hover states](/docs/foundations/variants/).

<BreakpointVariant class="rg-1" variant="rg-2" classPrefix="rg">
  ### Breakpoint variant
</BreakpointVariant>

<HoverVariant class="rg-1" variant="rg-2" classPrefix="rg">
  ### Hover variant
</HoverVariant>
