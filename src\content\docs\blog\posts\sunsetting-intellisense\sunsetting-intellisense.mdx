---
authors: Renildo
date: 2025-03-18
description: As of March 18, 2025, you won't be able to download the extension from the Visual Studio Code Marketplace.
pagefind: false
slug: blog/sunsetting-intellisense
title: Sunsetting Intellisense
---

As of March 18, 2025, you won't be able to download the extension from the [Visual Studio Code Marketplace](https://marketplace.visualstudio.com/VSCode).

{/* excerpt */}

Originally Yumma CSS Intellisense was created to help developers work faster with Yumma CSS, thanks to the rich [VS Code API](https://code.visualstudio.com/api/references/vscode-api) we could provide features like [intellisense](https://code.visualstudio.com/docs/editor/intellisense) and [hover providers](https://code.visualstudio.com/api/language-extensions/programmatic-language-features).

### Why we’re moving on?

Unfortunately, we've been getting way less attention from Yumma CSS Intellisense over the past 3 months, and it hasn't really helped us solve any problems, not to mention the fact that the project hasn't been maintained for a long time due to lack of innovation and interest.

### So, what's next?

We're working on a new extension called [Yumma CSS Search](https://github.com/yumma-lib/yumma-css-search) for VS Code that'll replace Yumma CSS Intellisense soon.

With [Yumma CSS Search](https://github.com/yumma-lib/yumma-css-search), you will be able to search all of the Yumma CSS utilities right in VS Code.

Even though Yumma CSS Intellisense wasn't a super useful product, we'll still develop VS Code extensions to help you get the most out of Yumma CSS right in [VS Code](https://code.visualstudio.com/) or in any other code editor like [Cursor](https://cursor.com/) or [Windsurf](https://codeium.com/windsurf).

We're putting a lot of time into [Yumma CSS v3](https://yummacss.com/docs/next), and we want it to be a good release for all developers using Yumma CSS.
