---
title: Vue.js with Yumma CSS
banner:
  content: N/A
description: Integrate Yumma CSS into Vue.js applications.
slug: docs/guides/vuejs
---

## Creating a new project

To create a new Vue.js project, you need run the [Vue.js command](https://vuejs.org/guide/quick-start.html#creating-a-vue-application) in your terminal.

<Steps>

    1. **Install Yumma CSS**

        Install `yummacss` using a package manager.

        <PackageManagers pkgManagers={["pnpm", "npm", "yarn"]} pkg="yummacss -D" title="Terminal" />

        <br />

    2. **Initialize configuration**

            Create a configuration file in your project.

            <PackageManagers pkgManagers={["pnpm", "npm", "yarn"]} pkg="yummacss init" title="Terminal" type="dlx" />

    3. **Set up configuration**

        Specify the locations of all your project files in the config file.

        ```js title="yumma.config.js" mark={2-3}
        export default {
            source: ["./src/**/*.vue"],
            output: "./src/assets/base.css",
            buildOptions: {
                reset: true,
                minify: false,
            }
        };
        ```

    4. **Build styles**

        You can now start generating your CSS with the [`build`](/docs/foundations/config/#build) or [`watch`](/docs/foundations/config/#watch) command.

        <PackageManagers pkgManagers={["pnpm", "npm", "yarn"]} pkg="yummacss build" title="Terminal" type="dlx" />

    5. **Done!**

        You're all set to start using Yumma CSS utility classes in your project.

        ```vue title="App.vue" mark={2-4}
        <template>
            <div class="bg-indigo-12 d-g h-dvh pi-c tc-white">
                <h1 class="fs-3xl fw-500">Yumma CSS ⚙️ Vue.js</h1>
            </div>
        </template>
        ```

</Steps>

---

## Clone this project

Skip the guide steps entirely with our Vue.js starter.

```bash title="Cloning the project..."
git clone https://github.com/yumma-lib/with-vuejs.git
```
