---
title: CLI Setup
banner:
  content: N/A
description: Learn how to set up the Yumma CSS CLI.
slug: docs/foundations/cli-setup
---

## Installing the CLI

Yumma CSS comes with the Yumma CLI, a powerful tool that's a breeze to set up and takes no time or effort for production-ready projects.

<PackageManagers pkgManagers={["pnpm", "npm", "yarn"]} pkg="yummacss -D" title="Terminal" />

## Creating your configuration file

To start using the CLI, you need to create a configuration file in your project.

<PackageManagers pkgManagers={["pnpm", "npm", "yarn"]} pkg="yummacss init" title="Terminal" type="dlx" />

This will create a complete  `yumma.config.js` file at the root of your project shown below.

```js title="yumma.config.js"
export default {
  source: [""],
  output: "",
  buildOptions: {
    reset: true,
    minify: false,
  },
};
```

### Configuration options

### Source

A collection of paths for your template files, such as `.js`, `.tsx`, or `.html` files. This is where the CLI will look for utility classes to include in the final CSS file.

<Note icon="info">Use [glob patterns](https://code.visualstudio.com/docs/editor/glob-patterns#_glob-pattern-syntax) to include subfolders and specific file types.</Note>

```js title="yumma.config.js" mark={2}
module.exports = {
  source: ["./src/**/*.html"],
};
```

### Output

A string that specifies the output path for the compiled CSS file. This is where the CLI will save the final CSS file after processing your template files.

```js title="yumma.config.js" mark={2}
module.exports = {
  output: "./src/styles.css",
};
```

### Reset

A boolean that determines whether the inclusion of the [Stylecent reset system](/docs/stylecent).

```js title="yumma.config.js" mark={3}
module.exports = {
  buildOptions: {
    reset: true, // default: true
  },
};
```

### Minify

A boolean that determines whether the compiled CSS files should or not be minified. This is useful for production builds where you want to reduce the file size for faster loading times.

```js title="yumma.config.js" mark={3}
module.exports = {
  buildOptions: {
    minify: true, // default: false
  },
};
```

## File scanning and output

The CLI will scan all the files specified in the `source` array and generate a single CSS file based on the utility classes used in those files. The output file is specified in the `output` field.

<Tabs>
  <TabItem label="Input">
    ```jsx title="index.html"
    <div class="bg-indigo px-4 py-2 rad-1 tc-white"></div>
    ```
  </TabItem>
  <TabItem label="Output">
    ```css title="styles.css"
    *, :before, :after {
        box-sizing: border-box;
        border: 0 solid;
    }

    * {
        margin: 0;
        padding: 0;
    }

    .rad-1 {
        border-radius: .25rem;
    }

    .px-4 {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .py-2 {
        padding-top: .5rem;
        padding-bottom: .5rem;
    }

    .bg-indigo {
        background-color: #595cd9;
    }

    .tc-white {
        color: #fff;
    }

    ```
  </TabItem>
</Tabs>

```js group="example" tab="JavaScript"  
console.log('Hello from JavaScript!')  
```  
  
```python group="example" tab="Python"    
print('Hello from Python!')  
```  
  
```rust group="example" tab="Rust"  
println!("Hello from Rust!");  
```