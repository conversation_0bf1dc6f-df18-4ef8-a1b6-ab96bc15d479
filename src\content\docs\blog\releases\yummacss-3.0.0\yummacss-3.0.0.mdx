---
authors: Renildo
cover:
  alt: Yumma CSS 3.0
  image: cover.png
date: 2025-04-14
description: After almost six months of hard work it's finally here — Yumma CSS v3.0 🎉
pagefind: false
slug: blog/yummacss-3.0
tags: ["release"]
title: Yumma CSS 3.0
---

After almost six months of hard work, we're super excited to share all of the new features, improvements, and fixes that we think you're going to love.

{/* excerpt */}

This is the biggest release of Yumma CSS, and we can't wait for you to try it out!

<iframe
  allowfullscreen
  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
  class="ar-16/9 rad-1 w-full"
  frameborder="0"
  referrerpolicy="strict-origin-when-cross-origin"
  src="https://youtube.com/embed/eqTIFRGqp9o?si=gSU63wW18MpoPjzl"
  title="What's new in Yumma CSS v3.0?"></iframe>

You may also want to take a look at some of the [release notes](https://github.com/yumma-lib/yumma-css/releases/tag/v3.0.0) but, anyway, these are the most noticeable shifts:

- [All-new CLI](#all-new-cli): A new CLI tool to compile SCSS to CSS.
- [New utilities and variants](#new-utilities-and-variants): Over 50 new utility classes.
- [Build performance](#build-performance): Faster build times and smaller file sizes.
- [Upgrading to v3.0](#upgrading-to-v30): Steps to upgrade to Yumma CSS v3.0.
- [Changes in v3.0](#changes-in-v30): Changes to the core framework structure.
- [Dependency changes](#dependency-changes): Changes to the dependency structure.
- [Color utility changes](#color-utility-changes): Changes to the color utility classes.
- [Utility changes](#align-content-utilities): Changes to the utility classes.
- [Stylecent changes](#stylecent-changes): Changes to the Stylecent utility classes.
- [Breakpoint changes](#breakpoint-changes): Changes to the breakpoint classes.
- [Fixed responsive utilities](#fixed-responsive-utilities): Changes to the responsive utility classes.
- [Removed utilities](#removed-container-utility): Removed utility classes.

This is a major update that introduces groundbreaking features. Major releases follow [semantic versioning](https://docs.npmjs.com/about-semantic-versioning) conventions. In other words, you probably need refactoring after upgrading.

---

## What's new in v3.0?

We've completely rewritten the code base for Yumma CSS v3, both internally and externally, and we're excited to improve the way you deal with CSS. We're making some big changes to keep improving Yumma and take it to the next level.

In v3, we're planning to address some major performance issues and add and improve existing utilities in the framework, among other things.

### All-new CLI

Until recently, you had to import a lot of annoying CSS from Yumma CSS, which was a real pain.

With v3, you won't have to stress about shipping unused CSS to the browser. The new CLI will scan and get rid of them for you automatically.

<Steps>

    1. **Install Yumma CSS**

        Add `yummacss` to your project as dev dependency.

        <Note icon="info">Learn more about [dependency changes](#dependency-changes).</Note>

        <PackageManagers pkgManagers={["pnpm", "npm", "yarn"]} pkg="yummacss -D" title="Terminal" />

    2. **Add the configuration file**

        Next, add the `yummacss.config.js` to the root level of your project or run `npx yummacss init` to create it for you.

        <FileTree>
            - node_modules/
            - public
                - favicon.ico
            - src
                - globals.css
                - index.html
            - .gitignore
            - package-lock.json
            - package.json
            - **yummacss.config.js**

        </FileTree>

    3. **Set up the config file**

        To generate styles using the CLI, just set up the `source` array with the path to your template files and set the `output` string with the path to the CSS file you want to generate.

        ```js title="yummacss.config.js" mark={2-3}
        module.exports = {
          source: ["./src/**/*.html"],
          output: "./src/globals.css",
          buildOptions: {
            reset: true,
            minify: false,
          },
        };
        ```

    4. **Write CSS**

        Start using Yumma CSS utilities in your to generate CSS with the CLI.

        ```html mark={7, 11-16} title="index.html"
        <!DOCTYPE html>
        <html lang="en">

        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <link rel="stylesheet" href="/src/globals.css" />
        </head>

        <body>
          <div class="b-1 bc-silver-2 bg-white rad-2 bs-sm p-4">
              <h1 class="fw-600 tc-indigo fs-lg">Hello 👋, name's Renildo.</h1>
              <p class="tc-gray-7">I'm the Founder / CEO of Yumma CSS 💛</p>

              <button class="bg-indigo fs-sm h:bg-indigo-7 mt-6 px-4 py-1 rad-1 tc-white">GitHub</button>
          </div>
        </body>

        </html>
        ```

    5. **Compile the SCSS**

        To compile the source into CSS, ou can run the following command:

        <PackageManagers pkgManagers={["pnpm", "npm", "yarn"]} type="exec" pkg="yummacss" args="build" title="Terminal" />

        When you run the `build` command the CLI will create a new CSS file and scan project paths based on the `yummacss.config.js` to purge of any unused styles.

        ```css title="globals.css"
        .rad-1 {
          border-radius: .25rem;
        }

        .rad-2 {
          border-radius: .5rem;
        }

        .b-1 {
          border-width: 1px;
        }

        .mt-6 {
          margin-top: 1.5rem;
        }

        .p-4 {
          padding: 1rem;
        }

        .px-4 {
          padding-left: 1rem;
          padding-right: 1rem;
        }

        .py-1 {
          padding-bottom: .25rem;
          padding-top: .25rem;
        }

        /* etc */
        ```

</Steps>

### New utilities and variants

To make the Yumma CSS framework as complete as possible, we're adding support for over 50 utility classes to the core of the framework.

| Category      | Properties                                                                                                                                                                                                                                                                                                                     |
| ------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| Backgrounds   | [Background Attachment](/docs/background-attachment), [Background Clip](/docs/background-clip), [Background Origin](/docs/background-origin), [Background Position](/docs/background-position), [Background Repeat](/docs/background-repeat), [Background Size](/docs/background-size)                                         |
| Borders       | [Border Spacing](/docs/border-spacing), [Border Bottom/Left/Right/Top Radius](/docs/border-radius)                                                                                                                                                                                                                             |
| Box Model     | [Margin LTR/RTL](/docs/margin#margin-block-end), [Padding LTR/RTL](/docs/padding#padding-block-end)                                                                                                                                                                                                                            |
| Effect        | [Blur](/docs/blur), [Grayscale](/docs/grayscale)                                                                                                                                                                                                                                                                               |
| Flexbox       | [Order](/docs/order), [Place Content](/docs/place-content), [Place Items](/docs/place-items), [Place Self](/docs/place-self)                                                                                                                                                                                                   |
| Interactivity | [Scroll Behavior](/docs/scroll-behavior), [Scroll Margin](/docs/scroll-margin) [Scroll Padding](/docs/scroll-padding) [Scroll Snap Align](/docs/scroll-snap-align), [Scroll Snap Stop](/docs/scroll-snap-stop), [Scroll Snap Type](/docs/scroll-snap-type) [Field Sizing](/docs/field-sizing)                                  |
| Positioning   | [Clear](/docs/clear), [Bottom/Left/Right/Top (Axis)](/docs/bottom-left-right-top), [Isolation](/docs/isolation), [Visibility](/docs/visibility)                                                                                                                                                                                |
| SVG           | [Fill](/docs/fill), [Stroke](/docs/stroke), [Stroke Width](/docs/stroke-width)                                                                                                                                                                                                                                                 |
| Text          | [Letter Spacing](/docs/letter-spacing), [List Style Position](/docs/list-style-position), [Text Indent](/docs/text-indent), [Text Overflow](/docs/text-overflow), [Text Transform](/docs/text-transform), [Text Underline Offset](/docs/text-underline-offset), [Text Wrap](/docs/text-wrap), [White Space](/docs/white-space) |
| Transforms    | [Rotate](/docs/rotate), [Scale](/docs/scale), [Skew](/docs/skew), [Transform Origin](/docs/transform-origin)                                                                                                                                                                                                                   |

### Build performance

We completely overhauled the entire codebase to get better performance in build times and overall file size. We changing the way utilities and modifiers are generated, to eliminate any potential for duplicated or unnecessary data in the `/dist` folder.

| Metric               | v2.1    | v3.0    | Improvement            |
| -------------------- | ------- | ------- | ---------------------- |
| Complete build       | 13.88 s | 3.96 s  | -9.92 s (71% faster)   |
| File size (standard) | 3.21 MB | 2.53 MB | -0.68 MB (21% smaller) |
| File size (minified) | 2.48 MB | 1.89 MB | -0.59 MB (24% smaller) |
| Utilities coverage   | 111     | 167     | +56                    |

### Upgrading to v3.0

If you're using Yumma CSS v2 or older, the process of upgrading to v3 using the Yumma CLI is honestly super easy.

<Steps>

1.  **Remove `@import` rules**

    The CLI works by compiling SCSS to CSS, so there's no need to import the Yumma CSS package dependency.

    ```diff lang="css" title="globals.css"
    - @import "/node_modules/yummacss/dist/yumma.min.css";
    ```

2.  **Add the Yumma config file**

    Create a `yummacss.config.js` file at the project root.

    <FileTree>
    - node_modules/
    - public
      - favicon.ico
    - src
      - globals.css
      - index.html
    - .gitignore
    - package-lock.json
    - package.json
    - **yummacss.config.js**

    </FileTree>

3.  **Set up the config file**

    Setup the `source` array and `output` string field.

    ```js title="yummacss.config.js" mark={2-7}
    module.exports = {
      source: ["./src/**/*.html"],
      output: "./src/globals.css",
      buildOptions: {
        reset: true,
        minify: true,
      },
    };
    ```

4.  **Compile the SCSS**

    Run `npx yummacss build` in your terminal to compile to CSS.

    <PackageManagers pkgManagers={["pnpm", "npm", "yarn"]} type="exec" pkg="yummacss" args="build" title="Terminal" />

</Steps>

---

## Changes in v3.0

Like some of the past major updates, Yumma CSS v3 is changing its core framework structure. Just so you know, there are going to be a few breaking changes in the release, so it might be worth taking a look before you update.

### Dependency changes

With Yumma CSS v3 you no longer need to install `yummacss` as a standard dependency. Instead, you can install it as a dev dependency. This is because Yumma CSS is now a CLI tool that generates CSS files based on your configuration.

<PackageManagers pkgManagers={["pnpm", "npm", "yarn"]} pkg="yummacss -D" title="Terminal" />

The full set of utilities will be retained within the distribution folder for the purpose of facilitating the importation of the entire Yumma utilities suite, should this be required.

<FileTree>

    - dist
    	- yumma.css
    	- yumma.min.css

</FileTree>

### Color utility changes

In v3, both the light (`l-`) and dark (`d-`) characters are being removed across all color utilities. As a result, the range used to determine a color's shade was also adjusted.

{/* prettier-ignore */}
<div class="p-4">
  ```diff lang="html" title="index.html" 
  - <button class="bg-l-indigo-6 h:bg-d-indigo-1">Hello</button>
  + <button class="bg-indigo-1 h:bg-indigo-7">Hello</button>
  ```
</div>

See what the `bg-*` background utility looks like compared to v2.1

| v3.0           | v2.1            | Length Difference |
| -------------- | --------------- | ----------------- |
| `bg-indigo-1`  | `bg-l-indigo-6` | -2 characters     |
| `bg-indigo-2`  | `bg-l-indigo-5` | -2 characters     |
| `bg-indigo-3`  | `bg-l-indigo-4` | -2 characters     |
| `bg-indigo-4`  | `bg-l-indigo-3` | -2 characters     |
| `bg-indigo-5`  | `bg-l-indigo-2` | -2 characters     |
| `bg-indigo-6`  | `bg-l-indigo-1` | -2 characters     |
| `bg-indigo`    | `bg-indigo`     | -0 characters     |
| `bg-indigo-7`  | `bg-d-indigo-1` | -2 character      |
| `bg-indigo-8`  | `bg-d-indigo-2` | -2 character      |
| `bg-indigo-9`  | `bg-d-indigo-3` | -2 character      |
| `bg-indigo-10` | `bg-d-indigo-4` | -1 character      |
| `bg-indigo-11` | `bg-d-indigo-5` | -1 character      |
| `bg-indigo-12` | `bg-d-indigo-6` | -1 character      |

Also, the color hue is increasing from `10%` shade modification to `14%`. This means that light colors will become lighter, and dark colors will become darker.

<Tabs>
  <TabItem label="v3.0">
    <Palette
      data={[
        { name: "Red", color: "rgb(215, 61, 61)" },
        { name: "Orange", color: "rgb(224, 104, 20)" },
        { name: "Yellow", color: "rgb(211, 161, 7)" },
        { name: "Green", color: "rgb(31, 177, 85)" },
        { name: "Teal", color: "rgb(18, 166, 149)" },
        { name: "Cyan", color: "rgb(5, 164, 191)" },
        { name: "Blue", color: "rgb(53, 117, 221)" },
        { name: "Indigo", color: "rgb(89, 92, 217)" },
        { name: "Violet", color: "rgb(125, 83, 221)" },
        { name: "Pink", color: "rgb(212, 65, 138)" },
        { name: "Lead", color: "rgb(63, 63, 78)" },
        { name: "Gray", color: "rgb(96, 103, 115)" },
        { name: "Silver", color: "rgb(191, 194, 199)" },
      ]}
    />
  </TabItem>
  <TabItem label="v2.1">
    <LegacyPalette
      data={[
        { name: "Red", color: "#d73d3d" },
        { name: "Orange", color: "#e06814" },
        { name: "Yellow", color: "#d3a107" },
        { name: "Green", color: "#1fb155" },
        { name: "Teal", color: "#12a695" },
        { name: "Cyan", color: "#05a4bf" },
        { name: "Blue", color: "#3575dd" },
        { name: "Indigo", color: "#595cd9" },
        { name: "Violet", color: "#7d53dd" },
        { name: "Pink", color: "#d4418a" },
        { name: "Lead", color: "#3f3f4e" },
        { name: "Gray", color: "#606773" },
        { name: "Silver", color: "#bfc2c7" },
      ]}
    />
  </TabItem>
</Tabs>

### Stylecent changes

We're making some changes to Stylecent in v3 to make it more modern and consistent. These changes are turned on by default, but you can turn them off using the [`yummacss.config.js`](#disabling-stylecent) file.

By default, all paddings will be removed.

```scss mark={3} title="base/stylecent.scss"
* {
  margin: 0;
  padding: 0;
}
```

Font rendering is smoother, and a consistent system font is set as the default. — [oshwcomeau.com](https://joshwcomeau.com/css/custom-css-reset/)

```diff lang=scss mark={2-3} title="base/stylecent.scss"
body {
  -webkit-font-smoothing: antialiased;
  font-family: vars.$yma-font-system;
-  line-height: 1.5;
}
```

Form elements now include padding by default. Borders are added for form elements without class attributes.

```scss mark={6-8,16} title="base/stylecent.scss"
button,
input,
optgroup,
select,
textarea {
  background-color: vars.$yma-color-transparent;
  font-family: inherit;
  padding: 0.5rem;
}

button:not([class]),
input:not([class]),
optgroup:not([class]),
select:not([class]),
textarea:not([class]) {
  border: 1px solid vars.$yma-color-silver;
}
```

Interactive elements have clear outlines for accessibility.

```scss mark={7-9} title="base/stylecent.scss"
button,
input,
textarea,
select,
a,
summary {
  &:focus {
    outline: 2px solid vars.$yma-color-transparent;
  }
}
```

In the absence of content, textareas will exhibit a default height. — [piccalil.li](https://piccalil.li/blog/a-more-modern-css-reset/)

```scss mark={2} title="base/stylecent.scss"
textarea:not([rows]) {
  min-height: 10em;
}
```

Disabled elements are visually distinct with reduced opacity and a "not-allowed" cursor.

```scss mark={5-6} title="base/stylecent.scss"
button:disabled,
input:disabled,
select:disabled,
textarea:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}
```

Headings adopt balanced text wrapping, consistent font sizes, and bold weights. — [oshwcomeau.com](https://joshwcomeau.com/css/custom-css-reset/)

```scss mark={7-9,13} title="base/stylecent.scss"
h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: 1rem;
  font-weight: 600;
  text-wrap: balance;
}

p {
  text-wrap: pretty;
}
```

Small text and code elements are consistently scaled and inherited. Code elements will have consistent font family. — [modern-normalize](https://github.com/sindresorhus/modern-normalize/blob/main/modern-normalize.css#L64)

```scss mark={3,7-8,15-16} title="base/stylecent.scss"
b,
strong {
  font-weight: 700;
}

small {
  font-size: 80%;
  line-height: 1.4;
}

pre,
code,
kbd,
samp {
  font-family: monospace, monospace;
  font-size: 1em;
}
```

Reset default link styles.

```scss mark={2,7} title="base/stylecent.scss"
a {
  color: inherit;
  text-decoration: none;
}
```

Table headers are bold and sized consistently.

```scss mark={2-3} title="base/stylecent.scss"
th {
  font-size: 1rem;
  font-weight: 600;
}
```

Horizontal rules, details, and summaries are updated for better spacing and display. — [modern-normalize](https://github.com/sindresorhus/modern-normalize/blob/main/modern-normalize.css#L64)

```scss mark={3,8,12} title="base/stylecent.scss"
hr {
  border-top: 1px solid vars.$yma-color-silver;
  height: 0;
  margin: 1em 0;
}

details {
  display: block;
}

summary {
  display: list-item;
}
```

#### Disabling Stylecent

From now on, files like `yumma-core.css` and `yumma-core.min.css` will be deleted from the `/dist` folder in favor of the `yummacss.config.js` config file.

<Tabs>
  <TabItem label="v3.0">
    ```js mark={4-7} title="yummacss.config.js"
    module.exports = {
      source: ["./src/**/*.html"],
      output: "./src/globals.css",
      buildOptions: {
        reset: false, // Disable reset styles
        minify: false, // Disable styles minification
      },
    };
    ```

  </TabItem>
  <TabItem label="v2.1">

    ```css title="globals.css"
    /* Disable base styles */
    @import "https://cdn.jsdelivr.net/gh/yumma-lib/yumma-css@latest/dist/yumma-core.css";

    /* Disable base styles and minify CSS */
    @import "https://cdn.jsdelivr.net/gh/yumma-lib/yumma-css@latest/dist/yumma-core.min.css";
    ```

  </TabItem>
</Tabs>

### Breakpoint changes

In v3, we are moving from pixel-based units to rem-based units. This change promotes better accessibility and scalability, as rem units respect the user's browser font size settings.

| Breakpoint | v2.1   | v3.0             |
| ---------- | ------ | ---------------- |
| `sm`       | 640px  | 40rem _(640px)_  |
| `md`       | 768px  | 48rem _(768px)_  |
| `lg`       | 1024px | 64rem _(1024px)_ |
| `xl`       | 1280px | 80rem _(1280px)_ |
| `xxl`      | 1536px | 96rem _(1536px)_ |

### Fixed responsive utilities

We're finally rolling out fixes for all the unexpected behaviors that popped up when using responsive utilities in Yumma CSS v3.

We've grouped related responsive utilities together so they can override existing ones in the DOM like they were supposed to.

This improvement also eliminates redundant CSS and makes the framework more predictable and easier to develop with.

### Align Content utilities

In v3, we're changing the `align-content` utility prefix from `ac-s` to `ac-st` to match with other utilities like `ji-st` and `js-st`.

```diff lang="html" title="index.html"
- <div class="ac-s ..."></div>
+ <div class="ac-st ..."></div>
```

### Align Items utilities

Additionally, in v3, we're changing changing the `align-items` utility prefix from `ai-s` to `ai-st`.

```diff lang="html" title="index.html"
- <div class="ai-s ..."></div>
+ <div class="ai-st ..."></div>
```

### Align Self utilities

Additionally, in v3, we're changing `align-self` utility prefix from `as-s` to `as-st` to match with other utilities like `ji-st` and `js-st`.

```diff lang="html" title="index.html"
- <div class="as-s ..."></div>
+ <div class="as-st ..."></div>
```

### Justify Content utilities

Finally, in v3, we're changing changing the `justify-content` utility prefix from `jc-s` to `jc-st` to match with other utilities like `ji-st` and `js-st`.

```diff lang="html" title="index.html"
- <div class="jc-s ..."></div>
+ <div class="jc-st ..."></div>
```

### Border Bottom Radius utilities

In v3, we're adding new `border-radius` utilities starting with `rad-b-*`

```diff lang="html" "rad-b-2" title="index.html"
- <button class="b-1 bg-black rad-bl-2 rad-br-2 tc-white">Subscribe</button>
+ <button class="b-1 bg-black rad-b-2 tc-white">Subscribe</button>
```

### Border Left Radius utilities

Additionally, in v3, we're also adding `rad-l-*` to add left rounded edges only to an element.

```diff lang="html" "rad-l-2" title="index.html"
- <button class="b-1 bg-black rad-bl-2 rad-tl-2 tc-white">Subscribe</button>
+ <button class="b-1 bg-black rad-l-2 tc-white">Subscribe</button>
```

### Border Right Radius utilities

Additionally, in v3, we're also adding `rad-r-*` to add right rounded edges only to an element.

```diff lang="html" "rad-r-2" title="index.html"
- <button class="b-1 bg-black rad-br-2 rad-tr-2 tc-white">Subscribe</button>
+ <button class="b-1 bg-black rad-r-2 tc-white">Subscribe</button>
```

### Border Top Radius utilities

Finally, in v3, we're also adding `rad-t-*` to add top rounded edges only to an element.

```diff lang="html" "rad-t-2" title="index.html"
- <button class="b-1 bg-black rad-tl-2 rad-tr-2 tc-white">Subscribe</button>
+ <button class="b-1 bg-black rad-t-2 tc-white">Subscribe</button>
```

### Bottom/ Inset / Left / Right / Top utilities

In v3, we've also made changes to the way the Bottom / Left / Right / Top utilities are written. This includes changes to the Bottom, Left, Right, and Top variants.

{/* prettier-ignore */}
<Tabs>
  <TabItem label="Bottom">
    ```diff lang="html" title="index.html"
    - <div class="dir-b-* ..."></div>
    + <div class="bo-* ..."></div>
    ```
  </TabItem>
  <TabItem label="Inset">
    ```diff lang="html" title="index.html"
    - <div class="dir-i-* ..."></div>
    + <div class="i-* ..."></div>
    ```
  </TabItem>
  <TabItem label="Left">
    ```diff lang="html" title="index.html"
    - <div class="dir-l-* ..."></div>
    + <div class="l-* ..."></div>
    ```
  </TabItem>
  <TabItem label="Right">
    ```diff lang="html" title="index.html"
    - <div class="dir-r-* ..."></div>
    + <div class="r-* ..."></div>
    ```
  </TabItem>
  <TabItem label="Top">
    ```diff lang="html" title="index.html"
    - <div class="dir-t-* ..."></div>
    + <div class="t-* ..."></div>
    ```
  </TabItem>
</Tabs>

### Columns utilities

In v3, we're making the `columns` utilities even smaller than before.

```diff lang="html" title="index.html"
- <div class="cols-* ..."></div>
+ <div class="c-* ..."></div>
```

### Dimension utilities

In v3, we've made the Dimension utility even more abbreviated to align with the purpose of the framework.

```diff lang="html" title="index.html"
- <div class="dim-* ..."></div>
+ <div class="d-* ..."></div>
```

### Max Dimension utilities

Additionally, in v3, we're changing the Max Dimension utility syntax.

This also applies to the Max and Min Dimension utilities.

```diff lang="html" title="index.html"
- <div class="max-dim-* ..."></div>
+ <div class="max-d-* ..."></div>
```

### Min Dimension utilities

Finally, in v3, we're changing the Min Dimension utility syntax.

```diff lang="html" title="index.html"
- <div class="min-dim-* ..."></div>
+ <div class="min-d-* ..."></div>
```

### Font Size utilities

In v3, new `font-size` utilities are being incorporated, and the visual sequential incremental between values is being enhanced to rectify the previous exaggerated outputs.

<Tabs>
  <TabItem label="v3.0">
    <Class category="font" name="font-size" />
  </TabItem>
  <TabItem label="v2.1">
    <Class category="font" name="font-size" />
  </TabItem>
</Tabs>

### Font Family utilities

In v3, new font stacks are being implemented, existing utilities are being refined, and additional fallback options are being incorporated for every `font-family` utility class. — https://modernfontstacks.com/

<Tabs>
  <TabItem label="v3.0">
    <Class category="font" name="font-family" />
  </TabItem>
  <TabItem label="v2.1">
    <LegacyClass
      data={[
        {
          className: "ff-c",
          properties: ["font-family: Charter, Cambria, serif;"],
        },
        {
          className: "ff-m",
          properties: ["font-family: ui-monospace, Consolas, monospace;"],
        },
        {
          className: "ff-s",
          properties: ["font-family: system-ui, sans-serif;"],
        },
      ]}
    />
  </TabItem>
</Tabs>

### Overflow utilities

In v3, we've completely reworked the `overflow` utility class syntax.

```diff lang="html" title="index.html"
- <div class="ovf-* ..."></div>
+ <div class="o-* ..."></div>
```

### Overflow X utilities

Additionally, in v3, we've shortened the `overflow-x` utility class.

```diff lang="html" title="index.html"
- <div class="ovf-x-* ..."></div>
+ <div class="o-x-* ..."></div>
```

### Overflow Y utilities

Finally, in v3, we've shortened the `overflow-y` utility class.

```diff lang="html" title="index.html"
- <div class="ovf-y-* ..."></div>
+ <div class="o-y-*..."></div>
```

### Float utilities

In v3 we're reworking the float utilities by shortening the syntax.

```diff lang="html" title="index.html"
- <div class="flo-* ..."></div>
+ <div class="fl-* ..."></div>
```

### Dimension, Height and Width utilities

In v3, we're renaming all `*-1/1` utilities, including the **Height**, **Width**, and **Dimension** utilities to `*-dvh`.

```diff lang="html" title="index.html"
- <div class="*-1/1 ...">A</div>
```

### Removed Container utility

We are no longer supporting the `cnt` utility. We are making improvements to our modifiers to make them more flexible and easier to use.

```html del="cnt" title="index.html"
<div class="cnt"></div>
```

### Removed Insert utility

It's been a great ride, and we're sure most of you have used it to easily center a div in the middle of the screen, but this concept was not very useful in many case scenarios where you would typically need more customizability, so we've decided to remove it.

```diff lang="html" title="index.html"
- <div class="ins ..."></div>
+ <div class="ai-c d-f jc-c ..."></div>
```

### Removed Spacing X utilities

In v3 we've removed the **Spacing X** utilities. You can use the **Row Gap** utilities instead for almost all cases, and even combine the **Row Gap** utilities with other utilities like `d-f` or `d-if`.

```html live "cg-8" title="index.html"
<div class="d-if fd-r cg-8 tc-white" id="area">
  <div class="ai-c bg-indigo d-f d-16 jc-c rad-1">A</div>
  <div class="ai-c bg-indigo d-f d-16 jc-c rad-1">B</div>
  <div class="ai-c bg-indigo d-f d-16 jc-c rad-1">C</div>
</div>
```

### Removed Spacing Y utilities

The same concept applies to the **Spacing Y** utilities. You should now use the **Column Gap** utilities instead. You can even combine the **Column Gap** utilities with other utilities like `d-f` or `d-if`.

```html live "rg-8" title="index.html"
<div class="d-if fd-c rg-8 tc-white" id="area">
  <div class="ai-c bg-indigo d-f d-16 jc-c rad-1">A</div>
  <div class="ai-c bg-indigo d-f d-16 jc-c rad-1">B</div>
  <div class="ai-c bg-indigo d-f d-16 jc-c rad-1">C</div>
</div>
```

### Removed `*-1/2` utilities

In v3, we're removing `h-1/2` from **Height** utilities, `w-1/2` from **Width** utilities, and `d-1/2` from **Dimension** utilities.

```diff lang="html" title="index.html"
- <div class="h-1/2 ...">A</div>
- <div class="w-1/2 ...">A</div>
- <div class="d-1/2 ...">A</div>
```

---

## Upgrade

You can upgrade your projects by getting the latest version of `yummacss` from npm:

<PackageManagers pkgManagers={["pnpm", "npm", "yarn"]} pkg="yummacss@latest" title="Terminal" />
