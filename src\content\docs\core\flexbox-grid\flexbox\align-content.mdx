---
title: Align Content
banner:
  content: N/A
description: Controls the positioning of rows in multi-row Flex and Grid containers.
slug: docs/align-content
---

<Class category="flexbox" name="align-content" />

## Baseline

This example sets the align content to **baseline**. The items will be aligned along the baseline of the container.

```html live "ac-b" layout="/src/layouts/inline.astro"
<div class="ac-b d-g g-4 gtc-3 h-64 tc-white ta-c w-full" id="area">
  <div class="bg-indigo p-4 rad-1">A</div>
  <div class="bg-indigo p-4 rad-1">B</div>
  <div class="bg-indigo p-4 rad-1">C</div>
  <div class="bg-indigo p-4 rad-1">D</div>
  <div class="bg-indigo p-4 rad-1">E</div>
</div>
```

## Center

This example sets the align content to **center**. The items will be centered within the container.

```html live "ac-c" layout="/src/layouts/inline.astro"
<div class="ac-c d-g g-4 gtc-3 h-64 tc-white ta-c w-full" id="area">
  <div class="bg-indigo p-4 rad-1">A</div>
  <div class="bg-indigo p-4 rad-1">B</div>
  <div class="bg-indigo p-4 rad-1">C</div>
  <div class="bg-indigo p-4 rad-1">D</div>
  <div class="bg-indigo p-4 rad-1">E</div>
</div>
```

## Flex End

This example sets the align content to **flex-end**. The items will be aligned to the end of the container.

```html live "ac-fe" layout="/src/layouts/inline.astro"
<div class="ac-fe d-g g-4 gtc-3 h-64 tc-white ta-c w-full" id="area">
  <div class="bg-indigo p-4 rad-1">A</div>
  <div class="bg-indigo p-4 rad-1">B</div>
  <div class="bg-indigo p-4 rad-1">C</div>
  <div class="bg-indigo p-4 rad-1">D</div>
  <div class="bg-indigo p-4 rad-1">E</div>
</div>
```

## Flex Start

This example sets the align content to **flex-start**. The items will be aligned to the start of the container.

```html live "ac-fs" layout="/src/layouts/inline.astro"
<div class="ac-fs d-g g-4 gtc-3 h-64 tc-white ta-c w-full" id="area">
  <div class="bg-indigo p-4 rad-1">A</div>
  <div class="bg-indigo p-4 rad-1">B</div>
  <div class="bg-indigo p-4 rad-1">C</div>
  <div class="bg-indigo p-4 rad-1">D</div>
  <div class="bg-indigo p-4 rad-1">E</div>
</div>
```

## Normal

> Initial value

This example sets the align content to **normal**. The items will be aligned according to the default alignment of the container.

```html live "ac-n" layout="/src/layouts/inline.astro"
<div class="ac-n d-g g-4 gtc-3 h-64 tc-white w-full" id="area">
  <div class="ai-c bg-indigo d-f jc-c p-4 rad-1">A</div>
  <div class="ai-c bg-indigo d-f jc-c p-4 rad-1">B</div>
  <div class="ai-c bg-indigo d-f jc-c p-4 rad-1">C</div>
  <div class="ai-c bg-indigo d-f jc-c p-4 rad-1">D</div>
  <div class="ai-c bg-indigo d-f jc-c p-4 rad-1">E</div>
</div>
```

## Stretch

This example sets the align content to **stretch**. The items will stretch to fill the container along the cross axis.

```html live "ac-s" layout="/src/layouts/inline.astro"
<div class="ac-s d-g g-4 gtc-3 h-64 tc-white w-full" id="area">
  <div class="ai-c bg-indigo d-f jc-c p-4 rad-1">A</div>
  <div class="ai-c bg-indigo d-f jc-c p-4 rad-1">B</div>
  <div class="ai-c bg-indigo d-f jc-c p-4 rad-1">C</div>
  <div class="ai-c bg-indigo d-f jc-c p-4 rad-1">D</div>
  <div class="ai-c bg-indigo d-f jc-c p-4 rad-1">E</div>
</div>
```

## Space Around

This example sets the align content to **space-around**. The items will be evenly distributed in the container with space around them.

```html live "ac-sa" layout="/src/layouts/inline.astro"
<div class="ac-sa d-g g-4 gtc-3 h-64 tc-white ta-c w-full" id="area">
  <div class="bg-indigo p-4 rad-1">A</div>
  <div class="bg-indigo p-4 rad-1">B</div>
  <div class="bg-indigo p-4 rad-1">C</div>
  <div class="bg-indigo p-4 rad-1">D</div>
  <div class="bg-indigo p-4 rad-1">E</div>
</div>
```

## Space Between

This example sets the align content to **space-between**. The items will be evenly distributed in the container with the first item at the start and the last item at the end.

```html live "ac-sb" layout="/src/layouts/inline.astro"
<div class="ac-sb d-g g-4 gtc-3 h-64 tc-white ta-c w-full" id="area">
  <div class="bg-indigo p-4 rad-1">A</div>
  <div class="bg-indigo p-4 rad-1">B</div>
  <div class="bg-indigo p-4 rad-1">C</div>
  <div class="bg-indigo p-4 rad-1">D</div>
  <div class="bg-indigo p-4 rad-1">E</div>
</div>
```

## Space Evenly

This example sets the align content to **space-evenly**. The items will be evenly distributed in the container with equal space around them.

```html live "ac-se" layout="/src/layouts/inline.astro"
<div class="ac-se d-g g-4 gtc-3 h-64 tc-white ta-c w-full" id="area">
  <div class="bg-indigo p-4 rad-1">A</div>
  <div class="bg-indigo p-4 rad-1">B</div>
  <div class="bg-indigo p-4 rad-1">C</div>
  <div class="bg-indigo p-4 rad-1">D</div>
  <div class="bg-indigo p-4 rad-1">E</div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<BreakpointVariant class="ac-s" variant="ac-e" classPrefix="ac">
  ### Breakpoint variant
</BreakpointVariant>

<HoverVariant class="ac-s" variant="ac-e" classPrefix="ac">
  ### Hover variant
</HoverVariant>
