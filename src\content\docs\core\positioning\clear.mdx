---
title: Clear
banner:
  content: N/A
description: Controls moving elements under floating elements.
slug: docs/clear
---

<Class category="positioning" name="clear" />

## Both

This example sets the clear property to **both**. The **cl-b** utility ensures that the element is moved below any floating elements on both sides.

```html live "cl-b" layout="/src/layouts/inline.astro"
<div class="bg-indigo d-32 fl-l rad-1"></div>
<div class="bg-indigo d-32 fl-r rad-1"></div>
<div class="cl-b p-6" id="area"></div>
```

## Inline End

This example sets the clear property to **inline-end**. The **cl-ie** utility ensures that the element is moved below any floating elements on the inline end side, which is typically the right side in left-to-right layouts.

```html live "cl-ie" layout="/src/layouts/inline.astro"
<div class="bg-indigo d-32 fl-r rad-1"></div>
<div class="cl-ie p-6" id="area"></div>
```

## Inline Start

This example sets the clear property to **inline-start**. The **cl-is** utility ensures that the element is moved below any floating elements on the inline start side, which is typically the left side in left-to-right layouts.

```html live "cl-is" layout="/src/layouts/inline.astro"
<div class="bg-indigo d-32 fl-l rad-1"></div>
<div class="cl-is p-6" id="area"></div>
```

## Left

This example sets the clear property to **left**. The **cl-l** utility ensures that the element is moved below any floating elements on the left side.

```html live "cl-l" layout="/src/layouts/inline.astro"
<div class="bg-indigo d-32 fl-l rad-1"></div>
<div class="cl-l p-6" id="area"></div>
```

## None

> Initial value

This example sets the clear property to **none**. The **cl-none** utility allows the element to be positioned normally without clearing any floating elements.

```html live "cl-none" layout="/src/layouts/inline.astro"
<div class="bg-indigo d-32 fl-l rad-1"></div>
<div class="cl-none p-6" id="area"></div>
```

## Right

This example sets the clear property to **right**. The **cl-r** utility ensures that the element is moved below any floating elements on the right side.

```html live "cl-r" layout="/src/layouts/inline.astro"
<div class="bg-indigo d-32 fl-r rad-1"></div>
<div class="cl-r p-6" id="area"></div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<MediaModifier class="cl-l" mediaModifier="cl-r" classPrefix="cl">
  ### Media modifier
</MediaModifier>

<HoverModifier class="cl-l" hoverModifier="cl-r" classPrefix="cl">
  ### Hover modifier
</HoverModifier>
