---
title: Box Sizing
banner:
  content: N/A
description: Controls the calculation of the total size of an element.
slug: docs/box-sizing
---

<Class category="boxModel" name="box-sizing" />

## Border Box

This example sets the box sizing to **border-box**. The element's width and height include padding and borders.

<Note icon="heart">Stylecent uses the `border-box` CSS property by default. Learn more about it in the [docs](/docs/stylecent).</Note>

```html live "bs-bb"
<div class="d-26 bs-bb p-8" id="area">
  <div class="ai-c bg-indigo d-f d-full jc-c tc-white">Box</div>
</div>
```

## Content Box

> Initial value

This example sets the box sizing to **content-box**. The element's width and height do not include padding and borders.

```html live "bs-cb"
<div class="d-26 bs-cb p-8" id="area">
  <div class="ai-c bg-indigo d-f d-full jc-c tc-white">Content</div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/foundations/responsive-design/) or other factors, such as [hover states](/docs/foundations/variants/).

<BreakpointVariant class="bs-cb" variant="bs-bb" classPrefix="bs">
  ### Breakpoint variant
</BreakpointVariant>

<HoverVariant class="bs-cb" variant="bs-bb" classPrefix="bs">
  ### Hover variant
</HoverVariant>
