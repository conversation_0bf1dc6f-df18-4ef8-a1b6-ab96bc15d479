{"name": "yumma-css-docs", "version": "0.0.0", "description": "A CSS framework for the web with abbreviated styles.", "homepage": "https://yummacss.com", "license": "MIT", "author": "<PERSON><PERSON><PERSON>", "private": true, "type": "module", "scripts": {"dev": "concurrently \"astro dev\" \"yummacss watch\"", "start": "astro dev", "build": "astro build && yummacss build", "preview": "astro preview", "astro": "astro"}, "dependencies": {"@astrojs/check": "^0.9.4", "@astrojs/sitemap": "3.4.1", "@astrojs/starlight": "^0.35.1", "@astrojs/vercel": "^8.2.2", "@expressive-code/plugin-collapsible-sections": "^0.41.2", "@expressive-code/plugin-line-numbers": "^0.41.2", "@fontsource-variable/outfit": "^5.2.5", "@yummacss/api": "^1.1.1", "astro": "^5.12.0", "astro-auto-import": "^0.4.4", "astro-live-code": "^0.0.6", "gsap": "^3.13.0", "sharp": "^0.34.2", "starlight-blog": "^0.23.2", "starlight-cooler-credit": "^0.4.0", "starlight-links-validator": "^0.16.0", "starlight-package-managers": "^0.11.0", "starlight-showcases": "^0.3.0", "starlight-sidebar-topics": "^0.6.0", "tinycolor2": "^1.6.0"}, "devDependencies": {"@types/js-beautify": "^1.14.3", "@types/tinycolor2": "^1.4.6", "concurrently": "^9.1.2", "typescript": "^5.8.3", "yummacss": "^3.1.0"}, "engines": {"node": "22.x"}}