---
title: Grid Row
banner:
  content: N/A
description: Controls the size and position of elements across rows.
slug: docs/grid-row
---

## Grid Row Span

Controls the row span of a grid item.

<Class category="grid" name="grid-row" />

This example sets the grid row to **span 2 / span 2**. The item will span across **2** rows, starting from its current position and occupying the next **2** rows in the grid.

```html live "gr-s-2" layout="/src/layouts/inline.astro"
<div class="d-g g-4 gtc-3 h-75 ta-c" id="area">
  <div class="ai-c bg-indigo gr-s-2 d-f jc-c p-4 rad-1 tc-white">A</div>
  <div class="ai-c bg-indigo-8 d-f jc-c p-4 rad-1 tc-indigo-5">B</div>
  <div class="ai-c bg-indigo-8 d-f jc-c p-4 rad-1 tc-indigo-5">C</div>
  <div class="ai-c bg-indigo-8 d-f jc-c p-4 rad-1 tc-indigo-5">D</div>
  <div class="ai-c bg-indigo-8 d-f jc-c p-4 rad-1 tc-indigo-5">E</div>
</div>
```

## Grid Row End

Controls where the grid item ends on the grid rows.

<Class category="grid" name="grid-row-end" />

This example sets the grid row end to **3**. The item will end at the first row line, effectively spanning from its starting position to the second row line.

```html live "gre-3" layout="/src/layouts/inline.astro"
<div class="p-r">
  <div class="d-g g-4 gaf-c gtr-3 ta-c tc-white" id="area">
    <div class="ai-c bg-indigo d-f d-g gr-s-2 gre-3 jc-c p-4 rad-1 tc-white">A</div>
    <div class="ai-c bg-indigo-8 d-f d-g gre-4 grs-1 jc-c p-4 rad-1 tc-indigo-5">B</div>
    <div class="ai-c bg-indigo-8 d-f d-g gre-4 grs-1 jc-c p-4 rad-1 tc-indigo-5">C</div>
  </div>
</div>
```

## Grid Row Start

Controls where the grid item begins on the grid rows.

<Class category="grid" name="grid-row-start" />

This example sets the grid row start to **3**. The item will begin at the first row line, positioning it to start in the third row of the grid.

```html live "grs-2" layout="/src/layouts/inline.astro"
<div class="p-r">
  <div class="d-g g-4 gaf-c gtr-3 ta-c tc-white" id="area">
    <div class="ai-c bg-indigo d-f d-g gr-s-2 grs-2 jc-c p-4 rad-1 tc-white">A</div>
    <div class="ai-c bg-indigo-8 d-f d-g gre-4 grs-1 jc-c p-4 rad-1 tc-indigo-5">B</div>
    <div class="ai-c bg-indigo-8 d-f d-g gre-4 grs-1 jc-c p-4 rad-1 tc-indigo-5">C</div>
  </div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/foundations/responsive-design/) or other factors, such as [hover states](/docs/foundations/variants/).

<BreakpointVariant class="gr-1" variant="gr-2" classPrefix="gr">
  ### Breakpoint variant
</BreakpointVariant>

<HoverVariant class="gr-1" variant="gr-2" classPrefix="gr">
  ### Hover variant
</HoverVariant>
