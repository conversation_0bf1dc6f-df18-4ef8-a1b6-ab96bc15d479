---
title: Stylecent
banner:
  content: N/A
description: Stylecent is an optional set of styles for Yumma CSS projects, meant for consistent and modern appearance across browsers.
slug: docs/stylecent
---

## Box sizing

Apply a modern box model and remove browser defaults to simplify layout consistency.

{/* prettier-ignore */}
<Steps>
  1. **Use a more intuitive `box-sizing` model to make the design consistent.**
  2. **Remove default `margin` and `padding`.**
  3. **Reset default `border` styles.**
</Steps>

```scss title="_stylecent.scss"
*,
*::before,
*::after {
  box-sizing: border-box; /* 1 */
  border: 0 solid; /* 3 */
}

* {
  margin: 0; /* 2 */
  padding: 0; /* 2 */
}
```

## Document

Improve `font` rendering and readability with base styles for the `<body>`.

{/* prettier-ignore */}
<Steps>
  1. **Improve `font` smoothing.**
  2. **Set a default system font.**
  3. **Add accessible `line-height`.**
</Steps>

```scss title="_stylecent.scss"
body {
  -webkit-font-smoothing: antialiased; /* 1 */
  font-family: vars.$yma-font-system; /* 2 */
  line-height: 1.5; /* 3 */
}
```

## Media Elements

Ensure responsive images and videos behave predictably in all layouts.

{/* prettier-ignore */}
<Steps>
  1. **Ensure all media elements like images, videos, and canvases are block-level.**
  2. **Limit their maximum width to the parent container.**
</Steps>

```scss title="_stylecent.scss"
canvas,
img,
picture,
svg,
video {
  display: block; /* 1 */
  max-width: 100%; /* 2 */
}
```

## Form Elements

Normalize form controls for visual consistency and better accessibility.

{/* prettier-ignore */}
<Steps>
  1. **Reset background and border styles for form elements.**
  2. **Use `inherit` to ensure font consistency within forms.**
  3. **Add default padding for usability.**
</Steps>

```scss title="_stylecent.scss"
button,
input,
optgroup,
select,
textarea {
  background-color: vars.$yma-color-transparent; /* 1 */
  font-family: inherit; /* 2 */
  padding: 0.5rem; /* 3 */
}
```

Add a default border to form elements that do not have a class attribute.

```scss title="_stylecent.scss"
button:not([class]),
input:not([class]),
optgroup:not([class]),
select:not([class]),
textarea:not([class]) {
  border: 1px solid vars.$yma-color-silver; /* 1 */
}
```

Apply consistent focus styles to interactive elements.

```scss title="_stylecent.scss"
a,
button,
input,
select,
summary,
textarea {
  &:focus {
    outline: 2px solid vars.$yma-color-transparent;
  }
}
```

Set a minimum height for textareas without a defined `rows` attribute.

```scss title="_stylecent.scss"
textarea:not([rows]) {
  min-height: 10em;
}
```

Ensure the buttons have a pointer cursor.

```scss title="_stylecent.scss"
button {
  cursor: pointer;
}
```

Disabled States

{/* prettier-ignore */}
<Steps>
  1. **Reduce opacity and set a "not-allowed" cursor for disabled elements.**
</Steps>

```scss title="_stylecent.scss"
button:disabled,
input:disabled,
select:disabled,
textarea:disabled {
  cursor: not-allowed; /* 1 */
  opacity: 0.5; /* 1 */
}
```

## Typography

Improve text rendering, wrapping, and default font behavior across browsers.

{/* prettier-ignore */}
<Steps>
  1. **Avoid text overflows.**
  2. **Improve line wrapping for headings.**
  3. **Add a consistent font weight for bold text.**
</Steps>

```scss title="_stylecent.scss"
h1,
h2,
h3,
h4,
h5,
h6,
p {
  overflow-wrap: break-word; /* 1 */
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: 1rem; /* 3 */
  font-weight: 600; /* 3 */
  text-wrap: balance; /* 2 */
}

p {
  text-wrap: pretty; /* 2 */
}
```

Add the correct font weight in Chrome, Edge, and Safari.

```scss title="_stylecent.scss"
b,
strong {
  font-weight: 700;
}
```

Add the correct font size in all browsers.

```scss title="_stylecent.scss"
small {
  font-size: 80%;
  line-height: 1.5;
}
```

{/* prettier-ignore */}
<Steps>
  1. **Correct the inheritance and scaling of font size in all browsers.**
  2. **Correct the odd `em` font sizing in all browsers.**
</Steps>

```scss title="_stylecent.scss"
pre,
code,
kbd,
samp {
  font-family: monospace, monospace; /* 1 */
  font-size: 1em; /* 2 */
}
```

1. **Remove underline styling from links by default.**
2. **Reset color to inherit from parent element.**

```scss title="_stylecent.scss"
a {
  color: inherit; /* 3 */
  text-decoration: none; /* 1 */
}
```

## Lists

Remove default list styles for a cleaner and more customizable base.

```scss title="_stylecent.scss"
ol,
ul {
  list-style: none;
  padding: 0;
}
```

## Tables

Standardize table header styling for better readability.

{/* prettier-ignore */}
<Steps>
  1. **Add a consistent font weight for bold text.**
</Steps>

```scss title="_stylecent.scss"
th {
  font-size: 1rem; /* 1 */
  font-weight: 600; /* 1 */
}
```

## Miscellaneous

Fix minor browser quirks and improve default styles for common elements.

{/* prettier-ignore */}
<Steps>
  1. **Add the correct height in Firefox.**
  2. **Correct text decoration.**
  3. **Add spacing around horizontal rules.**
</Steps>

```scss title="_stylecent.scss"
hr {
  border-top: 1px solid vars.$yma-color-silver; /* 2 */
  height: 0; /* 1 */
  margin: 1em 0; /* 3 */
}
```

Ensure details and summary elements display correctly.

```scss title="_stylecent.scss"
details {
  display: block;
}

summary {
  display: list-item;
}
```

### Disabling Stylecent

To disable specific styles, you can use the [`buildOptions.reset`](/docs/foundations/config/#reset) option and set it to `false` in your configuration file.
