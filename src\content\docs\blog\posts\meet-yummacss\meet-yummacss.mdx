---
authors: Renildo
date: 2025-05-29
description: Get to know Yumma CSS and learn how to use it in your next project!
pagefind: false
slug: blog/meet-yummacss
title: Meet Yumma CSS
---

Maintaining and scaling styles in CSS can be a lot of work, whether you're using [inline or external styles](https://www.w3schools.com/html/html_css.asp). It can be really frustrating when you're trying to keep your designs consistently predictable, and most importantly, scalable.

{/* excerpt */}

## What is Yumma CSS?

Think of Yumma CSS as a set of abbreviated utility classes for building faster and more maintainable UIs. [Yumma CSS](/) has a wide range of reusable utilities that you can apply to your design system to make it more consistent and less unpredictable.

Here's a reference example of the Yumma CSS utilities:

| CSS Property                             | Yumma CSS Utility |
| ---------------------------------------- | ----------------- |
| `align-items: center;`                   | `ai-c`            |
| `background-color: white;`               | `bg-white`        |
| `background-repeat: no-repeat;`          | `br-nr`           |
| `background-size: cover;`                | `bs-c`            |
| `border-color: black;`                   | `bc-black`        |
| `border-radius: 0.5rem;`                 | `rad-2`           |
| `border-width: 1px;`                     | `b-1`             |
| `box-sizing: border-box;`                | `bs-bb`           |
| `display: flex;`                         | `d-f`             |
| `filter: grayscale(100%);`               | `f-g-100`         |
| `font-weight: 600;`                      | `fw-600`          |
| `gap: 1rem;`                             | `g-4`             |
| `grid-column: span 2 / span 2;`          | `gc-s-2`          |
| `grid-template-columns: repeat(3, 1fr);` | `gtc-3`           |
| `height: auto;`                          | `h-auto`          |
| `justify-content: space-between;`        | `jc-sb`           |
| `margin: 1rem;`                          | `m-4`             |
| `opacity: 0.5;`                          | `o-50`            |
| `outline-style: solid;`                  | `os-s`            |
| `outline-width: 2px;`                    | `ow-2`            |
| `overflow: hidden;`                      | `o-h`             |
| `padding: 0.5rem;`                       | `p-2`             |
| `text-align: center;`                    | `ta-c`            |
| `text-color: white;`                     | `tc-white`        |
| `width: 100%;`                           | `w-full`          |

## Using Flexbox utilities

Let’s look at a classic layout case: centering with flexbox.

### Using inline styles

```html
<div style="align-items: center; display: flex; justify-content: center;">...</div>
```

### Using external styles

```css
p {
  align-items: center;
  display: flex;
  justify-content: center;
}
```

**Using Yumma CSS**

```html
<div class="ai-c d-f jc-c">...</div>
```

You end up with the same result, but the code is much shorter, more reusable, and way easier to grow.

You can use consistent patterns like `m-{n}` or `bg-{color}` to get faster prototyping directly in markup without writing the traditional inline or external styles.

## Overriding styles conditionally

So, let's say you use a specific utility in your markup and you want to override its value based on the user's [screen size](https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_media_queries/Using_media_queries) or other factors, such as [hover states](https://developer.mozilla.org/en-US/docs/Web/CSS/:hover).

Yumma CSS hooks you up with [utility modifiers](/docs/first-steps#conditional-styles) that let you target use cases like `sm:*`, `md:*`, `lg:*`, and `xxl:*` to target a specific viewport, and `h:*` to target hover states.

## Media queries

Let's take a look at how we can use media queries to change the layout of a grid based on the size of the screen.

### Using external styles

```css
.container {
  display: grid;
  gap: 4rem;
  grid-template-columns: 1fr;
}

@media (min-width: 640px) {
  .container {
    grid-template-columns: repeat(3, 1fr);
  }
}
```

### Using Yumma CSS

```html
<div class="d-g g-16 gtc-1 sm:gtc-3">
  <div class="bg-indigo d-16"></div>
  <div class="bg-indigo d-16"></div>
  <div class="bg-indigo d-16"></div>
</div>
```

## Hover states

Also, you can control [hover states](/docs/first-steps#hover-modifiers) with Yumma CSS. For example, if you want to hide an element when the user hovers over it, you can use the `h:*` modifier.

### Using external styles

```css
.flex {
  display: flex;
}

.hidden:hover {
  display: none;
}
```

### Using Yumma CSS

```html
<div class="d-g g-16 gtc-1 sm:gtc-3">
  <div class="ai-c bg-indigo d-16 d-f h:d-none jc-c"></div>
</div>
```

## Getting started

Try [Yumma CSS with npm](https://www.npmjs.com/package/yummacss) or in your browser on [Yumma CSS Play](https://play.yummacss.com).

```bash
npm install yummacss -D
```

## Closing thoughts

With [Yumma CSS](/), you get a set of abbreviated utilities designed to make your design system easier to maintain and extend, so you can focus on creating reusable UI components.

Learn more about Yumma CSS, [in the documentation](/docs/first-steps).
