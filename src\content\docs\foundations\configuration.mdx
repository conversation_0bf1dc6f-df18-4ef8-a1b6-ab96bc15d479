---
title: CLI Setup
banner:
  content: N/A
description: Learn how to set up the Yumma CSS CLI.
slug: docs/foundations/cli-setup
---

## Installing the CLI

Yumma CSS comes with the Yumma CLI, a powerful tool that's a breeze to set up and takes no time or effort for production-ready projects.

<PackageManagers pkgManagers={["pnpm", "npm", "yarn"]} pkg="yummacss -D" title="Terminal" />

## Creating your configuration file

To start using the CLI, you need to create a configuration file in your project.

<PackageManagers pkgManagers={["pnpm", "npm", "yarn"]} pkg="yummacss init" title="Terminal" type="dlx" />

This will create a complete `yumma.config.js` file at the root of your project shown below.

```js title="yumma.config.js"
export default {
  source: [""],
  output: "",
  buildOptions: {
    reset: true,
    minify: false,
  },
};
```

### Using ES modules

If you are using ESM modules, you need to use the `module.exports` syntax instead of `export default`.

```js title="yumma.config.js"
module.exports = {
  source: [""],
  output: "",
  buildOptions: {
    reset: true,
    minify: false,
  },
};
```

### Using CommonJS

If you are using CommonJS modules, you need to use the `export default` syntax instead of `module.exports`.

```js title="yumma.config.js"
export default {
  source: [""],
  output: "",
  buildOptions: {
    reset: true,
    minify: false,
  },
};
```

## Configuration options

### Source

A collection of paths for your template files, such as `.js`, `.tsx`, or `.html` files. This is where the CLI will look for utility classes to include in the final CSS file.

<Note icon="info">Use [glob patterns](https://code.visualstudio.com/docs/editor/glob-patterns#_glob-pattern-syntax) to include subfolders and specific file types.</Note>

```js title="yumma.config.js" mark={2}
module.exports = {
  source: ["./src/**/*.html"],
};
```

### Output

A string that specifies the output path for the compiled CSS file. This is where the CLI will save the final CSS file after processing your template files.

```js title="yumma.config.js" mark={2}
module.exports = {
  output: "./src/styles.css",
};
```

### Reset

A boolean that determines whether the inclusion of the [Stylecent reset system](/docs/stylecent).

```js title="yumma.config.js" mark={3}
module.exports = {
  buildOptions: {
    reset: true, // default: true
  },
};
```

### Minify

A boolean that determines whether the compiled CSS files should or not be minified. This is useful for production builds where you want to reduce the file size for faster loading times.

```js title="yumma.config.js" mark={3}
module.exports = {
  buildOptions: {
    minify: true, // default: false
  },
};
```

## Generating styles

The CLI will scan all the files specified in the `source` array and generate a single CSS file based on the utility classes used in those files. The output file is specified in the `output` field.

### Input

Let's say you have the following HTML file:

```jsx title="index.html"
<button class="bg-white px-4 rad-1 bs-lg fw-600">
  Button
</div>
```

### Output

Once you run the either `build` or `watch` command, the CLI will generate the following CSS file:

```css title="styles.css"
h1 {
  overflow-wrap: break-word;
  text-wrap: balance;
  font-size: 1rem;
  font-weight: 600;
}

.my-2 {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.fs-xl {
  font-size: 1.25rem;
}

.ta-c {
  text-align: center;
}
```

## CLI Commands

The Yumma CSS CLI provides several commands to help you work with your Yumma CSS files.

<Note icon="info">
  Make sure you have a [`yumma.config.js`](/docs/first-steps#get-started-with-yumma-cli) file set up before running these commands. If you haven't, run `npx yummacss init` first.
</Note>

### Build command

The `build` command will compile your Yumma CSS files once.

<PackageManagers pkgManagers={["pnpm", "npm", "yarn"]} type="exec" pkg="yummacss" args="build" title="Terminal" />

### Watch command

The `watch` command will watch for changes in your Yumma CSS files and recompile them automatically.

<PackageManagers pkgManagers={["pnpm", "npm", "yarn"]} type="exec" pkg="yummacss" args="watch" title="Terminal" />
