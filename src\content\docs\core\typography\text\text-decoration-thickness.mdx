---
title: Text Decoration Thickness
banner:
  content: N/A
description: Controls the thickness of text decorations.
slug: docs/text-decoration-thickness
---

<Class category="text" name="text-decoration-thickness" />

## Auto

> Initial value

This example sets the text decoration thickness to **auto**. The text will have a default decoration thickness.

```html live "tdt-auto" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg p-4 rad-1 ta-c tc-lead tdl-u tdt-auto">Sphinx of black quartz, judge my vow.</p>
</div>
```

## 0

This example sets the text decoration thickness to **0px**. The text will have no visible decoration thickness.

```html live "tdt-0" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg p-4 rad-1 ta-c tc-lead tdl-u tdt-0">Sphinx of black quartz, judge my vow.</p>
</div>
```

## 1

This example sets the text decoration thickness to **1px**. The text will have a thin decoration line.

```html live "tdt-1" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg p-4 rad-1 ta-c tc-lead tdl-u tdt-1">Sphinx of black quartz, judge my vow.</p>
</div>
```

## 2

This example sets the text decoration thickness to **2px**. The text will have a slightly thicker decoration line.

```html live "tdt-2" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg p-4 rad-1 ta-c tc-lead tdl-u tdt-2">Sphinx of black quartz, judge my vow.</p>
</div>
```

## 3

This example sets the text decoration thickness to **3px**. The text will have a moderately thick decoration line.

```html live "tdt-3" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg p-4 rad-1 ta-c tc-lead tdl-u tdt-3">Sphinx of black quartz, judge my vow.</p>
</div>
```

## 4

This example sets the text decoration thickness to **4px**. The text will have a thick decoration line.

```html live "tdt-4" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg p-4 rad-1 ta-c tc-lead tdl-u tdt-4">Sphinx of black quartz, judge my vow.</p>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<MediaModifier class="tdt-1" mediaModifier="tdt-2" classPrefix="tdt">
  ### Media modifier
</MediaModifier>

<HoverModifier class="tdt-1" hoverModifier="tdt-2" classPrefix="tdt">
  ### Hover modifier
</HoverModifier>
