---
title: Backdrop Blur
banner:
  content: N/A
description: Applies background blur filters to an element.
slug: docs/backdrop-blur
---

<Class category="effect" name="backdrop-blur" />

This example showcases various `blur()` utilities:

- The **bf-b-sm** backdrop blur utility sets the `backdrop-filter` to **blur(8px)**.
- The **bf-b-md** backdrop blur utility sets the `backdrop-filter` to **blur(16px)**.
- Finally, **bf-b-lg** backdrop blur utility sets the `backdrop-filter` to **blur(32px)**.

```html live "bf-b-sm" "bf-b-md" "bf-b-lg"
<div class="d-g g-16 gtc-1 sm:gtc-3">
  <div class="d-32 p-r rad-1">
    <img class="d-full of-c rad-1" src="https://picsum.photos/300?image=872" />
    <div class="ai-c bf-b-sm d-f d-22 i-0 jc-c m-auto p-a rad-1 tc-white">sm</div>
  </div>
  <div class="d-32 p-r rad-1">
    <img class="d-full of-c rad-1" src="https://picsum.photos/300?image=872" />
    <div class="ai-c bf-b-md d-f d-22 i-0 jc-c m-auto p-a rad-1 tc-white">md</div>
  </div>
  <div class="d-32 p-r rad-1">
    <img class="d-full of-c rad-1" src="https://picsum.photos/300?image=872" />
    <div class="ai-c bf-b-lg d-f d-22 i-0 jc-c m-auto p-a rad-1 tc-white">lg</div>
  </div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<MediaModifier class="bf-b-none" mediaModifier="bf-b-sm" classPrefix="bf-b">
  ### Breakpoint modifier
</MediaModifier>

<HoverModifier class="bf-b-none" hoverModifier="bf-b-sm" classPrefix="bf-b">
  ### Hover modifier
</HoverModifier>
