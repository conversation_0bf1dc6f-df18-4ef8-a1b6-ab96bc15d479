---
title: Solid.js with Yumma CSS
banner:
  content: N/A
description: Integrate Yumma CSS into Solid.js applications.
slug: docs/guides/solidjs
---

## Creating a new project

To create a new Solid.js project, you need run the [Solid.js command](https://docs.solidjs.com/quick-start#for-typescript-projects) in your terminal.

<Steps>

    1. **Install Yumma CSS**

        Install `yummacss` using a package manager.

        <PackageManagers pkgManagers={["pnpm", "npm", "yarn"]} pkg="yummacss -D" title="Terminal" />

        <br />

    2. **Initialize configuration**

            Create a configuration file in your project.

            <PackageManagers pkgManagers={["pnpm", "npm", "yarn"]} pkg="yummacss init" title="Terminal" type="dlx" />

    3. **Set up configuration**

        Specify the locations of all your project files in the config file.

        ```js title="yumma.config.js" mark={2-3}
        module.exports = {
            source: ["./src/**/*.{js,jsx}"],
            output: "./src/index.css",
            buildOptions: {
                reset: true,
                minify: false,
            }
        };
        ```

    4. **Build styles**

        You can now start generating your CSS with the [`build`](/docs/first-steps#build-command) or [`watch`](/docs/first-steps#watch-command) command.

        <PackageManagers pkgManagers={["pnpm", "npm", "yarn"]} pkg="yummacss build" title="Terminal" type="dlx" />

    5. **Done!**

        You're all set to start using Yumma CSS utility classes in your project.

        ```jsx title="App.jsx" mark={3-5}
        function App() {
            return (
                <div class="bg-indigo-12 d-g h-dvh pi-c tc-white">
                    <h1 class="fs-3xl fw-500">Yumma CSS ⚙️ Solid.js</h1>
                </div>
            );
        }

        export default App;
        ```

</Steps>

---

## Clone this project

Skip the guide steps entirely with our Solid.js starter.

```bash title="Cloning the project..."
git clone https://github.com/yumma-lib/with-solidjs.git
```
