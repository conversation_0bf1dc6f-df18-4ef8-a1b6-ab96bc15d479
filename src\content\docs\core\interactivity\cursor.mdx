---
title: Cursor
banner:
  content: N/A
description: Controls the cursor style when hovering over an element.
slug: docs/cursor
---

<Class category="interactivity" name="cursor" />

## Auto

> Initial value

This example sets the cursor to **auto**, indicating that the cursor is in its default state.

<Note icon="cursor_arrow">Try hovering over the buttons to see how the cursor transforms with your mouse.</Note>

{/* prettier-ignore */}
```html live "c-auto"
<button class="c-auto b-2 bc-indigo bg-indigo-2 d-g fw-600 pi-c px-8 py-1 rad-1 rad-2 tc-indigo">
  Login
</button>
```

## Help

This example sets the cursor to **help**, indicating that additional information is available.

<Note icon="cursor_arrow">Try hovering over the buttons to see how the cursor transforms with your mouse.</Note>

{/* prettier-ignore */}
```html live "c-h"
<button class="c-h b-2 bc-indigo bg-indigo-2 d-g fw-600 pi-c px-8 py-1 rad-1 rad-2 tc-indigo">
  Help
</button>
```

## Not Allowed

This example sets the cursor to **not-allowed**, indicating that the action is not permitted.

<Note icon="cursor_arrow">Try hovering over the buttons to see how the cursor transforms with your mouse.</Note>

{/* prettier-ignore */}
```html live "c-na"
<button class="c-na b-2 bc-red bg-red-2 d-g fw-600 pi-c px-8 py-1 rad-1 rad-2 tc-red">
  Stop
</button>
```

## Pointer

This example sets the cursor to **pointer**, indicating that the element is clickable.

<Note icon="cursor_arrow">Try hovering over the buttons to see how the cursor transforms with your mouse.</Note>

{/* prettier-ignore */}
```html live "c-p"
<button class="c-p b-2 bc-indigo bg-indigo-2 d-g fw-600 pi-c px-8 py-1 rad-1 rad-2 tc-indigo">
  Buy
</button>
```

## Progress

This example sets the cursor to **progress**, indicating that the application is performing some processing and the user should wait.

<Note icon="cursor_arrow">Try hovering over the buttons to see how the cursor transforms with your mouse.</Note>

{/* prettier-ignore */}
```html live "c-pr"
<button class="c-pr b-2 bc-indigo bg-indigo-2 d-g fw-600 pi-c px-8 py-1 rad-1 rad-2 tc-indigo">
  Wait
</button>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<MediaModifier class="c-p" mediaModifier="c-wait" classPrefix="c">
  ### Breakpoint modifier
</MediaModifier>

<HoverModifier class="c-p" hoverModifier="c-wait" classPrefix="c">
  ### Hover modifier
</HoverModifier>
