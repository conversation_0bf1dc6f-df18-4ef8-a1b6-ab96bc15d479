---
import tinycolor from "tinycolor2";

interface ColorItem {
  name: string;
  color: string;
}

interface Props {
  data: ColorItem[];
  percentage?: number;
}

const { data } = Astro.props as Props;

const percentage = 14;

const generateShades = (color: string): string[] => {
  const shades: string[] = [];

  for (let i = 1; i <= 6; i++) {
    const weight = (7 - i) * percentage;
    const mixedColor = tinycolor.mix(color, "white", weight);
    shades.push(mixedColor.toHexString());
  }

  shades.push(tinycolor(color).toHexString());

  for (let i = 1; i <= 6; i++) {
    const weight = i * percentage;
    const mixedColor = tinycolor.mix(color, "black", weight);
    shades.push(mixedColor.toHexString());
  }

  return shades;
};
---

<div class="d-f fd-c">
  <div class="d-none md:d-g fs-xs ta-c gtc-14">
    <div></div>
    {Array.from({ length: 13 }, (_, i) => <div>{i === 6 ? "Base" : i < 6 ? i + 1 : i}</div>)}
  </div>

  {
    data.map((colorItem) => {
      const shades = generateShades(colorItem.color);
      return (
        <div class="d-g g-1 ai-c md:gtc-14 md:f-none d-f fd-c md:d-g">
          <div class="fs-sm ws-nw md:wm-htb md:fs-sm ta-c fs-md ws-n">{colorItem.name}</div>
          {shades.map((shade) => (
            <div class="h-12 md:h-10 w-full" style={{ backgroundColor: shade }} onclick={`navigator.clipboard.writeText('${shade}')`} />
          ))}
        </div>
      );
    })
  }
</div>

<!-- TODO - yummacss@v3.1 -->
<style>
  @media (width >= 48rem) {
    .md\:wm-htb {
      writing-mode: vertical-lr;
    }
  }

  .wm-htb {
    writing-mode: horizontal-tb;
  }
</style>
