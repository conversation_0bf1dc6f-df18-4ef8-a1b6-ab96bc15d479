---
import tinycolor from "tinycolor2";
import { Tooltips } from "astro-tooltips";
interface ColorItem {
  name: string;
  color: string;
}

interface Props {
  data: ColorItem[];
  percentage?: number;
}

const { data } = Astro.props as Props;

const percentage = 14;

const generateShades = (color: string): string[] => {
  const shades: string[] = [];

  for (let i = 1; i <= 6; i++) {
    const weight = (7 - i) * percentage;
    const mixedColor = tinycolor.mix(color, "white", weight);
    shades.push(mixedColor.toHexString());
  }

  shades.push(tinycolor(color).toHexString());

  for (let i = 1; i <= 6; i++) {
    const weight = i * percentage;
    const mixedColor = tinycolor.mix(color, "black", weight);
    shades.push(mixedColor.toHexString());
  }

  return shades;
};

// Function to determine if a color needs a border for better visibility
const getBorderStyle = (color: string): string => {
  const tc = tinycolor(color);
  const lc = tc.getLuminance();

  // For very light colors (luminance > 0.9), add a subtle border
  if (lc > 0.9) {
    return "border: 1px solid rgba(0, 0, 0, 0.1);";
  }

  // For very dark colors (luminance < 0.1), add a subtle light border in dark mode
  if (lc < 0.1) {
    return "border: 1px solid light-dark(rgba(0, 0, 0, 0.1), rgba(255, 255, 255, 0.1));";
  }

  return "";
};
---

<Tooltips interactive={false} delay={[1, 100]} />

<div class="d-f fd-c">
  <div class="d-none lg:d-g fs-xs ta-c gtc-14">
    <div></div>
    {Array.from({ length: 13 }, (_, i) => <div>{i === 6 ? "Base" : i < 6 ? i + 1 : i}</div>)}
  </div>

  {
    data.map((colorItem) => {
      const shades = generateShades(colorItem.color);
      return (
        <div class="palette-row">
          <div class="palette-name">{colorItem.name}</div>
          <div class="palette-shades">
            {shades.map((shade) => (
              <div
                class="palette-shade"
                style={`background-color: ${shade}; ${getBorderStyle(shade)}`}
                onclick={`navigator.clipboard.writeText('${shade}')`}
                title={`${shade}`}
                data-tooltip-placement="top"
                data-tooltip-interactive="true"
                role="button"
                tabindex="0"
              />
            ))}
          </div>
        </div>
      );
    })
  }
</div>

<style>
  .palette-row {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    align-items: center;

    /* d-f fd-c g-1 ai-c */
  }

  .palette-name {
    font-size: 0.875rem;
    white-space: nowrap;
    text-align: center;
    font-weight: 500;
    min-width: fit-content;

    /* fs-sm ws-nw ta-c fw-500 min-w-fc */
  }

  .palette-shades {
    display: grid;
    gap: 0.25rem;
    width: 100%;
    /* Mobile: 2 columns */
    grid-template-columns: repeat(2, 1fr);

    /* d-g g-1 w-full gtc-2 */
  }

  .palette-shade {
    height: 3rem;
    width: 100%;
    border-radius: 0.25rem;
    cursor: pointer;
    position: relative;
    /* h-12 w-full rad-1 c-p p-r */
  }

  /* Small screens: 4 columns */
  @media (width >= 30rem) {
    .palette-shades {
      grid-template-columns: repeat(4, 1fr);
    }
  }

  /* Medium screens: 6 columns */
  @media (width >= 48rem) {
    .palette-row {
      flex-direction: row;
      align-items: center;
    }

    .palette-name {
      font-size: 0.875rem;
      min-width: 2rem;
      flex-shrink: 0;
    }

    .palette-shades {
      grid-template-columns: repeat(6, 1fr);
      flex: 1;
    }

    .palette-shade {
      height: 2.5rem;
    }
  }

  /* Large screens: show all 13 columns */
  @media (width >= 64rem) {
    .palette-shades {
      grid-template-columns: repeat(13, 1fr);
    }

    .palette-shade {
      height: 2.5rem;
    }
  }

  /* Extra large screens: optimize spacing */
  @media (width >= 80rem) {
    .palette-shades {
      gap: 0.125rem;
    }
  }
</style>
