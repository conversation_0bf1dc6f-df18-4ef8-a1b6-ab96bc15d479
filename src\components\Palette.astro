---
import tinycolor from "tinycolor2";
import { Tooltips } from "astro-tooltips";
interface ColorItem {
  name: string;
  color: string;
}

interface Props {
  data: ColorItem[];
  percentage?: number;
}

const { data } = Astro.props as Props;

const percentage = 14;

const generateShades = (color: string): string[] => {
  const shades: string[] = [];

  for (let i = 1; i <= 6; i++) {
    const weight = (7 - i) * percentage;
    const mixedColor = tinycolor.mix(color, "white", weight);
    shades.push(mixedColor.toHexString());
  }

  shades.push(tinycolor(color).toHexString());

  for (let i = 1; i <= 6; i++) {
    const weight = i * percentage;
    const mixedColor = tinycolor.mix(color, "black", weight);
    shades.push(mixedColor.toHexString());
  }

  return shades;
};

const getBorderStyle = (color: string): string => {
  const tc = tinycolor(color);
  const lc = tc.getLuminance();

  if (lc > 0.9) {
    return "border: 1px solid rgba(0, 0, 0, 0.1);";
  }

  if (lc < 0.1) {
    return "border: 1px solid light-dark(rgba(0, 0, 0, 0.1), rgba(255, 255, 255, 0.1));";
  }

  return "";
};
---

<Tooltips delay={[0, 0]} />

<div class="d-f fd-c">
  <div class="d-none lg:d-g fs-xs ta-c gtc-14">
    <div></div>
    {Array.from({ length: 13 }, (_, i) => <div>{i === 6 ? "Base" : i < 6 ? i + 1 : i}</div>)}
  </div>

  {
    data.map((colorItem) => {
      const shades = generateShades(colorItem.color);
      return (
        <div class="d-f fd-c g-1 ai-c md:fd-r md:ai-c">
          <p class="fs-sm ws-nw ta-c fw-500 min-w-fc md:min-w-16 jc-c d-f ai-c mr-2">{colorItem.name}</p>
          <div class="d-g g-1 w-full gtc-2 sm:gtc-4 md:gtc-6 lg:gtc-13 f-1 not-content">
            {shades.map((shade) => (
              <div
                class="h-12 w-full rad-1 c-p md:h-10"
                style={`background-color: ${shade}; ${getBorderStyle(shade)}`}
                onclick={`navigator.clipboard.writeText('${shade}')`}
                title={`${shade}`}
                data-tooltip-placement="top"
                data-tooltip-interactive="true"
                role="button"
                tabindex="0"
              />
            ))}
          </div>
        </div>
      );
    })
  }
</div>
