---
title: Order
banner:
  content: N/A
description: Controls the order of flex and grid items.
slug: docs/order
---

<Class category="flexbox" name="order" />

This example showcases various `order` utilities:

- The **or-l** order utility sets the order property to **-9999**, placing the item at the beginning of the flex container.
- The **or-f** order utility sets the order property to **9999**, placing the item at the end of the flex container.

```html live "or-f" "or-l" layout="/src/layouts/inline.astro"
<div class="d-f jc-sb tc-white">
  <div class="ai-c bg-indigo d-f d-14 jc-c or-f rad-1">A</div>
  <div class="ai-c bg-indigo d-f d-14 jc-c rad-1">B</div>
  <div class="ai-c bg-indigo d-f d-14 jc-c or-l rad-1">C</div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<MediaModifier class="or-f" mediaModifier="or-l" classPrefix="or">
  ### Media modifier
</MediaModifier>

<HoverModifier class="or-f" hoverModifier="or-l" classPrefix="or">
  ### Hover modifier
</HoverModifier>
