---
authors: Renildo
cover:
  alt: Yumma CSS
  image: cover.png
date: 2024-08-12
description: Yes, it's happening again! We're excited to announce the release of Yumma CSS v2.0.0! This new update is full of new features, big improvements, a redesigned source code, and of course, some important fixes.
pagefind: false
slug: blog/yummacss-2.0
tags: ["release"]
title: Yumma CSS 2.0
---

Yes, it's happening again! We're excited to announce the release of Yumma CSS v2.0.0! This new update is full of new features, big improvements, a redesigned source code, and of course, some important fixes.

{/* excerpt */}

<iframe
  allowfullscreen
  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
  class="ar-16/9 rad-1 w-full"
  frameborder="0"
  referrerpolicy="strict-origin-when-cross-origin"
  src="https://youtube.com/embed/fNqHGnfsJQI?si=qp3qz3b9c0RohZaM"
  title="What's new in Yumma CSS 2.0?"></iframe>

You might also want to check out the [release notes](https://github.com/yumma-lib/yumma-css/releases/tag/v2.0.0). Anyway, these are the most noticeable changes:

- [All-new Utilities](#all-new-utilities): Silver color, Gap, Spacing, and much more
- [Base Styles](#base-styles): Base styles are now optional
- [Responsive Breakpoints](#responsive-breakpoints): Media query utilities support extended
- [Container Rules](#container-rules): New container utility rules

This is a major update that introduces groundbreaking features. Major releases follow [semantic versioning](https://docs.npmjs.com/about-semantic-versioning) conventions. In other words, you probably need refactoring after upgrading.

---

## All new utilities

As part of our standard procedure for each new grand release, we implement a range of new customization utilities.

### Accent Color

This latest release is no exception, as it introduces the Accent Color utilities.

<LegacyColor
  classPrefix="ac-"
  propNames={["accent-color"]}
  data={[
    { color: "red", value: "#d73d3d" },
    { color: "orange", value: "#e06814" },
    { color: "yellow", value: "#d3a107" },
    { color: "green", value: "#1fb155" },
    { color: "teal", value: "#12a695" },
    { color: "cyan", value: "#05a4bf" },
    { color: "blue", value: "#3575dd" },
    { color: "indigo", value: "#595cd9" },
    { color: "violet", value: "#7d53dd" },
    { color: "pink", value: "#d4418a" },
    { color: "gray", value: "#606773" },
    { color: "lead", value: "#3f3f4e" },
    { color: "silver", value: "#bfc2c7" },
  ]}
/>

We're also adding a new color to the color palette — Silver.

<LegacyPalette
  data={[
    {
      name: "Silver",
      color: "#bfc2c7",
    },
  ]}
/>

### Aspect Ratio

The Aspect Ratio is a great addition, especially for image elements.

<LegacyClass
  data={[
    {
      className: "ar-s",
      properties: ["aspect-ratio: 1/1;"],
    },
    {
      className: "ar-l",
      properties: ["aspect-ratio: 16/9;"],
    },
    {
      className: "ar-auto",
      properties: ["aspect-ratio: auto;"],
    },
  ]}
/>

### Backdrop Filter

You can now apply cool effects to your components thanks to the Backdrop Filter utilities.

<LegacyClass
  data={[
    {
      className: "bf-b-none",
      properties: ["backdrop-filter: blur(0px);"],
    },
    {
      className: "bf-b-xs",
      properties: ["backdrop-filter: blur(4px);"],
    },
    {
      className: "bf-b-sm",
      properties: ["backdrop-filter: blur(8px);"],
    },
    {
      className: "bf-b-md",
      properties: ["backdrop-filter: blur(16px);"],
    },
    {
      className: "bf-b-lg",
      properties: ["backdrop-filter: blur(32px);"],
    },
    {
      className: "bf-b-xl",
      properties: ["backdrop-filter: blur(64px);"],
    },
  ]}
/>

### Border

We're also going to be adding some new Border utilities.

#### Border Collapse

We're finally adding the Border Collapse utilities to create better tables in your site with Yumma CSS.

<LegacyClass
  data={[
    {
      className: "bc-c",
      properties: ["border-collapse: collapse;"],
    },
    {
      className: "bc-s",
      properties: ["border-collapse: separate;"],
    },
  ]}
/>

#### Border Style

The new Border Style utilities let you change how borders look on different elements.

<LegacyClass
  data={[
    {
      className: "b-none",
      properties: ["border-style: none;"],
    },
    {
      className: "b-d",
      properties: ["border-style: dashed;"],
    },
    {
      className: "b-h",
      properties: ["border-style: hidden;"],
    },
    {
      className: "b-s",
      properties: ["border-style: solid;"],
    },
  ]}
/>

### Caption Side

We're also adding some handy features for tables, like Caption Side.

<LegacyClass
  data={[
    {
      className: "cs-b",
      properties: ["caption-side: bottom;"],
    },
    {
      className: "cs-t",
      properties: ["caption-side: top;"],
    },
  ]}
/>

### Font Style

You can now change the Font Style of text elements.

<LegacyClass
  data={[
    {
      className: "fs-i",
      properties: ["font-style: italic;"],
    },
    {
      className: "fs-n",
      properties: ["font-style: normal;"],
    },
  ]}
/>

### Gap

The Gap utilities aren't necessarily new, but they are different in older versions. Gap was designed for responsive layouts, not for adding gaps between elements.

<LegacyClass classPrefix="g-" propNames={["gap"]} incrementValue={0.25} range={16} unit="rem" />

### Grid

If you're looking for more control over your grid layouts, this new update includes new utility classes to control Grid Column and Grid Row properties.

<Tabs>

  <TabItem label="Column">

    **Grid Column**

    <LegacyClass
      classPrefix="gc-s-"
      excludeZero
      propNames={["grid-column"]}
      incrementName="span %i / span %i"
      incrementValue={1}
      range={16}
    />

    **Grid Column End**

    <LegacyClass
      classPrefix="gce-"
      propNames={["grid-column-end"]}
      incrementValue={1}
      range={16}
    />

    **Grid Column Start**

    <LegacyClass
      classPrefix="gcs-"
      propNames={["grid-column-start"]}
      incrementValue={1}
      range={16}
    />

  </TabItem>

  <TabItem label="Grid">

    **Grid Row Span**

    <LegacyClass
      classPrefix="gr-s-"
      excludeZero
      propNames={["grid-row"]}
      incrementName="span %i / span %i"
      incrementValue={1}
      range={16}
    />

    **Grid Row End**

    <LegacyClass
      classPrefix="gre-"
      propNames={["grid-row-end"]}
      incrementValue={1}
      range={16}
    />

    **Grid Row Start**

    <LegacyClass
      classPrefix="grs-"
      propNames={["grid-row-start"]}
      incrementValue={1}
      range={16}
    />

  </TabItem>
</Tabs>

### Object

We're also including the Object Fit and Object Position utility classes.

#### Object Fit

<LegacyClass
  data={[
    {
      className: "of-c",
      properties: ["object-fit: cover;"],
    },
    {
      className: "of-f",
      properties: ["object-fit: fill;"],
    },

    {
      className: "of-none",
      properties: ["object-fit: none;"],
    },
    {
      className: "of-sd",
      properties: ["object-fit: scale-down;"],
    },

]}
/>

#### Object Position

<LegacyClass
  data={[
    {
      className: "op-b",
      properties: ["object-position: bottom;"],
    },
    {
      className: "op-c",
      properties: ["object-position: center;"],
    },
    {
      className: "op-lb",
      properties: ["object-position: left bottom;"],
    },
    {
      className: "op-left",
      properties: ["object-position: left;"],
    },
    {
      className: "op-lt",
      properties: ["object-position: left top;"],
    },
    {
      className: "op-r",
      properties: ["object-position: right;"],
    },
    {
      className: "op-rb",
      properties: ["object-position: right bottom;"],
    },
    {
      className: "op-rt",
      properties: ["object-position: right top;"],
    },
    {
      className: "op-t",
      properties: ["object-position: top;"],
    },
  ]}
/>

### Outline

On top of the customization utilities we've already talked about, Yumma CSS 2.0 also includes Outline Color, Outline Offset, Outline Style, and Outline Width utility classes.

#### Outline Color

<LegacyColor
  classPrefix="oc-"
  propNames={["outline-color"]}
  data={[
    { color: "red", value: "#d73d3d" },
    { color: "orange", value: "#e06814" },
    { color: "yellow", value: "#d3a107" },
    { color: "green", value: "#1fb155" },
    { color: "teal", value: "#12a695" },
    { color: "cyan", value: "#05a4bf" },
    { color: "blue", value: "#3575dd" },
    { color: "indigo", value: "#595cd9" },
    { color: "violet", value: "#7d53dd" },
    { color: "pink", value: "#d4418a" },
    { color: "gray", value: "#606773" },
    { color: "lead", value: "#3f3f4e" },
  ]}
/>

#### Outline Offset

<LegacyClass classPrefix="oo-" propNames={["outline-offset"]} incrementValue={4} range={4} unit="px" />

#### Outline Style

<LegacyClass
  data={[
    {
      className: "os-none",
      properties: ["outline-style: none;"],
    },
    {
      className: "os-d",
      properties: ["outline-style: dashed;"],
    },
    {
      className: "os-h",
      properties: ["outline-style: hidden;"],
    },
    {
      className: "os-s",
      properties: ["outline-style: solid;"],
    },
  ]}
/>

#### Outline Width

<LegacyClass classPrefix="ow-" propNames={["outline-width"]} incrementValue={4} range={4} unit="px" />

### Spacing

We're also adding new Spacing utilities. These utilities work a bit differently, in that they use a unique syntax. This means you don't need to specify `margin` or `padding` if they have the same value.

#### Spacing X

<LegacyClass classPrefix="s-x-" propNames={["margin-left", "margin-right"]} range={100} incrementValue={0.25} unit="rem" />

#### Spacing Y

<LegacyClass classPrefix="s-y-" propNames={["margin-top", "margin-bottom"]} range={100} incrementValue={0.25} unit="rem" />

I have to mention the new table utilities and the Table Layout utility classes.

<LegacyClass
  data={[
    {
      className: "tl-auto",
      properties: ["table-layout: auto;"],
    },
    {
      className: "tl-f",
      properties: ["table-layout: fixed;"],
    },
  ]}
/>

We're also adding a bunch of new utility classes for text formatting to the table. These include things like Text Decoration Color, Text Decoration Style, and Text Decoration Thickness.

### Text Decoration Color

<LegacyColor
  classPrefix="tdc-"
  propNames={["outline-color"]}
  data={[
    { color: "red", value: "#d73d3d" },
    { color: "orange", value: "#e06814" },
    { color: "yellow", value: "#d3a107" },
    { color: "green", value: "#1fb155" },
    { color: "teal", value: "#12a695" },
    { color: "cyan", value: "#05a4bf" },
    { color: "blue", value: "#3575dd" },
    { color: "indigo", value: "#595cd9" },
    { color: "violet", value: "#7d53dd" },
    { color: "pink", value: "#d4418a" },
    { color: "gray", value: "#606773" },
    { color: "lead", value: "#3f3f4e" },
  ]}
/>

### Text Decoration Style

<LegacyClass
  data={[
    {
      className: "tds-none",
      properties: ["text-decoration-style: none;"],
    },
    {
      className: "tds-d",
      properties: ["text-decoration-style: dashed;"],
    },
    {
      className: "tds-h",
      properties: ["text-decoration-style: hidden;"],
    },
    {
      className: "tds-s",
      properties: ["text-decoration-style: solid;"],
    },
  ]}
/>

### Text Decoration Thickness

<LegacyClass
  data={[
    {
      className: "tdt-auto",
      properties: ["text-decoration-thickness: auto;"],
    },
    {
      className: "tdt-ff",
      properties: ["text-decoration-thickness: from-font;"],
    },
    {
      className: "tdt-0",
      properties: ["text-decoration-thickness: 0px;"],
    },
    {
      className: "tdt-1",
      properties: ["text-decoration-thickness: 4px;"],
    },
    {
      className: "tdt-2",
      properties: ["text-decoration-thickness: 8px;"],
    },
    {
      className: "tdt-3",
      properties: ["text-decoration-thickness: 12px;"],
    },
    {
      className: "tdt-4",
      properties: ["text-decoration-thickness: 16px;"],
    },
  ]}
/>

## Base styles

You can now use Yumma CSS without the default base styles. This could be really useful if you want to set specific rules in your theme.

#### Standard Core

```html
<link href="https://cdn.jsdelivr.net/gh/yumma-lib/yumma-css@2.0.0/dist/yumma-core.css" rel="stylesheet" crossorigin="anonymous" />
```

#### Minified Core

```html
<link href="https://cdn.jsdelivr.net/gh/yumma-lib/yumma-css@2.0.0/dist/yumma-core.min.css" rel="stylesheet" crossorigin="anonymous" />
```

## Responsive breakpoints

It took us a while, but We've finally added full media query support for every single utility class.

```html "sm:gtc-2" "md:gtc-3" "lg:gtc-4"
<div class="cnt mx-auto p-4">
  <div class="d-g gtc-1 sm:gtc-2 md:gtc-3 lg:gtc-4 g-4">
    <div class="...">...</div>
    <div class="...">...</div>
    <div class="...">...</div>
  </div>
</div>
```

Yumma CSS is going to change how your browser handles breakpoints.

<Tabs>
  <TabItem icon="approve-check-circle" label="Now">
    <LegacyClass
      data={[
        { className: "sm", properties: ["min-width: 640px;"] },
        { className: "md", properties: ["min-width: 768px;"] },
        { className: "lg", properties: ["min-width: 1024px;"] },
        { className: "xl", properties: ["min-width: 1280px;"] },
        { className: "xxl", properties: ["min-width: 1536px;"] },
      ]}
    />
  </TabItem>
  <TabItem icon="close" label="Previously">
    <LegacyClass
      data={[
        { className: "xs", properties: ["min-width: 0;"] },
        { className: "sm", properties: ["min-width: 480px;"] },
        { className: "md", properties: ["min-width: 720px;"] },
        { className: "lg", properties: ["min-width: 960px;"] },
        { className: "xl", properties: ["min-width: 1200px;"] },
      ]}
    />
  </TabItem>
</Tabs>

## Container rules

The container utility has a fresh new look in this update. We've given it a new class name, `cnt`, and added a set of media query rules to make sure it responds well to different themes.

<Tabs>
  <TabItem icon="approve-check-circle" label="Now">
    ```scss
    .cnt {
      width: 100%;
      @each $bp, $bp_value in $yma-breakpoints {
        @include breakpoint($bp_value) {
          max-width: $bp_value;
        }
      }
    }
    ```
  </TabItem>
  <TabItem icon="close" label="Previously">
    ```scss
    .cnn {
      width: 100%;
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
      box-sizing: border-box;
    }
    ```
  </TabItem>
</Tabs>

---

## Upgrade

You can upgrade your projects by getting the latest version of `yummacss` from npm:

<PackageManagers pkgManagers={["pnpm", "npm", "yarn"]} pkg="yummacss@latest" title="Terminal" />
