---
title: Object Fit
banner:
  content: N/A
description: Controls how to resize the content of a replaced element.
slug: docs/object-fit
---

<Class category="positioning" name="object-fit" />

## Cover

This example sets the object fit to **cover**. The content will scale to cover the entire container while maintaining its aspect ratio, potentially cropping the content.

```html live "of-c"
<div class="h-40 w-80" id="area">
  <img class="d-full of-c rad-1" src="https://picsum.photos/400/300?image=10" />
</div>
```

## Fill

> Initial value

This example sets the object fit to **fill**. The content will stretch to fill the entire container, disregarding its aspect ratio.

```html live "of-f"
<div class="h-40 w-80" id="area">
  <img class="d-full of-f rad-1" src="https://picsum.photos/400/300?image=10" />
</div>
```

## None

This example sets the object fit to **none**. The content will not be resized to fit the container, and it will retain its original size.

```html live "of-none"
<div class="h-40 w-80" id="area">
  <img class="d-full of-none rad-1" src="https://picsum.photos/400/300?image=10" />
</div>
```

## Scale Down

This example sets the object fit to **scale-down**. The content will be sized as if `none` or `contain`, whichever results in a smaller size.

```html live "of-sd"
<div class="h-40 w-80" id="area">
  <img class="d-full of-sd" src="https://picsum.photos/400/300?image=10" />
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<MediaModifier class="of-c" mediaModifier="of-sd" classPrefix="of">
  ### Breakpoint modifier
</MediaModifier>

<HoverModifier class="of-c" hoverModifier="of-sd" classPrefix="of">
  ### Hover modifier
</HoverModifier>
