---
authors: Renildo
cover:
  alt: Yumma CSS 0.1
  image: cover.png
date: 2023-11-06
description: Nothing beats a good update. This one's not as abundant, but there are some pretty handy improvements — [Yumma CSS 0.1.0](https://github.com/yumma-lib/yumma-css/releases/tag/v0.1.0)!
pagefind: false
slug: blog/yummacss-0.1
tags: ["release"]
title: Yumma CSS 0.1
---

Nothing beats a good update. This one's not as abundant, but there are some pretty handy improvements — [Yumma CSS 0.1.0](https://github.com/yumma-lib/yumma-css/releases/tag/v0.1.0)!

{/* excerpt */}

You may also want to take a look at some of the [release notes](https://github.com/yumma-lib/yumma-css/releases/tag/v0.1.0) but, anyway, these are the most noticeable shifts:

- [Box Model utilities](#box-model-utilities): Additions and improvements for Height, Margin, Padding, and Width utility classes.

This is an incremental update that may contain bug fixes. Minor releases follow [semantic versioning](https://docs.npmjs.com/about-semantic-versioning) conventions. In other words, this should be an easy update for you.

---

## Box Model utilities

Previous versions of Yumma CSS had some inconsistencies with the Height, Margin, Padding, and Width utilities. Version 0.1.0 addresses these inconsistencies and also fixes unexpected results and makes the range linear, starting from 0-100.

<Tabs>
  <TabItem label="Height">
    The scale ranges from 0 to 100, with a base value of `0.25rem`.
  
    <LegacyClass
      classPrefix="h-"
      propNames={["height"]}
      range={100}
      incrementValue={0.25}
      unit="rem"
    />
  </TabItem>

<TabItem label="Padding">
  The scale ranges from 0 to 100, with a base value of `0.25rem`.

  <LegacyClass
    classPrefix="p-"
    propNames={["padding"]}
    range={100}
    incrementValue={0.75}
    unit="rem"
  />
</TabItem>

{" "}

<TabItem label="Margin">
  The scale ranges from 0 to 100, with a base value of `0.25rem`.

  <LegacyClass
    classPrefix="m-"
    propNames={["margin"]}
    range={100}
    incrementValue={0.75}
    unit="rem"
  />
</TabItem>

  <TabItem label="Width">
    The scale ranges from 0 to 100, with a base value of `0.25rem`.

    <LegacyClass
      classPrefix="w-"
      propNames={["width"]}
      range={100}
      incrementValue={0.25}
      unit="rem"
    />

  </TabItem>
</Tabs>

---

## Upgrade

You can upgrade your projects by getting the latest version of `yummacss` from npm:

<PackageManagers pkgManagers={["pnpm", "npm", "yarn"]} pkg="yummacss@latest" title="Terminal" />
