---
title: Justify Self
banner:
  content: N/A
description: Controls the alignment of a single grid element relative to its inline axis.
slug: docs/justify-self
---

<Class category="flexbox" name="justify-self" />

## Auto

> Initial value

This example sets the justify self to **auto**. The item will use the default alignment based on the container's alignment properties.

```html live "js-auto" layout="/src/layouts/inline.astro"
<div class="d-g g-4 gtc-3 tc-white" id="area">
  <div class="ai-c bg-indigo-8 d-f d-14 jc-c rad-1 tc-indigo-5">A</div>
  <div class="ai-c bg-indigo d-f d-14 jc-c js-auto rad-1 tc-white">B</div>
  <div class="ai-c bg-indigo-8 d-f d-14 jc-c rad-1 tc-indigo-5">C</div>
</div>
```

## Center

This example sets the justify self to **center**. The item will be centered within its grid area along the inline axis.

```html live "js-c" layout="/src/layouts/inline.astro"
<div class="d-g g-4 gtc-3 tc-white" id="area">
  <div class="ai-c bg-indigo-8 d-f d-14 jc-c rad-1 tc-indigo-5">A</div>
  <div class="ai-c bg-indigo d-f d-14 jc-c js-c rad-1 tc-white">B</div>
  <div class="ai-c bg-indigo-8 d-f d-14 jc-c rad-1 tc-indigo-5">C</div>
</div>
```

## End

This example sets the justify self to **end**. The item will be aligned to the end of its grid area along the inline axis.

```html live "js-e" layout="/src/layouts/inline.astro"
<div class="d-g g-4 gtc-3 tc-white" id="area">
  <div class="ai-c bg-indigo-8 d-f d-14 jc-c rad-1 tc-indigo-5">A</div>
  <div class="ai-c bg-indigo d-f d-14 jc-c js-e rad-1 tc-white">B</div>
  <div class="ai-c bg-indigo-8 d-f d-14 jc-c rad-1 tc-indigo-5">C</div>
</div
```

## Start

This example sets the justify self to **start**. The item will be aligned to the start of its grid area along the inline axis.

```html live "js-s" layout="/src/layouts/inline.astro"
<div class="d-g g-4 gtc-3 tc-white" id="area">
  <div class="ai-c bg-indigo-8 d-f d-14 jc-c rad-1 tc-indigo-5">A</div>
  <div class="ai-c bg-indigo d-f d-14 jc-c js-s rad-1 tc-white">B</div>
  <div class="ai-c bg-indigo-8 d-f d-14 jc-c rad-1 tc-indigo-5">C</div>
</div>
```

## Stretch

This example sets the justify self to **stretch**. The item will stretch to fill its grid area along the inline axis.

```html live "js-st" layout="/src/layouts/inline.astro"
<div class="d-g g-4 gtc-3 tc-white" id="area">
  <div class="ai-c bg-indigo-8 d-f d-14 jc-c rad-1 tc-indigo-5">A</div>
  <div class="ai-c bg-indigo d-f d-14 jc-c js-st rad-1 tc-white">B</div>
  <div class="ai-c bg-indigo-8 d-f d-14 jc-c rad-1 tc-indigo-5">C</div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<MediaModifier class="js-s" mediaModifier="js-e" classPrefix="js">
  ### Media modifier
</MediaModifier>

<HoverModifier class="js-s" hoverModifier="js-e" classPrefix="js">
  ### Hover modifier
</HoverModifier>
