---
title: <PERSON><PERSON> Padding
banner:
  content: N/A
description: Controls all of the scroll paddings of an element simultaneously.
slug: docs/scroll-padding
---

<Class category="interactivity" name="scroll-padding" />

This example sets the scroll padding to **1.5rem**. The element will have a uniform padding of **1.5rem** around it when scrolling, ensuring that the content is positioned away from the edges of the viewport.

<Note icon="cursor_arrow">Try scrolling through the image container to see how it behaves.</Note>

```html live "sp-6" layout="/src/layouts/inline.astro"
<div class="o-h p-r">
  <div class="d-f g-6 o-x-s p-6 sst-x-m">
    <div class="fs-0 p-6 p-r sp-6 ssa-s">
      <div class="i-0 p-a w-full zi-0">
        <div class="d-full" id="area"></div>
      </div>
      <img class="h-40 p-r rad-1 w-80 zi-10" src="https://picsum.photos/400/200?image=849" />
    </div>
    <div class="fs-0 p-6 p-r sp-6 ssa-s">
      <div class="i-0 p-a w-full zi-0">
        <div class="d-full" id="area"></div>
      </div>
      <img class="h-40 p-r rad-1 w-80 zi-10" src="https://picsum.photos/400/200?image=866" />
    </div>
    <div class="fs-0 p-6 p-r sp-6 ssa-s">
      <div class="i-0 p-a w-full zi-0">
        <div class="d-full" id="area"></div>
      </div>
      <img class="h-40 p-r rad-1 w-80 zi-10" src="https://picsum.photos/400/200?image=872" />
    </div>
    <div class="fs-0 p-6 p-r sp-6 ssa-s">
      <div class="i-0 p-a w-full zi-0">
        <div class="d-full" id="area"></div>
      </div>
      <img class="h-40 p-r rad-1 w-80 zi-10" src="https://picsum.photos/400/200?image=875" />
    </div>
  </div>
</div>
```

## Scroll Padding Bottom

Sets the padding on the bottom side of an element, creating space between the element and its scrollable container.

<Class category="interactivity" name="scroll-padding-bottom" />

This example sets the scroll padding bottom to **1.5rem**. The element will have a padding of **1.5rem** below it when scrolling, ensuring that the content is positioned away from the bottom edge of the viewport.

<Note icon="cursor_arrow">Try scrolling through the image container to see how it behaves.</Note>

```html live "spb-6"
<div class="o-h p-r w-fc">
  <div class="d-f fd-c g-6 h-96 o-y-auto p-6 sst-y-m">
    <div class="fs-0 p-r pb-6 spb-6 ssa-s">
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=849" />
      <div class="h-6 l-0 l-0 p-a r-0" id="area"></div>
    </div>
    <div class="fs-0 p-r pb-6 spb-6 ssa-s">
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=866" />
      <div class="h-6 l-0 l-0 p-a r-0" id="area"></div>
    </div>
    <div class="fs-0 p-r pb-6 spb-6 ssa-s">
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=872" />
      <div class="h-6 l-0 l-0 p-a r-0" id="area"></div>
    </div>
    <div class="fs-0 p-r pb-6 spb-6 ssa-s">
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=875" />
      <div class="h-6 l-0 l-0 p-a r-0" id="area"></div>
    </div>
  </div>
</div>
```

## Scroll Padding Left

Sets the padding on the left side of an element, creating space between the element and its scrollable container.

<Class category="interactivity" name="scroll-padding-left" />

This example sets the scroll padding left to **1.5rem**. The element will have a padding of **1.5rem** to its left when scrolling, ensuring that the content is positioned away from the left edge of the viewport.

<Note icon="cursor_arrow">Try scrolling through the image container to see how it behaves.</Note>

```html live "spl-6" layout="/src/layouts/inline.astro"
<div class="o-h p-r">
  <div class="d-f g-6 o-x-s p-6 sst-x-m">
    <div class="fs-0 p-r pl-6 spl-6 ssa-s">
      <div class="bo-0 l-0 p-a  id="area"t-0 w-6"></div>
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=849" />
    </div>
    <div class="fs-0 p-r pl-6 spl-6 ssa-s">
      <div class="bo-0 l-0 p-a  id="area"t-0 w-6"></div>
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=866" />
    </div>
    <div class="fs-0 p-r pl-6 spl-6 ssa-s">
      <div class="bo-0 l-0 p-a  id="area"t-0 w-6"></div>
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=872" />
    </div>
    <div class="fs-0 p-r pl-6 spl-6 ssa-s">
      <div class="bo-0 l-0 p-a  id="area"t-0 w-6"></div>
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=875" />
    </div>
  </div>
</div>
```

## Scroll Padding Right

Sets the padding on the right side of an element, creating space between the element and its scrollable container.

<Class category="interactivity" name="scroll-padding-right" />

This example sets the scroll padding right to **1.5rem**. The element will have a padding of **1.5rem** to its right when scrolling, ensuring that the content is positioned away from the right edge of the viewport.

<Note icon="cursor_arrow">Try scrolling through the image container to see how it behaves.</Note>

```html live "spr-6" layout="/src/layouts/inline.astro"
<div class="o-h p-r">
  <div class="d-f g-6 o-x-s p-6 sst-x-m">
    <div class="fs-0 p-r pr-6 spr-6 ssa-s">
      <div class="bo-0 p-a r-0  id="area"t-0 w-6"></div>
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=849" />
    </div>
    <div class="fs-0 p-r pr-6 spr-6 ssa-s">
      <div class="bo-0 p-a r-0  id="area"t-0 w-6"></div>
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=866" />
    </div>
    <div class="fs-0 p-r pr-6 spr-6 ssa-s">
      <div class="bo-0 p-a r-0  id="area"t-0 w-6"></div>
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=872" />
    </div>
    <div class="fs-0 p-r pr-6 spr-6 ssa-s">
      <div class="bo-0 p-a r-0  id="area"t-0 w-6"></div>
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=875" />
    </div>
  </div>
</div>
```

## Scroll Padding Top

Sets the padding on the top side of an element, creating space between the element and its scrollable container.

<Class category="interactivity" name="scroll-padding-top" />

This example sets the scroll padding top to **1.5rem**. The element will have a padding of **1.5rem** above it when scrolling, ensuring that the content is positioned away from the top edge of the viewport.

<Note icon="cursor_arrow">Try scrolling through the image container to see how it behaves.</Note>

```html live "spt-6"
<div class="o-h p-r w-fc">
  <div class="d-f fd-c g-6 h-96 o-y-auto p-6 sst-y-m">
    <div class="fs-0 p-r pt-6 spt-6 ssa-s">
      <div class="h-6 l-0 p-a r-0  id="area"t-0"></div>
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=849" />
    </div>
    <div class="fs-0 p-r pt-6 spt-6 ssa-s">
      <div class="h-6 l-0 p-a r-0  id="area"t-0"></div>
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=866" />
    </div>
    <div class="fs-0 p-r pt-6 spt-6 ssa-s">
      <div class="h-6 l-0 p-a r-0  id="area"t-0"></div>
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=872" />
    </div>
    <div class="fs-0 p-r pt-6 spt-6 ssa-s">
      <div class="h-6 l-0 p-a r-0  id="area"t-0"></div>
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=875" />
    </div>
  </div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/foundations/responsive-design/) or other factors, such as [hover states](/docs/foundations/variants/).

<BreakpointVariant class="sp-1" variant="sp-2" classPrefix="sp">
  ### Breakpoint variant
</BreakpointVariant>

<HoverVariant class="sp-1" variant="sp-2" classPrefix="sp">
  ### Hover variant
</HoverVariant>
