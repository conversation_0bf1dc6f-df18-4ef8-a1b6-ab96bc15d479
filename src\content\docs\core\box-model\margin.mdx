---
title: Margin
banner:
  content: N/A
description: Controls the margin of an element.
slug: docs/margin
---

<Class category="boxModel" name="margin" />

This example sets the margin to **2rem**. The element will have a uniform margin around it.

```html live "m-8"
<div class="d-f fd-r" id="area">
  <div class="ai-c bg-indigo d-f d-16 jc-c m-8 rad-1 tc-white">8</div>
</div>
```

## Margin Bottom

Controls the bottom margin of an element.

<Class category="boxModel" name="margin-bottom" />

This example sets the bottom margin to **2rem**. The element will have space below it.

```html live "mb-8"
<div class="d-f fd-r" id="area">
  <div class="ai-c bg-indigo d-f d-16 jc-c mb-8 rad-1 t-8 tc-white">8</div>
</div>
```

## Margin Left

Controls the left margin of an element.

<Class category="boxModel" name="margin-left" />

This example sets the left margin to **2rem**. The element will have space to its left.

```html live "ml-8"
<div id="area">
  <div class="ai-c bg-indigo d-f d-16 jc-c ml-8 rad-1 tc-white">8</div>
</div>
```

## Margin Right

Controls the right margin of an element.

<Class category="boxModel" name="margin-right" />

This example sets the right margin to **2rem**. The element will have space to its right.

```html live "mr-8"
<div id="area">
  <div class="ai-c bg-indigo d-f d-16 jc-c mr-8 rad-1 tc-white">8</div>
</div>
```

## Margin Top

Controls the top margin of an element.

<Class category="boxModel" name="margin-top" />

This example sets the top margin to **2rem**. The element will have space above it.

```html live "mt-8"
<div class="d-f fd-r" id="area">
  <div class="ai-c bg-indigo d-f d-16 jc-c mt-8 rad-1 t-8 tc-white">8</div>
</div>
```

## Margin Block End

Controls the logical block end margin of an element, which maps to a physical margin depending on the element's writing mode, directionality, and text orientation.

<Class category="boxModel" name="margin-block-end" />

This example sets the margin block end to **2rem**. The element will have space below it.

```html live "mbe-8"
<div class="d-f fd-r" id="area">
  <div class="ai-c bg-indigo d-f d-16 jc-c mbe-8 rad-1 t-8 tc-white">8</div>
</div>
```

## Margin Block Start

Controls the logical block start margin of an element, which maps to a physical margin depending on the element's writing mode, directionality, and text orientation.

<Class category="boxModel" name="margin-block-start" />

This example sets the margin block start to **2rem**. The element will have space above it.

```html live "mbs-8"
<div class="d-f fd-r" id="area">
  <div class="ai-c bg-indigo d-f d-16 jc-c mbs-8 rad-1 t-8 tc-white">8</div>
</div>
```

## Margin Inline End

Controls the logical inline end margin of an element, which maps to a physical margin depending on the element's writing mode, directionality, and text orientation.

<Class category="boxModel" name="margin-inline-end" />

This example sets the margin inline end to **2rem**. The element will have space to its right.

```html live "mie-8"
<div id="area">
  <div class="ai-c bg-indigo d-f d-16 jc-c mie-8 rad-1 tc-white">8</div>
</div>
```

## Margin Inline Start

Controls the logical inline start margin of an element, which maps to a physical margin depending on the element's writing mode, directionality, and text orientation.

<Class category="boxModel" name="margin-inline-start" />

This example sets the margin inline start to **2rem**. The element will have space to its left.

```html live "mis-8"
<div id="area">
  <div class="ai-c bg-indigo d-f d-16 jc-c mis-8 rad-1 tc-white">8</div>
</div>
```

## Margin X

Controls the left and right margins of an element at once.

<Class category="boxModel" name="margin-x" />

This example sets the left and right margins to **2rem**. The element will have uniform space on both sides.

```html live "mx-8"
<div id="area">
  <div class="ai-c bg-indigo d-f d-16 jc-c mx-8 rad-1 tc-white">8</div>
</div>
```

## Margin Y

Controls the bottom and top margins of an element at once.

<Class category="boxModel" name="margin-y" />

This example sets the bottom and top margins to **2rem**. The element will have uniform space above and below it.

```html live "my-8"
<div class="d-f fd-r" id="area">
  <div class="ai-c bg-indigo d-f d-16 jc-c my-8 rad-1 tc-white">8</div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<MediaModifier class="m-1" mediaModifier="m-2" classPrefix="m">
  ### Media modifier
</MediaModifier>

<HoverModifier class="m-1" hoverModifier="m-2" classPrefix="m">
  ### Hover modifier
</HoverModifier>
