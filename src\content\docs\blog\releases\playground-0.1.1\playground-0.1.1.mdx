---
authors: Ren<PERSON><PERSON>
date: 2025-07-29
description: We've got another update for you today on the Yumma CSS Playground! This is a minor update, but it adds a lot of new cool features to the playground site.
pagefind: false
slug: blog/playground-0.1.1
title: Playground 0.1.1
---

We've got another update on the Yumma CSS Playground. It's a small update, but it's got a lot of cool new features that we're excited to show you.

{/* excerpt */}

<iframe
  allowfullscreen
  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
  class="ar-16/9 rad-1 w-full"
  frameborder="0"
  referrerpolicy="strict-origin-when-cross-origin"
  src="https://youtube.com/embed/xUQ3DW_J_3U?si=AwWogjY861eJCqlt"
  title="What's new in Yumma CSS Playground 0.1.1?"></iframe>

You may also want to take a look at some of the [release notes](https://github.com/yumma-lib/yumma-css-play/releases/tag/v0.1.1) but, anyway, these are the most noticeable shifts:

- [New Code Editor](#monaco-editor-migration): Switched from CodeMirror to Monaco Editor
- [Command palette](#command-palette): New VS Code-style command palette with F1
- [Emmet support](#emmet-support): Full HTML emmet abbreviation support
- [Format document](#format-document): New code formatting options

This is an incremental update that may contain bug fixes. Minor releases follow [semantic versioning](https://docs.npmjs.com/about-semantic-versioning) conventions. In other words, this should be an easy update for you.

---

## Monaco Editor Migration

For a while, our code editor was based on [CodeMirror 6](https://codemirror.net/), but we switched to [Monaco Editor](https://www.npmjs.com/package/@monaco-editor/react) when we realized it didn't have the features and functionality we needed. Some of you might have heard of Monaco, but for those who haven't, it's an incredibly powerful code editor that powers many apps, including VS Code, Cursor, Windsurf, Trae, and Kiro.

Now you can do the same things and use the same shortcuts that come with VS Code in Yumma CSS Playground. This migration brings a much more familiar and powerful editing experience to the playground.

## Command Palette

To give you an idea of the new capabilities, if you press **F1**, the Command Palette will pop up with your typical quick actions and functions found in VS Code. This makes it super easy to access various features and commands without having to remember specific shortcuts or navigate through menus.

The command palette includes all the essential VS Code-style commands you'd expect, making the playground feel just like working in your favorite code editor.

## Emmet Support

Enjoyike using emmet shortcuts to speed up your workflow, HTML emmets are now fully supported in our code editor. And guess what? It works just like VS Code.

You can now use all your favorite emmet abbreviations to quickly generate HTML structures, making it much faster to prototype and experiment with Yumma CSS utilities.

## Format Document

We've also added a super convenient way to format your code. If you right-click in the editor panel, you'll see a menu. Just click "Format Document" and it will format your whole code. Pretty cool, huh?

Now, if you're not a fan of right-clicking, we've got a shortcut for that. Simply press **Ctrl + S**, and it'll format just like before, but without the mouse. This makes it incredibly easy to keep your code clean and properly formatted while you're experimenting.

---

These improvements make the Yumma CSS Playground much more powerful and user-friendly. The Monaco Editor migration especially brings a professional-grade editing experience that should feel familiar to anyone who uses VS Code or similar editors.