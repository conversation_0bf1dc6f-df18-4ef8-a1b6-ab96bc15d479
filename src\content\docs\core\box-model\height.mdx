---
title: Height
banner:
  content: N/A
description: Controls the height of an element.
slug: docs/height
---

<Class category="boxModel" name="height" />

This example showcases various `height` utilities:

- The **h-16** height utility sets the `height` to **4rem**.
- The **h-28** height utility sets the `height` to **7rem**.
- Finally, the **h-48** height utility sets the `height` to **12rem**.

```html live "h-16" "h-28" "h-48"
<div class="d-f cg-8">
  <div class="ai-c bg-indigo d-f h-16 p-3 rad-1 tc-white">16</div>
  <div class="ai-c bg-indigo d-f h-28 p-3 rad-1 tc-white">28</div>
  <div class="ai-c bg-indigo d-f h-48 p-3 rad-1 tc-white">48</div>
</div>
```

## Max Height

Controls the maximum height of an element.

<Class category="boxModel" name="max-height" />

This example sets the maximum height of an element. The **max-h-64** utility sets the maximum height to **16rem**, ensuring that the element does not exceed this height regardless of the content inside.

<Note icon="size">Try changing the vertical size of the box to see how wide it can be at the maximum size.</Note>

```html live "max-h-64"
<div class="ai-c bg-indigo d-f jc-c max-h-64 min-h-16 o-auto p-6 r-v rad-1 tc-white h-32">64</div>
```

## Min Height

Controls the minimum height of an element.

<Class category="boxModel" name="min-height" />

This example sets the minimum height of an element. The **min-h-32** utility sets the minimum height to **8rem**, ensuring that the element maintains at least this height even when the content is smaller.

<Note icon="size">Try changing the vertical size of the box to see how wide it can be at the maximum size.</Note>

```html live "max-h-64"
<div class="ai-c bg-indigo d-f jc-c max-h-64 min-h-16 o-auto p-6 r-v rad-1 tc-white h-32">32</div>
```

## Using Percentages

This example showcases various height values: **100%** and **50%**.

- The **h-full** utility sets the height to **100%**, allowing the element to take up the full height of its container.
- The **h-half** utility sets the height to **50%**, allowing the element to occupy half the height of its container.

```html live "h-full" "h-half"
<div class="b-3 b-d bc-indigo d-f h-64 p-2 rad-1 cg-8">
  <div class="ai-c bg-indigo d-f h-full p-2 rad-1 tc-white">100%</div>
  <div class="ai-c bg-indigo d-f h-half p-2 rad-1 tc-white">50%</div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<MediaModifier class="h-1" mediaModifier="h-2" classPrefix="h">
  ### Media modifier
</MediaModifier>

<HoverModifier class="h-1" hoverModifier="h-2" classPrefix="h">
  ### Hover modifier
</HoverModifier>
