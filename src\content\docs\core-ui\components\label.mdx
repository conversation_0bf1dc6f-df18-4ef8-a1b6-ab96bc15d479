---
title: Label
description: A form element that provides descriptive text for form controls and improves accessibility.
slug: docs/ui/label
draft: true
---

## Examples

A basic implementation of the Label component with default styling.

```tsx showLineNumbers
import { Label } from "yumma-ui";

const App = () => {
  return (
    <Label variant="base" size="sm" htmlFor="email">
      Email
    </Label>
  );
};

export default App;
```

## Usage

```tsx showLineNumbers
import { Label } from "yumma-ui";
```

```tsx showLineNumbers
<Label>Email</Label>
```

## API reference

The Label component can be adapted to suit specific requirements by utilizing the internal API of Yumma UI, thereby enabling the modification of its visual appearance and operational characteristics.

### `variant`

> Default value is `base`

The `variant` property allows you to choose from different pre-defined styles that match your design system.

```ts
(property) variant?: "base" | "error" | "disabled" | null | undefined
```

### `size`

> Default value is `sm`

The `size` property allows you to choose from different pre-defined sizes that match your design system.

```ts
(property) size?: "sm" | "md" | "lg" | null | undefined
```

## Custom styling

The Label component exposes a simple API to control its look and behavior through props.

```tsx showLineNumbers 'className="..."'
import { Label } from "yumma-ui";

<Label className="...">Email</Label>;
```

## Extend properties

The Label component is fully extendable. It supports all native HTML `<label>` attributes through its props:

```tsx showLineNumbers 'htmlFor="..."'
import { Label } from "yumma-ui";

const App = () => {
  return (
    <Label htmlFor="..." {...props}>
      Email
    </Label>
  );
};

export default App;
```
