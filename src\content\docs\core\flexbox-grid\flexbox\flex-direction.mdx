---
title: Flex Direction
banner:
  content: N/A
description: Controls the direction of flex elements.
slug: docs/flex-direction
---

<Class category="flexbox" name="flex-direction" />

## Column

This example sets the flex direction to **column**. The items will be arranged vertically in a single column.

```html live "fd-c" layout="/src/layouts/inline.astro"
<div class="d-f fd-c rg-4 tc-white">
  <div class="bg-indigo ta-c p-4 rad-1">A</div>
  <div class="bg-indigo ta-c p-4 rad-1">B</div>
  <div class="bg-indigo ta-c p-4 rad-1">C</div>
</div>
```

## Column Reverse

This example sets the flex direction to **column-reverse**. The items will be arranged vertically in a single column, but in reverse order.

```html live "fd-cr" layout="/src/layouts/inline.astro"
<div class="d-f fd-cr rg-4 tc-white">
  <div class="bg-indigo ta-c p-4 rad-1">A</div>
  <div class="bg-indigo ta-c p-4 rad-1">B</div>
  <div class="bg-indigo ta-c p-4 rad-1">C</div>
</div>
```

## Row

> Initial value

This example sets the flex direction to **row**. The items will be arranged horizontally in a single row.

```html live "fd-r" layout="/src/layouts/inline.astro"
<div class="d-f fd-r cg-4 tc-white">
  <div class="d-16 bg-indigo ta-c p-4 rad-1">A</div>
  <div class="d-16 bg-indigo ta-c p-4 rad-1">B</div>
  <div class="d-16 bg-indigo ta-c p-4 rad-1">C</div>
</div>
```

## Row Reverse

This example sets the flex direction to **row-reverse**. The items will be arranged horizontally in a single row, but in reverse order.

```html live "fd-rr" layout="/src/layouts/inline.astro"
<div class="d-f fd-rr cg-4 tc-white">
  <div class="d-16 bg-indigo ta-c p-4 rad-1">A</div>
  <div class="d-16 bg-indigo ta-c p-4 rad-1">B</div>
  <div class="d-16 bg-indigo ta-c p-4 rad-1">C</div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<MediaModifier class="fd-c" mediaModifier="fd-r" classPrefix="fd">
  ### Media modifier
</MediaModifier>

<HoverModifier class="fd-c" hoverModifier="fd-r" classPrefix="fd">
  ### Hover modifier
</HoverModifier>
