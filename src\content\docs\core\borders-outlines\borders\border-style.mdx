---
title: Border Style
banner:
  content: N/A
description: Controls the style of the borders of an element.
slug: docs/border-style
---

<Class category="border" name="border-style" />

This example showcases various `border-style` utilities:

- The **dashed** utility creates a border with dashed lines.
- The **none** utility removes the border entirely.
- The **solid** utility creates a continuous border line.

```html live "b-none" "b-d" "b-s"
<div class="d-g g-16 gtc-1 sm:gtc-3">
  <div class="ai-c b-2 b-none bc-indigo d-f d-16 jc-c tc-indigo">A</div>
  <div class="ai-c b-2 b-d bc-indigo d-f d-16 jc-c tc-indigo">B</div>
  <div class="ai-c b-2 b-s bc-indigo d-f d-16 jc-c tc-indigo">C</div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<MediaModifier class="b-h" mediaModifier="b-s" classPrefix="b">
  ### Breakpoint modifier
</MediaModifier>

<HoverModifier class="b-h" hoverModifier="b-s" classPrefix="b">
  ### Hover modifier
</HoverModifier>
