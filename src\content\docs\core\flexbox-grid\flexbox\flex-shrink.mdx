---
title: Flex Shrink
banner:
  content: N/A
description: Controls the shrinkage of the flex items.
slug: docs/flex-shrink
---

<Class category="flexbox" name="flex-shrink" />

This example sets the flex-shrink property to **1**. The **fs-1** utility allows the item to shrink if necessary, proportionally to other items with the same property, when there is not enough space in the container.

```html live "fs-0" "fs-1" "fs-2" layout="/src/layouts/inline.astro"
<div class="d-f g-4 rad-1 tc-white" id="area">
  <div class="ai-c bg-indigo-8 d-f d-14 f-none jc-c p-4 rad-1 tc-indigo-5">A</div>
  <div class="ai-c bg-indigo d-f fs-1 h-14 jc-c p-4 rad-1 w-64 tc-white">B</div>
  <div class="ai-c bg-indigo-8 d-f d-14 f-none jc-c p-4 rad-1 tc-indigo-5">C</div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/foundations/responsive-design/) or other factors, such as [hover states](/docs/foundations/variants/).

<BreakpointVariant class="fs-1" variant="fs-2" classPrefix="fs">
  ### Breakpoint variant
</BreakpointVariant>

<HoverVariant class="fs-1" variant="fs-2" classPrefix="fs">
  ### Hover variant
</HoverVariant>
