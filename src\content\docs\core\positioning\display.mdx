---
title: Display
banner:
  content: N/A
description: Controls the display box type of an element.
slug: docs/display
---

<Class category="positioning" name="display" />

## Block

This example sets the display to **block**. The **d-b** utility makes the element a block-level element, causing it to take up the full width available and start on a new line.

```html live "d-b" layout="/src/layouts/inline.astro"
<div class="d-b ta-c tc-white">
  <div class="bg-indigo mt-4 p-4 rad-1">A</div>
  <div class="bg-indigo mt-4 p-4 rad-1">B</div>
  <div class="bg-indigo mt-4 p-4 rad-1">C</div>
</div>
```

## Flex

This example sets the display to **flex**. The **d-f** utility makes the element a flex container, enabling the use of flexbox layout properties for its children.

```html live "d-f" layout="/src/layouts/inline.astro"
<div class="d-f g-4 ta-c tc-white">
  <div class="ai-c bg-indigo d-b d-g d-14 jc-c p-4 rad-1">A</div>
  <div class="ai-c bg-indigo d-b d-g d-14 jc-c p-4 rad-1">B</div>
  <div class="ai-c bg-indigo d-b d-g d-14 jc-c p-4 rad-1">C</div>
</div>
```

## Flow Root

This example sets the display to **flow-root**. The **d-fr** utility creates a new block formatting context, allowing for better control of floated children.

```html live "d-fr" layout="/src/layouts/inline.astro"
<div class="d-fr ta-c tc-white">
  <div class="ai-c bg-indigo d-f d-18 flo-l jc-c p-a rad-1 zi-10">A</div>
  <div class="h-18"></div>
</div>
```

## Grid

This example sets the display to **grid**. The **d-g** utility makes the element a grid container, enabling the use of grid layout properties for its children.

```html live "d-g" layout="/src/layouts/inline.astro"
<div class="d-g g-4 gtc-2 ta-c tc-white">
  <div class="bg-indigo p-4 rad-1">A</div>
  <div class="bg-indigo p-4 rad-1">B</div>
  <div class="bg-indigo p-4 rad-1">C</div>
</div>
```

## Inline

> Initial value

This example sets the display to **inline**. The **d-i** utility makes the element an inline element, allowing it to sit within a line of text without breaking the flow.

```html live "d-i" layout="/src/layouts/inline.astro"
<div class="bg-indigo d-i p-4 rad-1 tc-white">A</div>
<div class="bg-indigo d-i p-4 rad-1 tc-white">B</div>
<div class="bg-indigo d-i p-4 rad-1 tc-white">C</div>
```

## Inline Flex

This example sets the display to **inline-flex**. The **d-if** utility makes the element an inline flex container, allowing it to sit within a line of text while enabling flexbox layout properties for its children.

```html live "d-if" layout="/src/layouts/inline.astro"
<div class="d-if g-4 ta-c tc-white">
  <div class="bg-indigo d-ib p-4 rad-1 w-32">A</div>
  <div class="bg-indigo d-ib p-4 rad-1 w-32">B</div>
  <div class="bg-indigo d-ib p-4 rad-1 w-32">C</div>
</div>
```

## Inline Block

This example sets the display to **inline-block**. The **d-ib** utility makes the element an inline-block element, allowing it to sit within a line of text while also respecting width and height properties.

```html live "d-ib" layout="/src/layouts/inline.astro"
<div class="ta-c tc-white">
  <span class="bg-indigo d-ib ml-4 p-4 rad-1 w-32">A</span>
  <span class="bg-indigo d-ib ml-4 p-4 rad-1 w-32">B</span>
  <span class="bg-indigo d-ib ml-4 p-4 rad-1 w-32">C</span>
</div>
```

## Inline Grid

This example sets the display to **inline-grid**. The **d-ig** utility makes the element an inline grid container, allowing it to sit within a line of text while enabling grid layout properties for its children.

```html live "d-ig"
<div class="d-ig gtc-2 g-4 ta-c ta-c tc-white">
  <div class="bg-indigo d-ib p-4 rad-1 w-32">A</div>
  <div class="bg-indigo d-ib p-4 rad-1 w-32">B</div>
  <div class="bg-indigo d-ib p-4 rad-1 w-32">C</div>
</div>
```

## Inline Table

This example sets the display to **inline-table**. The **d-it** utility makes the element an inline table, allowing it to sit within a line of text while behaving like a table element.

```html "d-it" layout="/src/layouts/inline.astro"
<div class="d-it ..."></div>
```

## Table

This example sets the display to **table**. The **d-t** utility makes the element a block-level table, allowing it to contain table rows and cells.

```html "d-t" layout="/src/layouts/inline.astro"
<div class="d-t ..."></div>
```

## None

This example sets the display to **none**. The **d-none** utility hides the element, preventing it from being displayed in the layout.

```html live "d-none" layout="/src/layouts/inline.astro"
<div class="d-g rg-4 ta-c tc-white">
  <div class="bg-indigo d-none p-4 rad-1">A</div>
  <div class="bg-indigo p-4 rad-1">B</div>
  <div class="bg-indigo p-4 rad-1">C</div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<MediaModifier class="d-none" mediaModifier="d-b" classPrefix="d">
  ### Breakpoint modifier
</MediaModifier>

<HoverModifier class="d-none" hoverModifier="d-b" classPrefix="d">
  ### Hover modifier
</HoverModifier>
