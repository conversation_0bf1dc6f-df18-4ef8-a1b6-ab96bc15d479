---
import { icons, type IconName } from "@/constants/icons";

interface Props {
  icon: IconName;
}

const { icon: name } = Astro.props as Props;
---

<div class="d-f g-2">
  <span class="ai-c bs-sm decorator-icon d-f h-fc mt-1 fs-0 p-1" set:html={icons[name]} />
  <span><slot /></span>
</div>

<style>
  .decorator-icon {
    background-color: light-dark(var(--sl-color-gray-7), var(--sl-color-gray-6));
    border-radius: 0.2rem;
    border: solid 1px light-dark(var(--sl-color-accent), var(--sl-color-accent-high));
    color: light-dark(var(--sl-color-accent), var(--sl-color-accent-high));
  }
</style>
