---
title: Transform Origin
banner:
  content: N/A
description: Controls to specify where an element should start transforming.
slug: docs/transform-origin
---

<Class category="transform" name="transform-origin" />

## Bottom

This example sets the transform origin to **bottom**. The **t-o-b** utility defines the point around which transformations (such as rotations or scaling) are applied, positioning it at the bottom edge of the element.

```html live "t-o-b"
<div class="d-g g-16 gtc-1 sm:gtc-3">
  <div class="my-8 p-r">
    <img class="d-18 d-ib rad-1 t-o-b t-r-30" src="https://picsum.photos/600?image=360" />
    <img class="o-20 d-18 d-ib l-0 p-a rad-1" src="https://picsum.photos/600?image=360" />
  </div>

  <div class="my-8 p-r">
    <img class="d-18 d-ib rad-1 t-o-b t-r-45" src="https://picsum.photos/600?image=360" />
    <img class="o-20 d-18 d-ib l-0 p-a rad-1" src="https://picsum.photos/600?image=360" />
  </div>

  <div class="my-8 p-r">
    <img class="d-18 d-ib rad-1 t-o-b t-r-55" src="https://picsum.photos/600?image=360" />
    <img class="o-20 d-18 d-ib l-0 p-a rad-1" src="https://picsum.photos/600?image=360" />
  </div>
</div>
```

## Bottom Left

This example sets the transform origin to **bottom-left**. The **t-o-bl** utility defines the point around which transformations (such as rotations or scaling) are applied, positioning it at the bottom-left corner of the element.

```html live "t-o-bl"
<div class="d-g g-16 gtc-1 sm:gtc-3">
  <div class="my-8 p-r">
    <img class="d-18 d-ib rad-1 t-o-bl t-r-30" src="https://picsum.photos/600?image=360" />
    <img class="o-20 d-18 d-ib l-0 p-a rad-1" src="https://picsum.photos/600?image=360" />
  </div>

  <div class="my-8 p-r">
    <img class="d-18 d-ib rad-1 t-o-bl t-r-45" src="https://picsum.photos/600?image=360" />
    <img class="o-20 d-18 d-ib l-0 p-a rad-1" src="https://picsum.photos/600?image=360" />
  </div>

  <div class="my-8 p-r">
    <img class="d-18 d-ib rad-1 t-o-bl t-r-55" src="https://picsum.photos/600?image=360" />
    <img class="o-20 d-18 d-ib l-0 p-a rad-1" src="https://picsum.photos/600?image=360" />
  </div>
</div>
```

## Bottom Right

This example sets the transform origin to **bottom-right**. The **t-o-br** utility defines the point around which transformations (such as rotations or scaling) are applied, positioning it at the bottom-right corner of the element.

```html live "t-o-br"
<div class="d-g g-16 gtc-1 sm:gtc-3">
  <div class="my-8 p-r">
    <img class="d-18 d-ib rad-1 t-o-br t-r-30" src="https://picsum.photos/600?image=360" />
    <img class="o-20 d-18 d-ib l-0 p-a rad-1" src="https://picsum.photos/600?image=360" />
  </div>

  <div class="my-8 p-r">
    <img class="d-18 d-ib rad-1 t-o-br t-r-45" src="https://picsum.photos/600?image=360" />
    <img class="o-20 d-18 d-ib l-0 p-a rad-1" src="https://picsum.photos/600?image=360" />
  </div>

  <div class="my-8 p-r">
    <img class="d-18 d-ib rad-1 t-o-br t-r-55" src="https://picsum.photos/600?image=360" />
    <img class="o-20 d-18 d-ib l-0 p-a rad-1" src="https://picsum.photos/600?image=360" />
  </div>
</div>
```

## Center

This example sets the transform origin to **center**. The **t-o-c** utility defines the point around which transformations (such as rotations or scaling) are applied, positioning it at the center of the element.

```html live "t-o-c"
<div class="d-g g-16 gtc-1 sm:gtc-3">
  <div class="my-8 p-r">
    <img class="d-18 d-ib rad-1 t-o-c t-r-30" src="https://picsum.photos/600?image=360" />
    <img class="o-20 d-18 d-ib l-0 p-a rad-1" src="https://picsum.photos/600?image=360" />
  </div>

  <div class="my-8 p-r">
    <img class="d-18 d-ib rad-1 t-o-c t-r-45" src="https://picsum.photos/600?image=360" />
    <img class="o-20 d-18 d-ib l-0 p-a rad-1" src="https://picsum.photos/600?image=360" />
  </div>

  <div class="my-8 p-r">
    <img class="d-18 d-ib rad-1 t-o-c t-r-55" src="https://picsum.photos/600?image=360" />
    <img class="o-20 d-18 d-ib l-0 p-a rad-1" src="https://picsum.photos/600?image=360" />
  </div>
</div>
```

## Left

This example sets the transform origin to **left**. The **t-o-l** utility defines the point around which transformations (such as rotations or scaling) are applied, positioning it at the left edge of the element.

```html live "t-o-l"
<div class="d-g g-16 gtc-1 sm:gtc-3">
  <div class="my-8 p-r">
    <img class="d-18 d-ib rad-1 t-o-l t-r-30" src="https://picsum.photos/600?image=360" />
    <img class="o-20 d-18 d-ib l-0 p-a rad-1" src="https://picsum.photos/600?image=360" />
  </div>

  <div class="my-8 p-r">
    <img class="d-18 d-ib rad-1 t-o-l t-r-45" src="https://picsum.photos/600?image=360" />
    <img class="o-20 d-18 d-ib l-0 p-a rad-1" src="https://picsum.photos/600?image=360" />
  </div>

  <div class="my-8 p-r">
    <img class="d-18 d-ib rad-1 t-o-l t-r-55" src="https://picsum.photos/600?image=360" />
    <img class="o-20 d-18 d-ib l-0 p-a rad-1" src="https://picsum.photos/600?image=360" />
  </div>
</div>
```

## Right

This example sets the transform origin to **right**. The **t-o-r** utility defines the point around which transformations (such as rotations or scaling) are applied, positioning it at the right edge of the element.

```html live "t-o-r"
<div class="d-g g-16 gtc-1 sm:gtc-3">
  <div class="my-8 p-r">
    <img class="d-18 d-ib rad-1 t-o-r t-r-30" src="https://picsum.photos/600?image=360" />
    <img class="o-20 d-18 d-ib l-0 p-a rad-1" src="https://picsum.photos/600?image=360" />
  </div>

  <div class="my-8 p-r">
    <img class="d-18 d-ib rad-1 t-o-r t-r-45" src="https://picsum.photos/600?image=360" />
    <img class="o-20 d-18 d-ib l-0 p-a rad-1" src="https://picsum.photos/600?image=360" />
  </div>

  <div class="my-8 p-r">
    <img class="d-18 d-ib rad-1 t-o-r t-r-55" src="https://picsum.photos/600?image=360" />
    <img class="o-20 d-18 d-ib l-0 p-a rad-1" src="https://picsum.photos/600?image=360" />
  </div>
</div>
```

## Top

This example sets the transform origin to **top**. The **t-o-t** utility defines the point around which transformations (such as rotations or scaling) are applied, positioning it at the top edge of the element.

```html live "t-o-t"
<div class="d-g g-16 gtc-1 sm:gtc-3">
  <div class="my-8 p-r">
    <img class="d-18 d-ib rad-1 t-o-t t-r-30" src="https://picsum.photos/600?image=360" />
    <img class="o-20 d-18 d-ib l-0 p-a rad-1" src="https://picsum.photos/600?image=360" />
  </div>

  <div class="my-8 p-r">
    <img class="d-18 d-ib rad-1 t-o-t t-r-45" src="https://picsum.photos/600?image=360" />
    <img class="o-20 d-18 d-ib l-0 p-a rad-1" src="https://picsum.photos/600?image=360" />
  </div>

  <div class="my-8 p-r">
    <img class="d-18 d-ib rad-1 t-o-t t-r-55" src="https://picsum.photos/600?image=360" />
    <img class="o-20 d-18 d-ib l-0 p-a rad-1" src="https://picsum.photos/600?image=360" />
  </div>
</div>
```

## Top Left

This example sets the transform origin to **top left**. The **t-o-tl** utility defines the point around which transformations (such as rotations or scaling) are applied, positioning it at the top-left corner of the element.

```html live "t-o-tl"
<div class="d-g g-16 gtc-1 sm:gtc-3">
  <div class="my-8 p-r">
    <img class="d-18 d-ib rad-1 t-o-tl t-r-30" src="https://picsum.photos/600?image=360" />
    <img class="o-20 d-18 d-ib l-0 p-a rad-1" src="https://picsum.photos/600?image=360" />
  </div>

  <div class="my-8 p-r">
    <img class="d-18 d-ib rad-1 t-o-tl t-r-45" src="https://picsum.photos/600?image=360" />
    <img class="o-20 d-18 d-ib l-0 p-a rad-1" src="https://picsum.photos/600?image=360" />
  </div>

  <div class="my-8 p-r">
    <img class="d-18 d-ib rad-1 t-o-tl t-r-55" src="https://picsum.photos/600?image=360" />
    <img class="o-20 d-18 d-ib l-0 p-a rad-1" src="https://picsum.photos/600?image=360" />
  </div>
</div>
```

## Top Right

This example sets the transform origin to **top right**. The **t-o-tr** utility defines the point around which transformations (such as rotations or scaling) are applied, positioning it at the top-right corner of the element.

```html live "t-o-tr"
<div class="d-g g-16 gtc-1 sm:gtc-3">
  <div class="my-8 p-r">
    <img class="d-18 d-ib rad-1 t-o-tr t-r-30" src="https://picsum.photos/600?image=360" />
    <img class="o-20 d-18 d-ib l-0 p-a rad-1" src="https://picsum.photos/600?image=360" />
  </div>

  <div class="my-8 p-r">
    <img class="d-18 d-ib rad-1 t-o-tr t-r-45" src="https://picsum.photos/600?image=360" />
    <img class="o-20 d-18 d-ib l-0 p-a rad-1" src="https://picsum.photos/600?image=360" />
  </div>

  <div class="my-8 p-r">
    <img class="d-18 d-ib rad-1 t-o-tr t-r-55" src="https://picsum.photos/600?image=360" />
    <img class="o-20 d-18 d-ib l-0 p-a rad-1" src="https://picsum.photos/600?image=360" />
  </div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/foundations/responsive-design/) or other factors, such as [hover states](/docs/foundations/variants/).

<BreakpointVariant class="t-o-l" variant="t-o-r" classPrefix="t-o">
  ### Breakpoint variant
</BreakpointVariant>

<HoverVariant class="t-o-l" variant="t-o-r" classPrefix="t-o">
  ### Hover variant
</HoverVariant>
