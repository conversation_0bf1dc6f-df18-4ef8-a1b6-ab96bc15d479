---
title: Text Decoration
banner:
  content: N/A
description: Controls the text decoration of an element.
slug: docs/text-decoration
---

<Class category="text" name="text-decoration" />

## None

This example sets the text decoration to **none**. The text will be displayed without any decoration.

```html live "td-none" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg p-4 rad-1 ta-c tc-lead td-none">Sphinx of black quartz, judge my vow.</p>
</div>
```

## Underline

This example sets the text decoration to **underline**. The text will be displayed with an underline.

```html live "td-u" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg p-4 rad-1 ta-c tc-lead td-u">Sphinx of black quartz, judge my vow.</p>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<BreakpointVariant class="td-none" variant="td-u" classPrefix="td">
  ### Breakpoint variant
</BreakpointVariant>

<HoverVariant class="td-none" variant="td-u" classPrefix="td">
  ### Hover variant
</HoverVariant>
