---
title: Colors
banner:
  content: N/A
description: Learn how to use colors in Yumma CSS.
slug: docs/foundations/colors
---

## Default Colors

Yumma CSS comes with a built-in color system that allows you to easily customize the colors of your elements.

Here's a range of all the default colors from the Yumma CSS theme.

<Palette
  data={[
    { name: "Red", color: "rgb(215, 61, 61)" },
    { name: "Orange", color: "rgb(224, 104, 20)" },
    { name: "Yellow", color: "rgb(211, 161, 7)" },
    { name: "<PERSON>", color: "rgb(31, 177, 85)" },
    { name: "<PERSON><PERSON>", color: "rgb(18, 166, 149)" },
    { name: "<PERSON><PERSON>", color: "rgb(5, 164, 191)" },
    { name: "Blue", color: "rgb(53, 117, 221)" },
    { name: "Indigo", color: "rgb(89, 92, 217)" },
    { name: "Violet", color: "rgb(125, 83, 221)" },
    { name: "<PERSON>", color: "rgb(212, 65, 138)" },
    { name: "Lead", color: "rgb(63, 63, 78)" },
    { name: "<PERSON>", color: "rgb(96, 103, 115)" },
    { name: "<PERSON>", color: "rgb(191, 194, 199)" },
  ]}
/>

## Using color utilities

Here's an example of how to use the color system to style a button:

```html live "bg-indigo-4" "bg-indigo-9"
<div class="d-g cg-4 gtc-2">
  <button class="b-0 bg-indigo-4 fw-600 px-5 py-1 rad-1 tc-black">Subscribe</button>
  <button class="b-0 bg-indigo-9 fw-600 px-5 py-1 rad-1 tc-white">Subscribe</button>
</div>
```
