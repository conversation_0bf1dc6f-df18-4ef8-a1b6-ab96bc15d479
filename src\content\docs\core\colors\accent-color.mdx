---
title: Accent Color
banner:
  content: N/A
description: Controls form control accent color.
slug: docs/accent-color
---

<Class category="color" name="accent-color" />

This example showcases the default browser **accent color** and a custom Indigo color. The custom accent will have a light indigo shade.

```html live "ac-indigo-8"
<div class="d-g g-8 gtc-1 sm:gtc-2">
  <label><input class="mr-1" type="checkbox" checked />Default </label>
  <label><input class="ac-indigo-2 mr-1" type="checkbox" checked />Custom</label>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<HoverVariant class="ac-lead" variant="ac-indigo" classPrefix="ac">
  ### Hover variant
</HoverVariant>
