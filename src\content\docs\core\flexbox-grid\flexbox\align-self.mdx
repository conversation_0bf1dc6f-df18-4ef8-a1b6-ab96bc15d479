---
title: Align Self
banner:
  content: N/A
description: Controls the positioning of a single flex or grid element along the transverse axis of its container.
slug: docs/align-self
---

<Class category="flexbox" name="align-self" />

## Auto

> Initial value

This example sets the align self to **auto**. The item will use the default alignment based on the container's alignment properties.

```html live "as-auto" layout="/src/layouts/inline.astro"
<div class="d-f g-4 h-24 w-full" id="area">
  <div class="ai-c bg-indigo-8 d-f f-1 jc-c p-4 rad-1 tc-indigo-5">A</div>
  <div class="ai-c as-auto bg-indigo d-f f-1 jc-c p-4 rad-1 tc-white">B</div>
  <div class="ai-c bg-indigo-8 d-f f-1 jc-c p-4 rad-1 tc-indigo-5">C</div>
</div>
```

## Baseline

This example sets the align self to **baseline**. The item will be aligned along the baseline of the container.

```html live "as-b" layout="/src/layouts/inline.astro"
<div class="d-f g-4 h-24 w-full" id="area">
  <div class="ai-c bg-indigo-8 d-f f-1 jc-c p-4 rad-1 tc-indigo-5">A</div>
  <div class="ai-c as-b bg-indigo d-f f-1 jc-c p-4 rad-1 tc-white">B</div>
  <div class="ai-c bg-indigo-8 d-f f-1 jc-c p-4 rad-1 tc-indigo-5">C</div>
</div>
```

## Center

This example sets the align self to **center**. The item will be centered within its container.

```html live "as-c" layout="/src/layouts/inline.astro"
<div class="d-f g-4 h-24 w-full" id="area">
  <div class="ai-c bg-indigo-8 d-f f-1 jc-c p-4 rad-1 tc-indigo-5">A</div>
  <div class="ai-c as-c bg-indigo d-f f-1 jc-c p-4 rad-1 tc-white">B</div>
  <div class="ai-c bg-indigo-8 d-f f-1 jc-c p-4 rad-1 tc-indigo-5">C</div>
</div>
```

## Flex-End

This example sets the align self to **flex-end**. The item will be aligned to the end of its container.

```html live "as-fe" layout="/src/layouts/inline.astro"
<div class="d-f g-4 h-24 w-full" id="area">
  <div class="ai-c bg-indigo-8 d-f f-1 jc-c p-4 rad-1 tc-indigo-5">A</div>
  <div class="ai-c as-fe bg-indigo d-f f-1 jc-c p-4 rad-1 tc-white">B</div>
  <div class="ai-c bg-indigo-8 d-f f-1 jc-c p-4 rad-1 tc-indigo-5">C</div>
</div>
```

## Flex-Start

This example sets the align self to **flex-start**. The item will be aligned to the start of its container.

```html live "as-fs" layout="/src/layouts/inline.astro"
<div class="d-f g-4 h-24 w-full" id="area">
  <div class="ai-c bg-indigo-8 d-f f-1 jc-c p-4 rad-1 tc-indigo-5">A</div>
  <div class="ai-c as-fs bg-indigo d-f f-1 jc-c p-4 rad-1 tc-white">B</div>
  <div class="ai-c bg-indigo-8 d-f f-1 jc-c p-4 rad-1 tc-indigo-5">C</div>
</div>
```

## Stretch

This example sets the align self to **stretch**. The item will stretch to fill its container along the cross axis.

```html live "as-s" layout="/src/layouts/inline.astro"
<div class="d-f g-4 h-24 w-full" id="area">
  <div class="ai-c bg-indigo-8 d-f f-1 jc-c p-4 rad-1 tc-indigo-5">A</div>
  <div class="ai-c as-s bg-indigo d-f f-1 jc-c p-4 rad-1 tc-white">B</div>
  <div class="ai-c bg-indigo-8 d-f f-1 jc-c p-4 rad-1 tc-indigo-5">C</div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<MediaModifier class="as-s" mediaModifier="as-e" classPrefix="as">
  ### Media modifier
</MediaModifier>

<HoverModifier class="as-s" hoverModifier="as-e" classPrefix="as">
  ### Hover modifier
</HoverModifier>
