---
import tinycolor from "tinycolor2";

interface ColorClass {
  color: string;
  value: string;
}

interface Props {
  classPrefix: string;
  propNames: string[];
  data: ColorClass[];
}

const { classPrefix, propNames, data } = Astro.props as Props;

const mixColors = (color1: string, color2: string, weight: number): string => {
  return tinycolor.mix(color1, color2, weight).toHexString();
};

const generateShades = (color: string) => {
  const lightShades: string[] = [];
  const darkShades: string[] = [];

  for (let i = 6; i >= 1; i--) {
    lightShades.push(mixColors(color, "white", i * 10));
  }

  const baseColor = color;

  for (let i = 1; i <= 6; i++) {
    darkShades.push(mixColors(color, "black", i * 10));
  }

  return { lightShades, baseColor, darkShades };
};

function generateClassData(classPrefix: string, propNames: string[], data: ColorClass[]) {
  return data.flatMap((colorClass) => {
    const { lightShades, baseColor, darkShades } = generateShades(colorClass.value);

    return [
      ...lightShades.map((shade, i) => ({
        classTitle: `${classPrefix}l-${colorClass.color}-${6 - i}`,
        properties: propNames.map((propertyName) => `${propertyName}: ${shade};`),
        colorValue: shade,
      })),
      {
        classTitle: `${classPrefix}${colorClass.color}`,
        properties: propNames.map((propertyName) => `${propertyName}: ${baseColor};`),
        colorValue: baseColor,
      },
      ...darkShades.map((shade, i) => ({
        classTitle: `${classPrefix}d-${colorClass.color}-${i + 1}`,
        properties: propNames.map((propertyName) => `${propertyName}: ${shade};`),
        colorValue: shade,
      })),
    ];
  });
}

const colorData = generateClassData(classPrefix, propNames, data);
---

<div class="max-h-90 o-y-auto mb-5">
  <table class="w-full" style="display: inline-table !important;">
    <thead>
      <tr>
        <th class="fs-md fw-600">Class</th>
        <th class="fs-md fw-600">Properties</th>
      </tr>
    </thead>
    <tbody>
      {
        colorData.map((classItem) => (
          <tr>
            <td class="fs-xs">
              <p class="tc-accent">{classItem.classTitle}</p>
            </td>
            <td class="fs-xs">
              <code style="background-color: transparent !important;">{classItem.properties.join("\n")}</code>
            </td>
          </tr>
        ))
      }
    </tbody>
  </table>
</div>
