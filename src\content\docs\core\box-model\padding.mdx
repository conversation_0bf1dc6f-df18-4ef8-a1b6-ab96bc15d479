---
title: Padding
banner:
  content: N/A
description: Controls the padding of an element.
slug: docs/padding
---

<Class category="boxModel" name="padding" />

This example sets the padding to **2rem**. The element will have uniform padding inside it.

```html live "p-6"
<div id="area">
  <div class="p-8">
    <p class="bg-indigo tc-white p-6">6</p>
  </div>
</div>
```

## Padding Bottom

Controls the bottom padding of an element.

<Class category="boxModel" name="padding-bottom" />

This example sets the bottom padding to **2rem**. The element will have space inside it at the bottom.

```html live "pb-6"
<div id="area">
  <div class="p-8">
    <p class="bg-indigo tc-white pb-6">6</p>
  </div>
</div>
```

## Padding Left

Controls the left padding of an element.

<Class category="boxModel" name="padding-left" />

This example sets the left padding to **2rem**. The element will have space inside it on the left side.

```html live "pl-6"
<div id="area">
  <div class="p-8">
    <p class="bg-indigo tc-white pl-6">6</p>
  </div>
</div>
```

## Padding Right

Controls the right padding of an element.

<Class category="boxModel" name="padding-right" />

This example sets the right padding to **2rem**. The element will have space inside it on the right side.

```html live "pr-6"
<div id="area">
  <div class="p-8">
    <p class="bg-indigo tc-white pr-6">6</p>
  </div>
</div>
```

## Padding Top

Controls the top padding of an element.

<Class category="boxModel" name="padding-top" />

This example sets the top padding to **2rem**. The element will have space inside it at the top.

```html live "pt-6"
<div id="area">
  <div class="p-8">
    <p class="bg-indigo tc-white pt-6">6</p>
  </div>
</div>
```

## Padding Block End

Controls the logical block end padding of an element, which maps to a physical padding depending on the element's writing mode, directionality, and text orientation.

<Class category="boxModel" name="padding-block-end" />

This example sets the bottom padding to **2rem**. The element will have space inside it at the bottom in a vertical writing mode.

```html live "pbe-6"
<div id="area">
  <div class="p-8">
    <p class="bg-indigo tc-white pbe-6">6</p>
  </div>
</div>
```

## Padding Block Start

Controls the logical block start padding of an element, which maps to a physical padding depending on the element's writing mode, directionality, and text orientation.

<Class category="boxModel" name="padding-block-start" />

This example sets the top padding to **2rem**. The element will have space inside it at the top in a vertical writing mode.

```html live "pbs-6"
<div id="area">
  <div class="p-8">
    <p class="bg-indigo tc-white pbs-6">6</p>
  </div>
</div>
```

## Padding Inline End

Controls the logical inline end padding of an element, which maps to a physical padding depending on the element's writing mode, directionality, and text orientation.

<Class category="boxModel" name="padding-inline-end" />

This example sets the right padding to **2rem**. The element will have space inside it on the right side in a horizontal writing mode.

```html live "pie-6"
<div id="area">
  <div class="p-8">
    <p class="bg-indigo tc-white pie-6">6</p>
  </div>
</div>
```

## Padding Inline Start

Controls the logical inline start padding of an element, which maps to a physical padding depending on the element's writing mode, directionality, and text orientation.

<Class category="boxModel" name="padding-inline-start" />

This example sets the left padding to **2rem**. The element will have space inside it on the left side in a horizontal writing mode.

```html live "pis-6"
<div id="area">
  <div class="p-8">
    <p class="bg-indigo tc-white pis-6">6</p>
  </div>
</div>
```

## Padding X

Controls the left and right paddings of an element at once.

<Class category="boxModel" name="padding-x" />

This example sets the left and right padding to **2rem**. The element will have uniform space on both sides inside it.

```html live "px-6"
<div id="area">
  <div class="p-8">
    <p class="bg-indigo tc-white px-6">6</p>
  </div>
</div>
```

## Padding Y

Controls the bottom and top paddings of an element at once.

<Class category="boxModel" name="padding-y" />

This example sets the bottom and top to **2rem**. The element will have uniform space above and below its content.

```html live "py-6"
<div id="area">
  <div class="p-8">
    <p class="bg-indigo tc-white py-6">6</p>
  </div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<BreakpointVariant class="p-1" variant="p-2" classPrefix="p">
  ### Breakpoint variant
</BreakpointVariant>

<HoverVariant class="p-1" variant="p-2" classPrefix="p">
  ### Hover variant
</HoverVariant>
