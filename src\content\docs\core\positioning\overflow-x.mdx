---
title: Overflow X
banner:
  content: N/A
description: Controls how an element behaves when content overflows on the X-axis.
slug: docs/overflow-x
---

<Class category="positioning" name="overflow-x" />

## Auto

This example sets the horizontal overflow behavior to **auto**. The **o-x-auto** utility allows the content to scroll horizontally if it overflows the container, while hiding the scrollbar when not needed.

```html live "o-x-auto"
<div class="bg-indigo-2 h-32 o-x-auto p-4 w-64">
  <div class="bg-indigo p-4 tc-white w-96">This content overflows horizontally.</div>
</div>
```

## Clip

This example sets the horizontal overflow behavior to **clip**. The **o-x-c** utility clips the content that overflows horizontally, preventing it from being displayed or scrolled.

```html live "o-x-c"
<div class="bg-indigo-2 h-32 o-x-c p-4 w-64">
  <div class="bg-indigo p-4 tc-white w-96">This content is clipped and cannot be scrolled horizontally.</div>
</div>
```

## Hidden

This example sets the horizontal overflow behavior to **hidden**. The **o-x-h** utility hides any content that overflows horizontally, ensuring it is not visible or scrollable.

```html live "o-x-h"
<div class="bg-indigo-2 h-32 o-x-h p-4 w-64">
  <div class="bg-indigo p-4 tc-white w-96">This content is hidden when it overflows horizontally.</div>
</div>
```

## Scroll

This example sets the horizontal overflow behavior to **scroll**. The **o-x-s** utility enables a horizontal scrollbar for the container, allowing users to scroll through the content even if it doesn’t overflow.

```html live "o-x-s"
<div class="bg-indigo-2 h-32 o-x-s p-4 w-64">
  <div class="bg-indigo p-4 tc-white w-96">This content overflows the container, and a horizontal scrollbar is always visible.</div>
</div>
```

## Visible

> Initial value

This example sets the horizontal overflow behavior to **visible**. The **o-x-v** utility ensures that any content overflowing horizontally remains visible outside the container.

```html live "o-x-v"
<div class="bg-indigo-2 h-32 o-x-v p-4 w-64">
  <div class="bg-indigo p-4 tc-white w-96">This content overflows the container and is fully visible horizontally.</div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/foundations/responsive-design/) or other factors, such as [hover states](/docs/foundations/variants/).

<BreakpointVariant class="o-x-h" variant="o-x-auto" classPrefix="o-x">
  ### Breakpoint variant
</BreakpointVariant>

<HoverVariant class="o-x-h" variant="o-x-auto" classPrefix="o-x">
  ### Hover variant
</HoverVariant>
