---
interface ClassItem {
  className: string;
  properties: string[];
}

interface AdditionalClass {
  name: string;
  value: string;
}

interface Props {
  additionalClasses?: AdditionalClass[];
  classPrefix: string;
  data?: ClassItem[];
  incrementName?: string;
  incrementValue: number;
  incrementPrefix?: number;
  propNames: string[];
  range: number;
  unit?: string;
  excludeZero?: boolean;
  round?: number;
}

const { additionalClasses, classPrefix, data, incrementName, incrementValue, incrementPrefix, propNames, range, unit, excludeZero, round } = Astro.props as Props;

function generateClassData(
  classPrefix: string,
  propNames: string[],
  range: number,
  incrementValue: number,
  incrementPrefix?: number,
  unit?: string,
  additionalClasses?: AdditionalClass[],
  excludeZero?: boolean,
  round?: number
): ClassItem[] {
  const data: ClassItem[] = [];
  const startIndex = excludeZero ? 1 : 0;

  for (let i = startIndex; i <= range; i++) {
    const properties = propNames.map((propertyName) => {
      let value = incrementName ? incrementName.replace(/%i/g, `${i * incrementValue}`) : unit ? `${i * incrementValue}${unit}` : `${i * incrementValue}`;

      if (round !== undefined) {
        value = parseFloat(value).toFixed(round);
      }

      return `${propertyName}: ${value};`;
    });

    const className = incrementPrefix ? `${classPrefix}${i * incrementPrefix}` : `${classPrefix}${i * 1}`;

    data.push({ className, properties });
  }

  if (additionalClasses) {
    additionalClasses.forEach((additionalClass) => {
      const properties = propNames.map((propertyName) => `${propertyName}: ${additionalClass.value};`);
      data.push({
        className: `${classPrefix}${additionalClass.name}`,
        properties,
      });
    });
  }

  return data;
}

const codeData = data || generateClassData(classPrefix, propNames, range, incrementValue, incrementPrefix, unit, additionalClasses, excludeZero, round);
---

<div class="max-h-90 o-y-auto mb-5">
  <table>
    <thead>
      <tr>
        <th class="fs-md fw-600">Class</th>
        <th class="fs-md fw-600">Properties</th>
      </tr>
    </thead>
    <tbody>
      {
        codeData.map((classItem) => (
          <tr>
            <td class="fs-xs">
              <p class="tc-accent">{classItem.className}</p>
            </td>
            <td class="fs-xs">
              <code style="background-color: transparent !important;">{classItem.properties.join("\n")}</code>
            </td>
          </tr>
        ))
      }
    </tbody>
  </table>
</div>

<style>
  .cls {
    color: light-dark(var(--sl-color-accent), var(--sl-color-accent-high));
  }

  .prop {
    color: light-dark(var(--sl-color-gray-3), var(--sl-color-gray-8));
  }
</style>
