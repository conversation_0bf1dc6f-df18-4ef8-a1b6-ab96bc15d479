---
import { Code } from "@astrojs/starlight/components";
import Note from "./Note.astro";

interface Props {
  class: string;
  classPrefix: string;
  hoverModifier: string;
}

const { class: baseClass, classPrefix, hoverModifier } = Astro.props as Props;
---

<section>
  <Note icon="cursor_arrow">
    <slot />
  </Note>

  <p>
    Alternatively, you can apply <code>:hover</code> by using <code>h:{classPrefix}-*</code> utility to override elements and change their values when hovering over them.
  </p>

  <Code code={`<div class="${baseClass} h:${hoverModifier} ..."></div>`} lang="html" mark={`h:${hoverModifier}`} />
</section>
