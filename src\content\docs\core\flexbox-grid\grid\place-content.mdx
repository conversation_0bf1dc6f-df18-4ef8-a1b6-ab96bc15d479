---
title: Place Content
banner:
  content: N/A
description: Control how content is justified and aligned simultaneously.
slug: docs/place-content
---

<Class category="grid" name="place-content" />

## Baseline

This example sets the place content to **baseline**. The items will be aligned along the baseline of the container.

```html live "pc-b" layout="/src/layouts/inline.astro"
<div class="tc-white" id="area">
  <div class="d-g g-4 gtc-2 h-50 pc-b">
    <div class="ai-c bg-indigo d-14 d-f jc-c p-4 rad-1">A</div>
    <div class="ai-c bg-indigo d-14 d-f jc-c p-4 rad-1">B</div>
    <div class="ai-c bg-indigo d-14 d-f jc-c p-4 rad-1">C</div>
    <div class="ai-c bg-indigo d-14 d-f jc-c p-4 rad-1">D</div>
  </div>
</div>
```

## Center

This example sets the place content to **center**. The items will be centered within the container.

```html live "pc-c" layout="/src/layouts/inline.astro"
<div class="tc-white" id="area">
  <div class="d-g g-4 gtc-2 h-50 pc-c">
    <div class="ai-c bg-indigo d-14 d-f jc-c p-4 rad-1">A</div>
    <div class="ai-c bg-indigo d-14 d-f jc-c p-4 rad-1">B</div>
    <div class="ai-c bg-indigo d-14 d-f jc-c p-4 rad-1">C</div>
    <div class="ai-c bg-indigo d-14 d-f jc-c p-4 rad-1">D</div>
  </div>
</div>
```

## End

This example sets the place content to **end**. The items will be aligned to the end of the container.

```html live "pc-e" layout="/src/layouts/inline.astro"
<div class="tc-white" id="area">
  <div class="d-g g-4 gtc-2 h-50 pc-e">
    <div class="ai-c bg-indigo d-14 d-f jc-c p-4 rad-1">A</div>
    <div class="ai-c bg-indigo d-14 d-f jc-c p-4 rad-1">B</div>
    <div class="ai-c bg-indigo d-14 d-f jc-c p-4 rad-1">C</div>
    <div class="ai-c bg-indigo d-14 d-f jc-c p-4 rad-1">D</div>
  </div>
</div>
```

## Start

This example sets the place content to **start**. The items will be aligned to the start of the container.

```html live "pc-s" layout="/src/layouts/inline.astro"
<div class="tc-white" id="area">
  <div class="d-g g-4 gtc-2 h-50 pc-s">
    <div class="ai-c bg-indigo d-14 d-f jc-c p-4 rad-1">A</div>
    <div class="ai-c bg-indigo d-14 d-f jc-c p-4 rad-1">B</div>
    <div class="ai-c bg-indigo d-14 d-f jc-c p-4 rad-1">C</div>
    <div class="ai-c bg-indigo d-14 d-f jc-c p-4 rad-1">D</div>
  </div>
</div>
```

## Space Around

This example sets the place content to **space-around**. The items will be evenly distributed in the container with space around them.

```html live "pc-sa" layout="/src/layouts/inline.astro"
<div class="tc-white" id="area">
  <div class="d-g g-4 gtc-2 h-50 pc-sa">
    <div class="ai-c bg-indigo d-14 d-f jc-c p-4 rad-1">A</div>
    <div class="ai-c bg-indigo d-14 d-f jc-c p-4 rad-1">B</div>
    <div class="ai-c bg-indigo d-14 d-f jc-c p-4 rad-1">C</div>
    <div class="ai-c bg-indigo d-14 d-f jc-c p-4 rad-1">D</div>
  </div>
</div>
```

## Space Between

This example sets the place content to **space-between**. The items will be evenly distributed in the container with the first item at the start and the last item at the end.

```html live "pc-sb" layout="/src/layouts/inline.astro"
<div class="tc-white" id="area">
  <div class="d-g g-4 gtc-2 h-50 pc-sb">
    <div class="ai-c bg-indigo d-14 d-f jc-c p-4 rad-1">A</div>
    <div class="ai-c bg-indigo d-14 d-f jc-c p-4 rad-1">B</div>
    <div class="ai-c bg-indigo d-14 d-f jc-c p-4 rad-1">C</div>
    <div class="ai-c bg-indigo d-14 d-f jc-c p-4 rad-1">D</div>
  </div>
</div>
```

## Space Evenly

This example sets the place content to **space-evenly**. The items will be evenly distributed in the container with equal space around them.

```html live "pc-se" layout="/src/layouts/inline.astro"
<div class="tc-white" id="area">
  <div class="d-g g-4 gtc-2 h-50 pc-se">
    <div class="ai-c bg-indigo d-14 d-f jc-c p-4 rad-1">A</div>
    <div class="ai-c bg-indigo d-14 d-f jc-c p-4 rad-1">B</div>
    <div class="ai-c bg-indigo d-14 d-f jc-c p-4 rad-1">C</div>
    <div class="ai-c bg-indigo d-14 d-f jc-c p-4 rad-1">D</div>
  </div>
</div>
```

## Stretch

This example sets the place content to **stretch**. The items will stretch to fill the container along the cross axis.

```html live "pc-st" layout="/src/layouts/inline.astro"
<div class="tc-white" id="area">
  <div class="d-g g-4 gtc-2 h-50 pc-st">
    <div class="ai-c bg-indigo d-14 d-f jc-c p-4 rad-1">A</div>
    <div class="ai-c bg-indigo d-14 d-f jc-c p-4 rad-1">B</div>
    <div class="ai-c bg-indigo d-14 d-f jc-c p-4 rad-1">C</div>
    <div class="ai-c bg-indigo d-14 d-f jc-c p-4 rad-1">D</div>
  </div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<MediaModifier class="pc-s" mediaModifier="pc-e" classPrefix="pc">
  ### Breakpoint modifier
</MediaModifier>

<HoverModifier class="pc-s" hoverModifier="pc-e" classPrefix="pc">
  ### Hover modifier
</HoverModifier>
