---
title: Text Decoration Line
banner:
  content: N/A
description: Controls the text decoration line of an element.
slug: docs/text-decoration-line
---

<Class category="text" name="text-decoration-line" />

## Line Through

This example sets the text decoration line to **line-through**. The text will be displayed with a line through it, indicating that it is struck out.

```html live "tdl-lt" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg p-4 rad-1 ta-c tc-lead tdl-lt">Sphinx of black quartz, judge my vow.</p>
</div>
```

## None

This example sets the text decoration line to **none**. The text will be displayed without any decoration lines.

```html live "tdl-none" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg p-4 rad-1 ta-c tc-lead tdl-none">Sphinx of black quartz, judge my vow.</p>
</div>
```

## Underline

This example sets the text decoration line to **underline**. The text will be displayed with an underline, emphasizing the content.

```html live "tdl-u" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg p-4 rad-1 ta-c tc-lead tdl-u">Sphinx of black quartz, judge my vow.</p>
</div>
```

## Overline

This example sets the text decoration line to **overline**. The text will be displayed with a line above it.

```html live "tdl-o" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg p-4 rad-1 ta-c tc-lead tdl-o">Sphinx of black quartz, judge my vow.</p>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<MediaModifier class="tdl-none" mediaModifier="tdl-u" classPrefix="tdl">
  ### Media modifier
</MediaModifier>

<HoverModifier class="tdl-none" hoverModifier="tdl-u" classPrefix="tdl">
  ### Hover modifier
</HoverModifier>
