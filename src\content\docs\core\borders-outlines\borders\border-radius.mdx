---
title: Border Radius
banner:
  content: N/A
description: Controls the radius of the borders of an element.
slug: docs/border-radius
---

<Class category="border" name="border-radius" />

This example sets the border radius to **0.5rem**. The element will have moderately rounded corners at the bottom.

```html live "rad-2"
<div class="bg-indigo d-16 rad-2"></div>
```

## Bottom Radius

Controls the radius of the bottom borders of an element.

<Class category="border" name="border-bottom-radius" />

This example sets the bottom border radius to **0.5rem**. The element will have moderately rounded corners at the bottom.

```html live "rad-b-2"
<div class="bg-indigo d-16 rad-b-2"></div>
```

## Bottom Left Radius

Controls the radius of the bottom left border of an element.

<Class category="border" name="border-bottom-left-radius" />

This example sets the bottom left border radius to **0.5rem**. The element will have moderately rounded corners at the bottom left.

```html live "rad-bl-2"
<div class="bg-indigo d-16 rad-bl-2"></div>
```

## Bottom Right Radius

This example sets the bottom right border radius to **0.5rem**. The element will have moderately rounded corners at the bottom right.

<Class category="border" name="border-bottom-right-radius" />

```html live "rad-br-2"
<div class="bg-indigo d-16 rad-br-2"></div>
```

## Left Radius

This example sets the left border radius to **0.5rem**. The element will have moderately rounded corners at the left.

<Class category="border" name="border-left-radius" />

```html live "rad-l-2"
<div class="bg-indigo d-16 rad-l-2"></div>
```

## Right Radius

This example sets the right border radius to **0.5rem**. The element will have moderately rounded corners at the right.

<Class category="border" name="border-right-radius" />

```html live "rad-r-2"
<div class="bg-indigo d-16 rad-r-2"></div>
```

## Top Left Radius

This example sets the top left border radius to **0.5rem**. The element will have moderately rounded corners at the top left.

<Class category="border" name="border-top-left-radius" />

```html live "rad-tl-2"
<div class="bg-indigo d-16 rad-tl-2"></div>
```

## Top Right Radius

This example sets the top right border radius to **0.5rem**. The element will have moderately rounded corners at the top right.

<Class category="border" name="border-top-right-radius" />

```html live "rad-tr-2"
<div class="bg-indigo d-16 rad-tr-2"></div>
```

## Top Radius

This example sets the top border radius to **0.5rem**. The element will have moderately rounded corners at the top.

<Class category="border" name="border-top-radius" />

```html live "rad-t-2"
<div class="bg-indigo d-16 rad-t-2"></div>
```

## Using Percentages

This example showcases various `border-radius` utilities:

- The **rad-full** utility applies a `border-radius` of **100%** to an element.
- The **rad-half** utility applies a `border-radius` of **50%** to an element.

```html live "rad-100" "rad-50"
<div class="d-g g-8 gtc-1 sm:gtc-2">
  <div class="ai-c bg-indigo d-16 d-f jc-c rad-full tc-white">100%</div>
  <div class="ai-c bg-indigo d-16 d-f jc-c rad-half tc-white">50%</div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<MediaModifier class="rad-1" mediaModifier="rad-2" classPrefix="rad">
  ### Breakpoint modifier
</MediaModifier>

<HoverModifier class="rad-1" hoverModifier="rad-2" classPrefix="rad">
  ### Hover modifier
</HoverModifier>
