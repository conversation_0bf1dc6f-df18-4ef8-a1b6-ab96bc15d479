---
title: Brand
editUrl: false
description: Here you'll find the Yumma CSS brand assets and usage guidelines.
slug: brand
---

## Trademark Agreement

[Yumma Lib](https://github.com/yumma-lib/) owns the trademarks "Yumma CSS" and its associated logos. These trademarks help users and developers recognize Yumma CSS as a trusted source for our tools and resources.

**You can use Yumma CSS trademarks if you follow these rules:**

1. <u>Referencing the Project</u>: You may use the Yumma CSS name to refer to the project, tools, and resources created by Yumma Lib.
2. <u>Educational and Informative Use</u>: You may use the Yumma CSS name and logos in blogs, articles, presentations, and educational materials, as long as the use is not
   misleading.
3. <u>Link to Official Sources</u>: You may link to the official Yumma CSS website or GitHub repository and display the logo to indicate that it is the official project.

**You may not use Yumma CSS trademarks in these ways without permission:**

- Do not use Yumma CSS trademarks in a way that implies an endorsement or partnership with Yumma Lib that doesn't exist.
- Do not use Yumma CSS trademarks on products, services, or merchandise that are sold or promoted without express permission.
- Do not alter, distort, or modify the Yumma CSS logos.

---

## Trademark Assets

All official Yumma CSS logos:

| Asset    | Format | Light Version                                                                                              | Dark Version                                                                                             |
| -------- | ------ | ---------------------------------------------------------------------------------------------------------- | -------------------------------------------------------------------------------------------------------- |
| Logomark | SVG    | [mark-light.svg](https://github.com/yumma-lib/yumma-css-docs/blob/release/public/trademark/mark-light.svg) | [mark-dark.svg](https://github.com/yumma-lib/yumma-css-docs/blob/release/public/trademark/mark-dark.svg) |
| Logotype | PNG    | [logo-light.png](https://github.com/yumma-lib/yumma-css-docs/blob/release/public/trademark/logo-light.png) | [logo-dark.png](https://github.com/yumma-lib/yumma-css-docs/blob/release/public/trademark/logo-dark.png) |

### Logomark

<Note icon="moon">Try switching themes in the navigation bar to see how the logotype image changes.</Note>

```html live layout="/src/layouts/previewOnly.astro"
<img class="light:sl-hidden max-h-full" src="/trademark/mark-dark.svg" width="144" alt="Light logo" />
<img class="dark:sl-hidden max-h-full" src="/trademark/mark-light.svg" width="144" alt="Dark logo" />
```

### Logotype

<Note icon="moon">Try switching themes in the navigation bar to see how the logotype image changes.</Note>

```html live layout="/src/layouts/previewOnly.astro"
<img class="light:sl-hidden max-h-full" src="/trademark/logo-dark.png" width="440" alt="Light logo" />
<img class="dark:sl-hidden max-h-full" src="/trademark/logo-light.png" width="440" alt="Dark logo" />
```
