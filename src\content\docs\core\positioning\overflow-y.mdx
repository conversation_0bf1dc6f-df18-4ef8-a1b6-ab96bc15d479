---
title: Overflow Y
banner:
  content: N/A
description: Controls how an element behaves when content overflows on the Y-axis.
slug: docs/overflow-y
---

<Class category="positioning" name="overflow-y" />

## Auto

This example sets the vertical overflow behavior to **auto**. The **o-y-auto** utility allows the content to scroll vertically if it overflows the container, while hiding the scrollbar when not needed.

```html live "o-y-auto"
<div class="bg-indigo-2 h-32 o-y-auto p-4 w-64">
  <div class="bg-indigo h-64 p-4 tc-white w-full">This content overflows vertically.</div>
</div>
```

## Clip

This example sets the vertical overflow behavior to **clip**. The **o-y-c** utility clips the content that overflows vertically, preventing it from being displayed or scrolled.

```html live "o-y-c"
<div class="bg-indigo-2 h-32 o-y-c p-4 w-64">
  <div class="bg-indigo h-64 p-4 tc-white w-full">This content is clipped and cannot be scrolled vertically.</div>
</div>
```

## Hidden

This example sets the vertical overflow behavior to **hidden**. The **o-y-h** utility hides any content that overflows vertically, ensuring it is not visible or scrollable.

```html live "o-y-h"
<div class="bg-indigo-2 h-32 o-y-h p-4 w-64">
  <div class="bg-indigo h-64 p-4 tc-white w-full">This content is hidden when it overflows vertically.</div>
</div>
```

## Scroll

This example sets the vertical overflow behavior to **scroll**. The **o-y-s** utility enables a vertical scrollbar for the container, allowing users to scroll through the content even if it doesn’t overflow.

```html live "o-y-s"
<div class="bg-indigo-2 h-32 o-y-s p-4 w-64">
  <div class="bg-indigo h-64 p-4 tc-white w-full">This content overflows the container, and a vertical scrollbar is always visible.</div>
</div>
```

## Visible

> Initial value

This example sets the vertical overflow behavior to **visible**. The **o-y-v** utility ensures that any content overflowing vertically remains visible outside the container.

```html live "o-y-v"
<div class="bg-indigo-2 h-32 o-y-v p-4 w-64">
  <div class="bg-indigo h-64 p-4 tc-white w-full">This content overflows the container and is fully visible vertically.</div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<MediaModifier class="o-y-h" mediaModifier="o-y-auto" classPrefix="o-y">
  ### Breakpoint modifier
</MediaModifier>

<HoverModifier class="o-y-h" hoverModifier="o-y-auto" classPrefix="o-y">
  ### Hover modifier
</HoverModifier>
