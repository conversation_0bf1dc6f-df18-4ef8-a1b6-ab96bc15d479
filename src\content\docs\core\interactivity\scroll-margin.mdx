---
title: <PERSON><PERSON>
banner:
  content: N/A
description: Controls all of the scroll margins of an element simultaneously.
slug: docs/scroll-margin
---

<Class category="interactivity" name="scroll-margin" />

This example sets the scroll margin to **1.5rem**. The element will have a uniform spacing of **1.5rem** around it when scrolling, ensuring that it is positioned away from the edges of the viewport.

<Note icon="cursor_arrow">Try scrolling through the image container to see how it behaves.</Note>

```html live "sm-6" layout="/src/layouts/inline.astro"
<div class="o-h p-r">
  <div class="d-f g-6 o-x-s p-6 sst-x-m">
    <div class="fs-0 p-6 p-r sm-6 ssa-s">
      <div class="i-0 p-a w-full zi-0">
        <div class="d-full" id="area"></div>
      </div>
      <img class="h-40 p-r rad-1 w-80 zi-10" src="https://picsum.photos/400/200?image=849" />
    </div>
    <div class="fs-0 p-6 p-r sm-6 ssa-s">
      <div class="i-0 p-a w-full zi-0">
        <div class="d-full" id="area"></div>
      </div>
      <img class="h-40 p-r rad-1 w-80 zi-10" src="https://picsum.photos/400/200?image=866" />
    </div>
    <div class="fs-0 p-6 p-r sm-6 ssa-s">
      <div class="i-0 p-a w-full zi-0">
        <div class="d-full" id="area"></div>
      </div>
      <img class="h-40 p-r rad-1 w-80 zi-10" src="https://picsum.photos/400/200?image=872" />
    </div>
    <div class="fs-0 p-6 p-r sm-6 ssa-s">
      <div class="i-0 p-a w-full zi-0">
        <div class="d-full" id="area"></div>
      </div>
      <img class="h-40 p-r rad-1 w-80 zi-10" src="https://picsum.photos/400/200?image=875" />
    </div>
  </div>
</div>
```

## Scroll Margin Bottom

Sets the margin on the bottom side of an element, creating space between the element and its scrollable container.

<Class category="interactivity" name="scroll-margin-bottom" />

This example sets the scroll margin bottom to **1.5rem**. The element will have a spacing of **1.5rem** below it when scrolling, ensuring that it is positioned away from the bottom edge of the viewport.

<Note icon="cursor_arrow">Try scrolling through the image container to see how it behaves.</Note>

```html live "smb-6"
<div class="o-h p-r w-fc">
  <div class="d-f fd-c g-6 h-96 o-y-auto p-6 sst-y-m">
    <div class="fs-0 p-r pb-6 smb-6 ssa-s">
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=849" />
      <div class="h-6 l-0 l-0 p-a r-0" id="area"></div>
    </div>
    <div class="fs-0 p-r pb-6 smb-6 ssa-s">
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=866" />
      <div class="h-6 l-0 l-0 p-a r-0" id="area"></div>
    </div>
    <div class="fs-0 p-r pb-6 smb-6 ssa-s">
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=872" />
      <div class="h-6 l-0 l-0 p-a r-0" id="area"></div>
    </div>
    <div class="fs-0 p-r pb-6 smb-6 ssa-s">
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=875" />
      <div class="h-6 l-0 l-0 p-a r-0" id="area"></div>
    </div>
  </div>
</div>
```

## Scroll Margin Left

Sets the margin on the left side of an element, creating space between the element and its scrollable container.

<Class category="interactivity" name="scroll-margin-left" />

This example sets the scroll margin left to **1.5rem**. The element will have a spacing of **1.5rem** to its left when scrolling, ensuring that it is positioned away from the left edge of the viewport.

<Note icon="cursor_arrow">Try scrolling through the image container to see how it behaves.</Note>

```html live "sml-6" layout="/src/layouts/inline.astro"
<div class="o-h p-r">
  <div class="d-f g-6 o-x-s p-6 sst-x-m">
    <div class="fs-0 p-r pl-6 sml-6 ssa-s">
      <div class="bo-0 l-0 p-a  id="area"t-0 w-6"></div>
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=849" />
    </div>
    <div class="fs-0 p-r pl-6 sml-6 ssa-s">
      <div class="bo-0 l-0 p-a  id="area"t-0 w-6"></div>
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=866" />
    </div>
    <div class="fs-0 p-r pl-6 sml-6 ssa-s">
      <div class="bo-0 l-0 p-a  id="area"t-0 w-6"></div>
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=872" />
    </div>
    <div class="fs-0 p-r pl-6 sml-6 ssa-s">
      <div class="bo-0 l-0 p-a  id="area"t-0 w-6"></div>
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=875" />
    </div>
  </div>
</div>
```

## Scroll Margin Right

Sets the margin on the right side of an element, creating space between the element and its scrollable container.

<Class category="interactivity" name="scroll-margin-right" />

This example sets the scroll margin right to **1.5rem**. The element will have a spacing of **1.5rem** to its right when scrolling, ensuring that it is positioned away from the right edge of the viewport.

<Note icon="cursor_arrow">Try scrolling through the image container to see how it behaves.</Note>

```html live "smr-6" layout="/src/layouts/inline.astro"
<div class="o-h p-r">
  <div class="d-f g-6 o-x-s p-6 sst-x-m">
    <div class="fs-0 p-r pr-6 smr-6 ssa-s">
      <div class="bo-0 p-a r-0  id="area"t-0 w-6"></div>
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=849" />
    </div>
    <div class="fs-0 p-r pr-6 smr-6 ssa-s">
      <div class="bo-0 p-a r-0  id="area"t-0 w-6"></div>
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=866" />
    </div>
    <div class="fs-0 p-r pr-6 smr-6 ssa-s">
      <div class="bo-0 p-a r-0  id="area"t-0 w-6"></div>
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=872" />
    </div>
    <div class="fs-0 p-r pr-6 smr-6 ssa-s">
      <div class="bo-0 p-a r-0  id="area"t-0 w-6"></div>
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=875" />
    </div>
  </div>
</div>
```

## Scroll Margin Top

Sets the margin on the top side of an element, creating space between the element and its scrollable container.

<Class category="interactivity" name="scroll-margin-top" />

This example sets the scroll margin top to **1.5rem**. The element will have a spacing of **1.5rem** above it when scrolling, ensuring that it is positioned away from the top edge of the viewport.

<Note icon="cursor_arrow">Try scrolling through the image container to see how it behaves.</Note>

```html live "smt-6"
<div class="o-h p-r w-fc">
  <div class="d-f fd-c g-6 h-96 o-y-auto p-6 sst-y-m">
    <div class="fs-0 p-r pt-6 smt-6 ssa-s">
      <div class="h-6 l-0 p-a r-0  id="area"t-0"></div>
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=849" />
    </div>
    <div class="fs-0 p-r pt-6 smt-6 ssa-s">
      <div class="h-6 l-0 p-a r-0  id="area"t-0"></div>
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=866" />
    </div>
    <div class="fs-0 p-r pt-6 smt-6 ssa-s">
      <div class="h-6 l-0 p-a r-0  id="area"t-0"></div>
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=872" />
    </div>
    <div class="fs-0 p-r pt-6 smt-6 ssa-s">
      <div class="h-6 l-0 p-a r-0  id="area"t-0"></div>
      <img class="h-40 rad-1 w-80" src="https://picsum.photos/400/200?image=875" />
    </div>
  </div>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<BreakpointVariant class="sm-1" variant="sm-2" classPrefix="sm">
  ### Breakpoint variant
</BreakpointVariant>

<HoverVariant class="sm-1" variant="sm-2" classPrefix="sm">
  ### Hover variant
</HoverVariant>
