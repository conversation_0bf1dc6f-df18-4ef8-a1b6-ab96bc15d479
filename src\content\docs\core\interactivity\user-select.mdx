---
title: User Select
banner:
  content: N/A
description: Controls the ability of the user to select text in an element.
slug: docs/user-select
---

<Class category="interactivity" name="user-select" />

## All

This example sets the user select to **all**. The **us-a** utility allows the user to select all text within the element when clicked or dragged over.

<Note icon="cursor_text">Try selecting the sentence to see how the text highlighting works.</Note>

```html live "us-a" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-sm p-4 rad-1 ta-c tc-lead us-a">Sphinx of black quartz, judge my vow.</p>
</div>
```

## Auto

> Initial value

This example sets the user select to **auto**. The **us-auto** utility allows the browser to determine the default selection behavior based on the element's content.

<Note icon="cursor_text">Try selecting the sentence to see how the text highlighting works.</Note>

```html live "us-auto" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-sm p-4 rad-1 ta-c tc-lead us-auto">Sphinx of black quartz, judge my vow.</p>
</div>
```

## None

This example sets the user select to **none**. The **us-none** utility prevents the user from selecting any text within the element.

<Note icon="cursor_text">Try selecting the sentence to see how the text highlighting works.</Note>

```html live "us-none" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-sm p-4 rad-1 ta-c tc-lead tc-lead us-none">Sphinx of black quartz, judge my vow.</p>
</div>
```

## Text

This example sets the user select to **text**. The **us-t** utility allows the user to select text within the element, enabling text selection while preventing selection of other elements.

<Note icon="cursor_text">Try selecting the sentence to see how the text highlighting works.</Note>

```html live "us-t" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-sm p-4 rad-1 ta-c tc-lead us-t">Sphinx of black quartz, judge my vow.</p>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/foundations/responsive-design/) or other factors, such as [hover states](/docs/foundations/variants/).

<BreakpointVariant class="us-none" variant="us-auto" classPrefix="us">
  ### Breakpoint variant
</BreakpointVariant>

<HoverVariant class="us-none" variant="us-auto" classPrefix="us">
  ### Hover variant
</HoverVariant>
