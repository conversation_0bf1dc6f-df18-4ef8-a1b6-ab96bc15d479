---
title: Columns
banner:
  content: N/A
description: Controls how many columns are in an element.
slug: docs/columns
---

<Class category="positioning" name="columns" />

This example sets the columns to **4**. The **c-4** utility divides the content into **4** equal columns.

```html live "c-4" layout="/src/layouts/inline.astro"
<div class="c-4">
  <p class="bg-indigo rad-1 ta-c tc-white p-4">A</p>
  <p class="bg-indigo rad-1 ta-c tc-white p-4">B</p>
  <p class="bg-indigo rad-1 ta-c tc-white p-4">C</p>
  <p class="bg-indigo rad-1 ta-c tc-white p-4">D</p>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<BreakpointVariant class="c-1" variant="c-2" classPrefix="c">
  ### Breakpoint variant
</BreakpointVariant>

<HoverVariant class="c-1" variant="c-2" classPrefix="c">
  ### Hover variant
</HoverVariant>
