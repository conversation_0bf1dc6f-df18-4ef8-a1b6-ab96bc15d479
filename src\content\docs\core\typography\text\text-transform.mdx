---
title: Text Transform
banner:
  content: N/A
description: Controls the capitalization and case of text content.
slug: docs/text-transform
---

<Class category="text" name="text-transform" />

## Capitalize

This example sets the text transform to **capitalize**. The first letter of each word will be converted to uppercase.

```html live "tt-c" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg p-4 rad-1 ta-c tc-lead tt-c">Sphinx of black quartz, judge my vow.</p>
</div>
```

## Lowercase

This example sets the text transform to **lowercase**. All letters in the text will be converted to lowercase.

```html live "tt-l" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg p-4 rad-1 ta-c tc-lead tt-l">Sphinx of black quartz, judge my vow.</p>
</div>
```

## None

> Initial value

This example sets the text transform to **none**. The text will be displayed as it is, without any transformation applied.

```html live "tt-none" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg p-4 rad-1 ta-c tc-lead tt-none">Sphinx of black quartz, judge my vow.</p>
</div>
```

## Uppercase

This example sets the text transform to **uppercase**. All letters in the text will be converted to uppercase.

```html live "tt-u" layout="/src/layouts/inline.astro"
<div class="bg-indigo-1 p-4 rad-1">
  <p class="bg-white fs-lg p-4 rad-1 ta-c tc-lead tt-u">Sphinx of black quartz, judge my vow.</p>
</div>
```

## Conditional styles

Learn how to override existing utilities based on the user's [screen size](/docs/first-steps#breakpoint-modifiers) or other factors, such as [hover states](/docs/first-steps#hover-modifiers).

<MediaModifier class="tt-none" mediaModifier="tt-c" classPrefix="tt">
  ### Breakpoint modifier
</MediaModifier>

<HoverModifier class="tt-none" hoverModifier="tt-c" classPrefix="tt">
  ### Hover modifier
</HoverModifier>
