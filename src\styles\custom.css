/* Light mode */
:root[data-theme="light"] {
  --purple-hsl: 242, 85%, 65%;
  --sl-color-accent-high: #282b61;
  --sl-color-accent-low: #cfd5f6;
  --sl-color-accent: #413cb8;
  --sl-color-black: #ffffff;
  --sl-color-gray-1: #21243f;
  --sl-color-gray-2: #323652;
  --sl-color-gray-3: #515674;
  --sl-color-gray-4: #8389aa;
  --sl-color-gray-5: #bdc1d3;
  --sl-color-gray-6: #eaedfc;
  --sl-color-gray-7: #f4f6fd;
  --sl-color-gray-8: #fcfcfe;
  --sl-color-white: #151724;

  --hero-mesh: url("src/assets/mesh-light.png");
  --mesh-boxes: url("src/assets/boxes-dark.png");
  --framework-card: var(--sl-color-gray-8);
}

/* Dark mode */
:root[data-theme="dark"] {
  --overlay-blurple: hsla(var(--purple-hsl), 0.2);
  --purple-hsl: 242, 50%, 50%;
  --sl-color-accent-high: #bec6f2;
  --sl-color-accent-low: #1d2041;
  --sl-color-accent: #5456d2;
  --sl-color-black: #151724;
  --sl-color-gray-1: #eaedfc;
  --sl-color-gray-2: #c4c7d9;
  --sl-color-gray-3: #9ba2c4;
  --sl-color-gray-4: #515674;
  --sl-color-gray-5: #323652;
  --sl-color-gray-6: #21243f;
  --sl-color-white: #ffffff;

  --hero-mesh: url("src/assets/mesh-dark.png");
  --mesh-boxes: url("src/assets/boxes-dark.png");
  --framework-card: var(--sl-color-gray-6);
}

@media (min-width: 50rem) {
  div:has(> site-search) {
    justify-content: center;
  }
}

@media (min-width: 72rem) {
  div:has(> site-search) {
    max-width: calc(var(--sl-content-width));
  }
}

.container {
  background: var(--sl-color-black);
  border-bottom: none;
  border: 1px solid light-dark(var(--sl-color-gray-6), var(--ec-brdCol));
  overflow-x: auto;
}

.border-reset * {
  border: 0 solid;
}

.tc-accent {
  color: light-dark(var(--sl-color-accent), var(--sl-color-accent-high));
}

.sl-link-button {
  border-radius: 0.25rem !important;
}

.header {
  --sl-color-bg-nav: light-dark(var(--sl-color-black), var(--sl-color-gray-6));
}

body {
  font-family: "Outfit Variable", sans-serif;
}

h2 {
  font-size: 1.625rem !important;
}

h3 {
  font-size: 1.325rem !important;
}

h4 {
  font-size: 1.125rem !important;
}

img[src*="houston_omg"] {
  display: none;
}

pre {
  background-color: #21243f !important;
}

strong {
  font-weight: 600 !important;
}

table {
  width: 100%;
  display: inline-table !important;
}

/* code blocks */
#area {
  background-color: light-dark(var(--sl-color-gray-8), var(--sl-color-gray-6));

  background-image:
    repeating-linear-gradient(45deg, light-dark(#d3d5e3, #31365e) 25%, transparent 25%, transparent 75%, light-dark(#d3d5e3, #31365e) 75%, light-dark(#d3d5e3, #31365e)),
    repeating-linear-gradient(
      45deg,
      light-dark(#d3d5e3, #31365e) 25%,
      var(--sl-color-black) 25%,
      var(--sl-color-black) 75%,
      light-dark(#d3d5e3, #31365e) 75%,
      light-dark(#d3d5e3, #31365e)
    );
  background-position:
    0 0,
    8px 8px;
  background-size: 16px 16px;
}

#starlight__on-this-page {
  font-size: 1.125rem !important;
}
